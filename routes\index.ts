import { Router } from "express";
import AuthRoutes from "./auth/auth.Route";
import roleRoutes from "./roles/index";
import agentsRoutes from "./agents";
import locationRoutes from "./locations/index";
import languageRoutes from "./languages/index";
import documentRoutes from "./documents/index";
import servicesRoutes from "./services/index";
import nationalitiesRoutes from "./nationalities/index";
import commonRoutes from "./common";
import adminRoutes from "./admin/index";
import webhookRoutes from "./webhook/index";


const router: Router = Router();

router.use("/auth", AuthRoutes);
router.use("/role", roleRoutes);
router.use("/documents", documentRoutes);
router.use("/agent", agentsRoutes);
router.use("/location", locationRoutes);
router.use("/languages", languageRoutes);
router.use("/nationalities", nationalitiesRoutes);
router.use("/type", servicesRoutes);

router.use("/other", commonRoutes);

router.use("/admin",  adminRoutes);
router.use("/webhook",  webhookRoutes);


export default router;
