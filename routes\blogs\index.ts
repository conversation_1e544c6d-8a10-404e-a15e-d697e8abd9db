import express from "express";
import {
  blogCreation,
  getAllBlogs,
  getAllPublishedBlogs,
  getBlogDetails,
  updateBlog,
  deleteBlog,
  likeDislikeBlog,
  updateBlogStatus,
} from "../../controller/blogs";
 
import { storageData, uploadErrorHandler } from "../../utils/services/multer";
const upload = storageData("agentDetails");
const router = express.Router();

router.post("/create",upload.none(), blogCreation);
router.get("/all", getAllBlogs);
router.get("/published", getAllPublishedBlogs);
router.get("/get/:slug", getBlogDetails);
router.put("/update/:id",upload.none(), updateBlog);
router.delete("/delete/:id",upload.none(), deleteBlog);
router.post("/like/:id",upload.none(), likeDislikeBlog);
router.put("/status/:id",upload.none(), updateBlogStatus);

export default router;
