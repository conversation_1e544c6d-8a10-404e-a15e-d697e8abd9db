import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import { errorResponse, responseData } from "../../../utils/response";
import { AUTH } from "../../../utils/database/queries/auth";
import db from "../../../config/database";
import { REVIEWS } from "../../../utils/database/queries/reviews";

// Get all reviews for admin with pagination and filtering
export const getAllReviews = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const status = req.query.status as string;
      const search = req.query.search as string;
      const offset = (page - 1) * limit;

      let reviews;
      let totalCount;

      if (search) {
        // Search reviews
        reviews = await db.query(REVIEWS.SEARCH_REVIEWS, [
          `%${search}%`,
          limit,
          offset,
        ]);
        // Get count for search (simplified)
        totalCount = await db.query(
          `SELECT COUNT(*) as total 
           FROM agn.reviews r
           LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
           LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
           LEFT JOIN agn.agencies agn ON r."revieweeId" = agn."profileId"
           WHERE 
             LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE LOWER($1)
             OR LOWER(CONCAT(reviewee."firstName", ' ', reviewee."lastName")) LIKE LOWER($1)
             OR LOWER(agn.name) LIKE LOWER($1)`,
          [`%${search}%`]
        );
      } else if (status) {
        // Filter by status
        const statusId = parseInt(status);
        reviews = await db.query(REVIEWS.GET_REVIEWS_BY_STATUS, [
          statusId,
          limit,
          offset,
        ]);
        totalCount = await db.query(REVIEWS.GET_REVIEWS_COUNT_BY_STATUS, [statusId]);
      } else {
        // Get all reviews
        reviews = await db.query(REVIEWS.GET_ALL_REVIEWS, [limit, offset]);
        totalCount = await db.query(REVIEWS.GET_REVIEWS_COUNT);
      }

      const total = parseInt(totalCount.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return responseData(res, 200, "Reviews retrieved successfully", {
        reviews: reviews.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      });
    } catch (error) {
      console.error("Error getting reviews:", error);
      return errorResponse(res, "Failed to get reviews");
    }
  }
);

// Get single review by ID
export const getReviewById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const review = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);

      if (!review.rows.length) {
        return errorResponse(res, "Review not found");
      }

      return responseData(res, 200, "Review retrieved successfully", {
        review: review.rows[0],
      });
    } catch (error) {
      console.error("Error getting review:", error);
      return errorResponse(res, "Failed to get review");
    }
  }
);

// Update review status (approve/hide only)
export const updateReviewStatus = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { statusName, reason } = req.body;
      const adminId = req.user?.id;

      // Validation
      if (!statusName) {
        return errorResponse(res, "Status name is required");
      }

      // Only allow "Approved" or "Hidden" status
      if (!["Published", "Hidden"].includes(statusName)) {
        return errorResponse(res, "Only 'Approved' or 'Hidden' status is allowed");
      }

      // Validate status exists in database by name
      const statusResult = await db.query(`SELECT id, name FROM look.status WHERE name = $1`, [statusName]);
      if (!statusResult.rows.length) {
        return errorResponse(res, "Invalid status name. Please provide a valid status name from the database.");
      }

      const status = statusResult.rows[0].id;
      const statusNameFromDb = statusResult.rows[0].name;

      // Check if reason is provided for hidden status 
      if (statusNameFromDb.toLowerCase() === 'hidden' && (!reason || reason.trim().length === 0)) {
        return errorResponse(res, "Reason is required when hiding a review");
      }

      // Validate reason length if provided
      if (reason && reason.trim().length > 500) {
        return errorResponse(res, "Reason cannot exceed 500 characters");
      }

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      const currentReview = existingReview.rows[0];

      // Get admin's login ID
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      let result;
      
      // Use different query based on whether it's a hide action or not
      if (statusNameFromDb.toLowerCase() === 'hidden') {
        // Update review status with reason for hidden reviews
        result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS_WITH_REASON, [
          status,
          adminLogin.rows[0].id,
          id,
          reason.trim(),
        ]);
      } else {
        // Regular status update without reason
        result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS, [
          status,
          adminLogin.rows[0].id,
          id,
        ]);
      }

      // Log the action
      const statusText = statusNameFromDb.toLowerCase();
      const logMessage = statusNameFromDb.toLowerCase() === 'hidden' ? 
        `Status changed to ${statusText}. Reason: ${reason.trim()}` : 
        `Status changed to ${statusText}`;
      
      await db.query(REVIEWS.LOG_REVIEW_ACTION, [
        id,
        'status_updated',
        currentReview.statusId,
        status,
        logMessage,
        adminLogin.rows[0].id,
      ]);

      // Prepare response message
      const responseMessage = statusNameFromDb.toLowerCase() === 'hidden' ? 
        `Review hidden successfully. Reason: ${reason.trim()}` : 
        `Review ${statusText} successfully`;

      return responseData(res, 200, responseMessage, {
        review: result.rows[0],
        previousStatus: currentReview.statusId,
        newStatus: status,
        reason: statusNameFromDb.toLowerCase() === 'hidden' ? reason.trim() : null,
        adminAction: `Review ${statusText} by admin`,
      });
    } catch (error) {
      console.error("Error updating review status:", error);
      return errorResponse(res, "Failed to update review status");
    }
  }
);

// Delete review (hard delete)
export const deleteReview = asyncHandler(   
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const adminId = req.user?.id;

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      // Get admin's login ID for validation
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      // Hard delete review from database
      await db.query(`DELETE FROM agn.reviews WHERE id = $1`, [id]);

      return responseData(res, 200, "Review deleted permanently", {
        deletedReviewId: id,
      });
    } catch (error) {
      console.error("Error deleting review:", error);
      return errorResponse(res, "Failed to delete review");
    }
  }
);

// Get reviews statistics for dashboard
export const getReviewsStats = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Get total reviews count  
      const totalReviews = await db.query(
        `SELECT COUNT(*) as total FROM agn.reviews WHERE "statusId" != 4`
      );

      // Get pending reviews count
      const pendingReviews = await db.query(
        `SELECT COUNT(*) as total FROM agn.reviews WHERE "statusId" = 1`
      );

      // Get approved reviews count
      const approvedReviews = await db.query(
        `SELECT COUNT(*) as total FROM agn.reviews WHERE "statusId" = 2`
      );

      // Get rejected reviews count
      const rejectedReviews = await db.query(
        `SELECT COUNT(*) as total FROM agn.reviews WHERE "statusId" = 3`
      );

      // Get average rating
      const avgRating = await db.query(
        `SELECT ROUND(AVG(rating), 2) as average_rating 
         FROM agn.reviews 
         WHERE "statusId" = 2`
      );

      // Get recent reviews (last 5)
      const recentReviews = await db.query(
        `SELECT 
          r.id,
          r.rating,
          r."created_at",
          reviewer."firstName" AS "reviewerFirstName",
          reviewer."lastName" AS "reviewerLastName",
          reviewee."firstName" AS "revieweeFirstName",
          reviewee."lastName" AS "revieweeLastName",
          reviewee."accountType" AS "revieweeType",
          s.name AS "statusName"
        FROM agn.reviews r
        LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
        LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
        LEFT JOIN look.status s ON r."statusId" = s.id
        WHERE r."statusId" != 4
        ORDER BY r."created_at" DESC
        LIMIT 5`
      );

      return responseData(res, 200, "Reviews statistics retrieved successfully", {
        stats: {
          total: parseInt(totalReviews.rows[0].total),
          pending: parseInt(pendingReviews.rows[0].total),
          approved: parseInt(approvedReviews.rows[0].total),
          rejected: parseInt(rejectedReviews.rows[0].total),
          averageRating: parseFloat(avgRating.rows[0].average_rating) || 0,
        },
        recentReviews: recentReviews.rows,
      });
    } catch (error) {
      console.error("Error getting reviews stats:", error);
      return errorResponse(res, "Failed to get reviews statistics");
    }
  }
);

// Bulk update reviews status
export const bulkUpdateReviewsStatus = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { reviewIds, statusId } = req.body;
      const adminId = req.user?.id;

      // Validation
      if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
        return errorResponse(res, "Review IDs array is required");
      }

      // Validate status exists in database
      const bulkStatusResult = await db.query(`SELECT id, name FROM look.status WHERE id = $1`, [parseInt(statusId)]);
      if (!bulkStatusResult.rows.length) {
        return errorResponse(res, "Invalid status ID. Please provide a valid status ID from the database.");
      }

      // Get admin's login ID
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      // Update multiple reviews
      const placeholders = reviewIds.map((_, index) => `$${index + 3}`).join(',');
      const query = `
        UPDATE agn.reviews 
        SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP
        WHERE id IN (${placeholders})
        RETURNING id
      `;

      const result = await db.query(query, [
        statusId,
        adminLogin.rows[0].id,
        ...reviewIds,
      ]);

      // Get status name dynamically from database
      const bulkUpdateStatusResult = await db.query(`SELECT name FROM look.status WHERE id = $1`, [statusId]);
      const statusText = bulkUpdateStatusResult.rows.length ? bulkUpdateStatusResult.rows[0].name.toLowerCase() : 'unknown';

      return responseData(res, 200, `${result.rows.length} reviews updated to ${statusText}`, {
        updatedCount: result.rows.length,
        updatedIds: result.rows.map(row => row.id),
      });
    } catch (error) {
      console.error("Error bulk updating reviews:", error);
      return errorResponse(res, "Failed to bulk update reviews");
    }
  }
);

// Add note to review
export const addReviewNote = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { note } = req.body;
      const adminId = req.user?.id;

      // Validation
      if (!note || note.trim().length === 0) {
        return errorResponse(res, "Note is required");
      }

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      // Get admin's login ID
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      // Add note
      const result = await db.query(REVIEWS.ADD_REVIEW_NOTE, [
        id,
        note.trim(),
        adminLogin.rows[0].id,
      ]);

      return responseData(res, 201, "Note added successfully", {
        note: result.rows[0],
      });
    } catch (error) {
      console.error("Error adding review note:", error);
      return errorResponse(res, "Failed to add note");
    }
  }
);

// Get review notes
export const getReviewNotes = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      // Get notes
      const notes = await db.query(REVIEWS.GET_REVIEW_NOTES, [id]);

      return responseData(res, 200, "Review notes retrieved successfully", {
        notes: notes.rows,
      });
    } catch (error) {
      console.error("Error getting review notes:", error);
      return errorResponse(res, "Failed to get review notes");
    }
  }
);

// Get review history
export const getReviewHistory = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      // Get history
      const history = await db.query(REVIEWS.GET_REVIEW_HISTORY, [id]);

      return responseData(res, 200, "Review history retrieved successfully", {
        history: history.rows,
      });
    } catch (error) {
      console.error("Error getting review history:", error);
      return errorResponse(res, "Failed to get review history");
    }
  }
);

// Flag review for moderation
export const flagReview = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const adminId = req.user?.id;

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      const review = existingReview.rows[0];
      
      // Get flagged status ID from database  
      const flaggedStatus = await db.query(`SELECT id FROM look.status WHERE name = $1`, ["Flagged"]);
      
      if (!flaggedStatus.rows.length) {
        return errorResponse(res, "Flagged status not found in database");
      }

      const flaggedStatusId = flaggedStatus.rows[0].id;
      
      // Check if review is already flagged
      if (review.statusId === flaggedStatusId) {
        return errorResponse(res, "Review is already flagged");
      }

      // Get admin's login ID
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      // Update status to flagged using dynamic status ID
      const result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS, [
        flaggedStatusId, // Dynamic flagged status ID from database
        adminLogin.rows[0].id,
        id,
      ]);

      // Log the action
      await db.query(REVIEWS.LOG_REVIEW_ACTION, [
        id,
        'flagged',
        review.statusId,
        flaggedStatusId,
        reason || 'Flagged for moderation',
        adminLogin.rows[0].id,
      ]);

      return responseData(res, 200, "Review flagged successfully", {
        review: result.rows[0],
      });
    } catch (error) {
      console.error("Error flagging review:", error);
      return errorResponse(res, "Failed to flag review");
    }
  }
);

// Hide review (separate from delete)
export const hideReview = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const adminId = req.user?.id;

       if (!reason) {
        return errorResponse(res, "Provide a reason for hiding the review");
      }

      // Check if review exists
      
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      const review = existingReview.rows[0];
      
      // Get hidden status ID from database
      const hiddenStatus = await db.query(`SELECT id FROM look.status WHERE name = $1`, ["Hidden"]);
      
      if (!hiddenStatus.rows.length) {
        return errorResponse(res, "Hidden status not found in database");
      }

      const hiddenStatusId = hiddenStatus.rows[0].id;
      
      // Check if review is already hidden
      if (review.statusId === hiddenStatusId) {
        return errorResponse(res, "Review is already hidden");
      }

      // Get admin's login ID
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      // Update status to hidden using dynamic status ID
      const result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS, [
        hiddenStatusId, // Dynamic hidden status ID from database
        adminLogin.rows[0].id,
        id,
      ]);

      // Log the action
      await db.query(REVIEWS.LOG_REVIEW_ACTION, [
        id,
        'hidden',
        review.statusId,
        hiddenStatusId,
        reason || 'Hidden by admin',
        adminLogin.rows[0].id,
      ]);

      return responseData(res, 200, "Review hidden successfully", {
        review: result.rows[0],
      });
    } catch (error) {
      console.error("Error hiding review:", error);
      return errorResponse(res, "Failed to hide review");
    }
  }
);

// Restore hidden review
export const restoreReview = asyncHandler(

  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { newStatus } = req.body;
      const adminId = req.user?.id;

      // Check if review exists
      const existingReview = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
      if (!existingReview.rows.length) {
        return errorResponse(res, "Review not found");
      }

      const review = existingReview.rows[0];
      
      // Get hidden status ID from database to check if review is hidden
      const hiddenStatus = await db.query(`SELECT id FROM look.status WHERE name = $1`, ["Hidden"]);
      
      if (!hiddenStatus.rows.length) {
        return errorResponse(res, "Hidden status not found in database");
      }

      const hiddenStatusId = hiddenStatus.rows[0].id;
      
      // Check if review is hidden
      if (review.statusId !== hiddenStatusId) {
        return errorResponse(res, "Review is not hidden");
      }

      // Get the previous status from review history (status before it was hidden)
      let targetStatus = newStatus; // Use provided newStatus if available
      
      if (!targetStatus) {
        // Query history to find the status before it was hidden
        const historyQuery = `
          SELECT "previousStatus" 
          FROM agn.review_history 
          WHERE "reviewId" = $1 AND "newStatus" = $2 AND action IN ('hidden', 'status_updated')
          ORDER BY "created_at" DESC 
          LIMIT 1
        `;
        
        const historyResult = await db.query(historyQuery, [id, hiddenStatusId]);
        
        if (historyResult.rows.length > 0) {
          targetStatus = historyResult.rows[0].previousStatus;
        } else {
          // Fallback to pending if no history found (since new reviews start as pending)
          const pendingStatus = await db.query(`SELECT id FROM look.status WHERE name = $1`, ["Pending"]);
          
          if (!pendingStatus.rows.length) {
            return errorResponse(res, "Pending status not found in database");
          }
          
          targetStatus = pendingStatus.rows[0].id;
        }
      }

      // Get admin's login ID
      const adminLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [adminId]
      );

      if (!adminLogin.rows.length) {
        return errorResponse(res, "Invalid admin");
      }

      // Get status name for logging
      const restoreStatusResult = await db.query(`SELECT name FROM look.status WHERE id = $1`, [targetStatus]);
      const statusText = restoreStatusResult.rows.length ? restoreStatusResult.rows[0].name.toLowerCase() : 'unknown';

      // Update status
      const result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS, [
        targetStatus,
        adminLogin.rows[0].id,
        id,
      ]);

      // Log the action
      await db.query(REVIEWS.LOG_REVIEW_ACTION, [
        id,
        'restored',
        review.statusId,
        targetStatus,
        `Restored to ${statusText}`,
        adminLogin.rows[0].id,
      ]);

      return responseData(res, 200, `Review restored to ${statusText}`, {
        review: result.rows[0],
      });
    } catch (error) {
      console.error("Error restoring review:", error);
      return errorResponse(res, "Failed to restore review");
    }
  }
);
