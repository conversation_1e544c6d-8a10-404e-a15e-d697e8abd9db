import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    await knex.schema.withSchema("list").createTable("countries", (table) => {
        table.increments("id").primary();
        table.text("name").notNullable();
        table.text("nationality").notNullable();
        table.integer("statusId").notNullable().defaultTo(1);
        table.timestamp("createdOn", { useTz: true }).defaultTo(knex.fn.now());
        table.timestamp("modifiedOn", { useTz: true }).nullable();
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.withSchema("list").dropTableIfExists("countries");
}