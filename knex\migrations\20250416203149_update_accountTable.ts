import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("prf").alterTable("accounts", (table) => {
    table.string("name").nullable().alter();
    table.string("code").nullable().alter();
    table.integer("createdBy").nullable().alter();
    table.boolean("verified").defaultTo(false).alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema("prf").alterTable("accounts", (table) => {
    table.string("name").notNullable().alter();
    table.string("code").notNullable().alter();
    table.integer("createdBy").notNullable().alter();
    table.boolean("verified").defaultTo(false).notNullable().alter();
  });
}