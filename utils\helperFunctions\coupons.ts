import Stripe from "stripe";
import stripeKeys from "../../config/stripeConfigs";

const stripe = new Stripe(stripeKeys.stripe.secretKey);

interface UpdateCouponParams {
  id: string;
  name?: string;
  description?: string;
}

interface CreateCouponParams {
  percent_off?: number; // e.g. 10 for 10%
  amount_off?: number; // in cents e.g. 1000 = $10
  currency?: string; // required for amount_off
  name?: string;
  id?: string; // optional coupon ID (your own identifier)
}

export async function createStripeCoupon(params: CreateCouponParams) {
  try {
    const coupon = await stripe.coupons.create({
      percent_off: params.percent_off,
      amount_off: params.amount_off,
      currency: params.currency,
      name: params.name,
      id: params.id,
    });

    return { success: true, data: coupon };
  } catch (err: any) {
    console.error("Stripe coupon creation error:", err);
    return { success: false, error: err.message };
  }
}

// Mark old coupon as inactive (via metadata)
export async function deactivateStripeCoupon(couponId: string) {
  try {
    const coupon = await stripe.coupons.update(couponId, {
      metadata: { status: "inactive" },
    });

    return { success: true, data: coupon };
  } catch (err: any) {
    console.error("Error deactivating coupon:", err);
    return { success: false, error: err.message };
  }
}

export async function updateStripeCouponMetadata(params: UpdateCouponParams) {
  try {
    const updated = await stripe.coupons.update(params.id, {
      name: params.name,
      metadata: {
        ...(params.description ? { description: params.description } : {}),
      },
    });

    return { success: true, data: updated };
  } catch (err: any) {
    console.error("Stripe coupon update error:", err);
    return { success: false, error: err.message };
  }
}
