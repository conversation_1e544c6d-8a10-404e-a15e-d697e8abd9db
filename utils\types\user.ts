export type User = {
  id: number;
  userName?: string;
  fullName?: string;
  phoneNumber: string;
  email: string;
  password: string;
  role: string;
  isVerified: boolean;
  isActive: string;
  status: string;
  isSubscribed: boolean;
  created_at: Date;
  updated_at: Date;
};

export type UserData = {
  userName: string | undefined,
  firstName: string | undefined,
  lastName: string | undefined,
  phoneNumber: string | undefined,
  profileImage?: string | null,
  id?: number,
}

export type UserProfile = {
  userName: string | undefined,
  firstName: string | undefined,
  lastName: string | undefined,
  phoneNumber: string | undefined,
  profileImage?: string | null,
  designation?: string | null;
  shortDescription?: string | null;
  description?: string | null;
  specialization?: string | null;
  experience?: number | null;
  languages?: string | null;
  industry?: string | null;
  accountType?: string | null;
  association: boolean;
  certified: boolean;
  issuedBy?: string | null;
  certificateNumber?: string | null;
  expiryDate?: Date | null;
  contactNumber?: string | null;
  whatsappContact?: string | null;
  contactEmail?: string | null;
  cardHolderName?: string | null;
  cardType?: string | null;
  cardNumber?: bigint | null;
};

export type UpdateUserProfile = {
  firstName: string | undefined,
  lastName: string | undefined,
  phone: string | undefined,
  designation?: string | null;
  shortDescription?: string | null;
  description?: string | null;
  specialization?: string | null;
  experience?: number | null;
  languages?: string | null;
  industry?: string | null;
  accountType?: string | null;
  association: boolean;
  certified: boolean;
  issuedBy?: string | null;
  certificateNumber?: string | null;
  expiryDate?: Date | null;
  contactNumber?: string | null;
  whatsappContact?: string | null;
  contactEmail?: string | null;
  cardHolderName?: string | null;
  cardType?: string | null;
  cardNumber?: bigint | null;
  profileImage?: string | null;
  id?: number | null
};

