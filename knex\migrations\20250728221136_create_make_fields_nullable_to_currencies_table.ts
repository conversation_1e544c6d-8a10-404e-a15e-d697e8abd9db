import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("list.currency", (table) => {
    table.integer("statusId").nullable().alter();
    table.decimal("rate").nullable().alter();
    table.unique(["code"]);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("list.currency", (table) => {
    table.integer("statusId").notNullable().defaultTo(1).alter();
    table.decimal("rate").notNullable().alter();
    table.dropUnique(["code"]);
  });
}
