import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

export const sendPasswordResetEmail = async (
    email: string,
    fullName: string,
    newPassword: string
): Promise<void> => {
    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string,
        to: email,
        subject: "Your Password Has Been Reset for FindAnyAgent",
        html: await passwordResetHTMLTemplate(fullName, newPassword),
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log(`Password reset email sent successfully to ${email}`);
    } catch (error) {
        console.error("Error sending password reset email:", error);
        // Note: We don't send a direct error response to the client here,
        // as this function is called from within another controller function
        // that handles the overall API response.
    }
};

const passwordResetHTMLTemplate = async (
    fullName: string,
    newPassword: string
): Promise<string> => {
    return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            /* margin: 40px auto; */
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            /* text-align: center; */
        }
        .header {
            font-size: 22px;
            font-weight: bold;
            color: #5e9b6d;
            margin-bottom: 20px;
        }
        .content {
            font-size: 16px;
            line-height: 1.6;
        }
        .password-box {
            background-color: #eefaf1;
            border: 1px solid #5e9b6d;
            padding: 10px 20px;
            margin: 20px auto;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            display: inline-block;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Your Password Has Been Reset</div>
        <div class="content">
            <p>Dear ${fullName},</p>
            <p>Your password for FindAnyAgent has been successfully reset.</p>
            <p>Your new temporary password is:</p>
            <div class="password-box">${newPassword}</div>
            <p>Please log in with this new password and change it immediately for security reasons.</p>
            <!-- <p>You can log in here: <a href="https://dev-faa.findanyagent.ae/?showLogin=true">Login to FindAnyAgent</a></p> -->
            <p>If you did not request this password reset, please contact our support team immediately.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>FindAnyAgent Team</strong></p>
        </div>
    </div>
</body>
</html>`;
}; 