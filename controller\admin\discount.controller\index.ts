import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import db from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import {
  createStripeCoupon,
  deactivateStripeCoupon,
  updateStripeCouponMetadata,
} from "../../../utils/helperFunctions/coupons";
import { TABLE } from "../../../utils/database/table";

// -------------------- GET ALL DISCOUNTS --------------------
export const getFilteredDiscounts = asyncHandler(
  async (req: Request, res: Response) => {
    const { page = 1, pageSize = 10, search = "", status = "" } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const conditions: string[] = [];
    const values: any[] = [];

    try {
      // ─── Filter by search ───
      if (search) {
        conditions.push(`(LOWER(d.name) LIKE $${values.length + 1})`);
        values.push(`%${String(search).toLowerCase()}%`);
      }

      // ─── Filter by status ───
      if (status) {
        const statusName = String(status).trim();
        const { rows } = await db.query(
          `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
          [statusName]
        );
        if (rows.length === 0) {
          return error(res, 400, `Status '${statusName}' not found.`);
        }

        conditions.push(`d."statusId" = $${values.length + 1}`);
        values.push(Number(rows[0].id));
      }

      const whereClause = conditions.length
        ? `WHERE ${conditions.join(" AND ")}`
        : "";

      // ─── Total count query ───
      const countQuery = `
        SELECT COUNT(*) AS total
        FROM web.discounts d
        ${whereClause}
        `;
      const countResult = await db.query(countQuery, values);
      const total = Number(countResult.rows[0].total);

      // ─── Main data query ───
      const dataQuery = `
        SELECT 
            d.*, 
            s.name AS status_name
        FROM web.discounts d
        JOIN look.status s ON d."statusId" = s.id
        ${whereClause}
        ORDER BY d.id DESC
        LIMIT $${values.length + 1} OFFSET $${values.length + 2}
        `;
      const result = await db.query(dataQuery, [
        ...values,
        Number(pageSize),
        offset,
      ]);

      return responseData(res, 200, "Discounts fetched", {
        discounts: result.rows,
        pagination: {
          total,
          currentPage: Number(page),
          pageSize: Number(pageSize),
          totalPages: Math.ceil(total / Number(pageSize)),
        },
      });
    } catch (err) {
      console.error("Get all discounts failed:", err);
      return error(res, 500, "Internal server error");
    }
  }
);

// -------------------- GET ALL DISCOUNTS --------------------
export const getAllDiscounts = asyncHandler(
  async (req: Request, res: Response) => {
    const { status = "" } = req.query;
    const conditions: string[] = [];
    const values: any[] = [];

    try {
      // Only apply filter if status is explicitly "active"
      if (String(status).toLowerCase() === "active") {
        const { rows } = await db.query(
          `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = 'active' LIMIT 1`
        );

        if (rows.length === 0) {
          return error(res, 400, `Status 'active' not found.`);
        }

        conditions.push(`d."statusId" = $1`);
        values.push(Number(rows[0].id));
      }

      const whereClause = conditions.length
        ? `WHERE ${conditions.join(" AND ")}`
        : "";

      const query = `
        SELECT 
            d.*, 
            s.name AS status_name
        FROM web.discounts d
        JOIN look.status s ON d."statusId" = s.id
        ${whereClause}
        ORDER BY d.id DESC
      `;

      const result = await db.query(query, values);

      return responseData(res, 200, "Discounts fetched", result.rows);
    } catch (err) {
      console.error("Fetching discounts failed:", err);
      return error(res, 500, "Internal server error");
    }
  }
);

// -------------------- GET DISCOUNT BY ID --------------------
export const getDiscountById = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Discount ID is required.");

    try {
      const query = `
      SELECT 
        d.*, 
        s.name AS status_name
      FROM web.discounts d
      JOIN look.status s ON d."statusId" = s.id
      WHERE d.id = $1
    `;
      const { rows } = await db.query(query, [id]);
      if (rows.length === 0) return error(res, 404, "Discount not found.");
      return responseData(res, 200, "Discount fetched", rows[0]);
    } catch (err) {
      console.error("Get discount by ID failed:", err);
      return error(res, 500, "Internal server error");
    }
  }
);

// -------------------- CREATE DISCOUNT --------------------
export const createDiscount = asyncHandler(
  async (req: Request, res: Response) => {
    const { name, description, type, value, code } = req.body;

    if (!name || !type || !value) {
      return error(res, 400, "Missing required fields.");
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const { rows: duplicates } = await client.query(
        `SELECT id FROM web.discounts WHERE LOWER(name) = LOWER($1) AND type = $2 AND value = $3 LIMIT 1`,
        [name, type, value]
      );
      
      if (duplicates.length > 0) {
        await client.query("ROLLBACK");
        return error(
          res,
          400,
          "A discount with the same name, type, and value already exists."
        );
      }

      const statusName = "Active";

      const { rows: statusRows } = await client.query(
        `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
        [statusName]
      );

      if (statusRows.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, `Status '${statusName}' not found.`);
      }

      const statusId = statusRows[0].id;

      const couponId = `discount-${Date.now()}`;
      const stripeResponse = await createStripeCoupon({
        id: couponId,
        name,
        currency: "aed",
        percent_off: type === "percentage" ? Number(value) : undefined,
        amount_off:
          type === "fixed" ? Math.round(Number(value) * 100) : undefined,
      });

      if (!stripeResponse.success) {
        await client.query("ROLLBACK");
        return error(res, 400, `Stripe error: ${stripeResponse.error}`);
      }

      const stripeDiscountId = stripeResponse.data?.id;

      const insertQuery = `
        INSERT INTO web.discounts (name, description, type, value, code, "stripeDiscountId", "statusId")
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *;
      `;

      const { rows } = await client.query(insertQuery, [
        name,
        description || null,
        type,
        value,
        code || null,
        stripeDiscountId,
        statusId,
      ]);

      await client.query("COMMIT");
      return responseData(res, 201, "Discount created", rows[0]);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Create discount failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- UPDATE DISCOUNT --------------------
export const updateDiscount = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { name, description, type, value, code } = req.body;

    if (!id || !name || !type || !value) {
      return error(res, 400, "Missing required fields.");
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Fetch current discount
      const { rows: existingRows } = await client.query(
        `SELECT * FROM web.discounts WHERE id = $1`,
        [id]
      );

      if (existingRows.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Discount not found.");
      }

      const current = existingRows[0];
      const hasValueOrTypeChanged =
        current.type !== type || Number(current.value) !== Number(value);

      if (!hasValueOrTypeChanged) {
        // Only update name/description in Stripe
        if (current.stripeDiscountId) {
          const updateRes = await updateStripeCouponMetadata({
            id: current.stripeDiscountId,
            name,
            description,
          });

          if (!updateRes.success) {
            await client.query("ROLLBACK");
            return error(res, 400, `Stripe update failed: ${updateRes.error}`);
          }
        }

        // Update local DB with updated name/description
        const updateQuery = `
          UPDATE web.discounts
          SET name = $1, description = $2, code = $3, updated_at = NOW()
          WHERE id = $4
          RETURNING *;
        `;

        const { rows: updated } = await client.query(updateQuery, [
          name,
          description || null,
          code || null,
          id,
        ]);

        await client.query("COMMIT");
        return responseData(res, 200, "Discount updated", updated[0]);
      }

      // Stripe: deactivate current coupon
      if (current.stripeDiscountId) {
        await deactivateStripeCoupon(current.stripeDiscountId);
      }

      // Set current discount to Inactive
      const { rows: inactiveStatus } = await client.query(
        `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = 'inactive' LIMIT 1`
      );

      if (!inactiveStatus.length) {
        await client.query("ROLLBACK");
        return error(res, 400, "Inactivated status not found.");
      }

      const deactivatedStatusId = inactiveStatus[0].id;

      await client.query(
        `UPDATE web.discounts SET "statusId" = $1, updated_at = NOW() WHERE id = $2`,
        [deactivatedStatusId, id]
      );

      // Create new Stripe coupon
      const couponId = `discount-${Date.now()}`;
      const stripeRes = await createStripeCoupon({
        id: couponId,
        name,
        currency: "aed",
        percent_off: type === "percentage" ? Number(value) : undefined,
        amount_off:
          type === "fixed" ? Math.round(Number(value) * 100) : undefined,
      });

      if (!stripeRes.success) {
        await client.query("ROLLBACK");
        return error(res, 400, `Stripe error: ${stripeRes.error}`);
      }

      // Get "Active" statusId
      const { rows: activeStatus } = await client.query(
        `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = 'active' LIMIT 1`
      );
      const statusId = activeStatus[0]?.id;
      if (!statusId) {
        await client.query("ROLLBACK");
        return error(res, 400, "Active status not found.");
      }

      // Create new discount record
      const insertQuery = `
        INSERT INTO web.discounts (name, description, type, value, code, "stripeDiscountId", "statusId")
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *;
      `;

      const { rows: newDiscount } = await client.query(insertQuery, [
        name,
        description || null,
        type,
        value,
        code || null,
        stripeRes.data?.id || null,
        statusId,
      ]);

      await client.query("COMMIT");
      return responseData(
        res,
        200,
        "Discount updated with new Stripe coupon",
        newDiscount[0]
      );
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Update discount failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- DELETE DISCOUNT --------------------
export const deleteDiscount = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Discount ID is required.");

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const { rows: existing } = await client.query(
        `SELECT * FROM web.discounts WHERE id = $1`,
        [id]
      );
      if (existing.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Discount not found.");
      }

      if (existing[0].stripeDiscountId) {
        await deactivateStripeCoupon(existing[0].stripeDiscountId);
      }

      await client.query(`DELETE FROM web.discounts WHERE id = $1`, [id]);

      await client.query("COMMIT");
      return response(res, 200, "Discount deleted successfully.");
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Delete discount failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- UPDATE DISCOUNT STATUS --------------------
export const updateDiscountStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { statusId } = req.body;

    if (!id || !statusId) {
      return error(res, 400, "Discount ID and statusId are required.");
    }

    try {
      const { rows } = await db.query(
        `UPDATE web.discounts SET "statusId" = $1, updated_at = NOW() WHERE id = $2 RETURNING *`,
        [statusId, id]
      );

      if (rows.length === 0) return error(res, 404, "Discount not found.");
      return responseData(res, 200, "Status updated", rows[0]);
    } catch (err) {
      console.error("Update discount status failed:", err);
      return error(res, 500, "Internal server error");
    }
  }
);
