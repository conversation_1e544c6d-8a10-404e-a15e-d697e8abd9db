import jwt from "jsonwebtoken";
import dotenv from "dotenv";

dotenv.config();

export const getSignedJwt = (id: string, email: string) => {
  console.log(id);

  return jwt.sign({ id, email }, process.env.JWT_SECRET ?? "", {
    expiresIn: "3d",
  });
};

export const createResetToken = (id: { email: string }, time: string) =>
  jwt.sign(id, process.env.JWT_SECRET ?? "", {
    expiresIn: "10m",
  });

export const verifyToken = (token: any) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!);
  } catch (error) {
    throw new Error("Invalid or expired token");
  }
};

export const getExpirationTime = (): number => {
  const expiration = process.env.JWT_EXPIRE || "1h";
  const unit = expiration.slice(-1);
  const value = parseInt(expiration.slice(0, -1), 10);

  let multiplier = 3600000;

  switch (unit) {
    case "d":
      multiplier = 86400000;
      break;
    case "h":
      multiplier = 3600000;
      break;
    case "m":
      multiplier = 60000;
      break;
    case "s":
      multiplier = 1000;
      break;
    default:
      console.warn("Invalid session expiration format, defaulting to 1 hour.");
  }

  return value * multiplier;
};
