import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
    await knex.schema.createTable('prf.reasons', function (table) {
        table.increments('id').primary();
        table.integer('profileId').unsigned().notNullable()
            .references('id')
            .inTable('prf.profile')
            .onDelete('CASCADE');
        table.text('reason').notNullable();
        table.integer('createdBy').unsigned().notNullable()
            .references('id')
            .inTable('prf.profile')
            .onDelete('CASCADE');
        table.timestamps(true, true);
    });
}


export async function down(knex: Knex): Promise<void> {
    await knex.schema.dropTableIfExists('prf.reasons');
}

