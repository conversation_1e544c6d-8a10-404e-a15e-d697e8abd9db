import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
    CREATE OR REPLACE FUNCTION look.sp_packagefeaturevalues (
  p_fnid integer,
  p_packagefeatureid integer DEFAULT NULL::integer,
  p_packagetypeid integer DEFAULT NULL::integer,
  p_featureid integer DEFAULT NULL::integer,
  p_featurevalue text DEFAULT NULL::text
) RETURNS jsonb LANGUAGE plpgsql AS $function$
DECLARE
    v_data JSONB;
    v_title TEXT;
    v_result INT;
    v_type TEXT;
    v_message TEXT;
BEGIN
    IF p_fnid = 0 THEN
        SELECT to_jsonb(t) INTO v_data FROM look.packagefeaturevalues t WHERE t."packageFeatureId" = p_packagefeatureid;
        v_title := 'Package Feature Values - Single Record';
        v_result := 1;
        v_type := 'success';
        v_message := 'Record selected successfully.';
    ELSIF p_fnid = 1 THEN
        SELECT jsonb_agg(to_jsonb(t)) INTO v_data FROM look.packagefeaturevalues t 
        WHERE (p_packagefeatureid IS NULL OR t."packageFeatureId" = p_packagefeatureid)
        AND (p_packagetypeid IS NULL OR t."packageTypeId" = p_packagetypeid)
        AND (p_featureid IS NULL OR t."featureId" = p_featureid);
        v_title := 'Package Feature Values - List Records';
        v_result := 1;
        v_type := 'success';
        v_message := 'Records selected successfully.';
    ELSIF p_fnid = 2 THEN
        IF p_packagefeatureid IS NULL THEN
            INSERT INTO look.packagefeaturevalues (
                "packageTypeId", "featureId", "featureValue", "created_at"
            ) VALUES (
                p_packagetypeid, p_featureid, p_featurevalue, NOW()
            ) RETURNING to_jsonb(look.packagefeaturevalues.*) INTO v_data;
            v_title := 'Package Feature Values - Insert';
            v_result := 1;
            v_type := 'success';
            v_message := 'Record inserted successfully.';
        ELSE
            UPDATE look.packagefeaturevalues
            SET
                "packageTypeId" = p_packagetypeid,
                "featureId" = p_featureid,
                "featureValue" = p_featurevalue,
                "updated_at" = NOW()
            WHERE "packageFeatureId" = p_packagefeatureid
            RETURNING to_jsonb(look.packagefeaturevalues.*) INTO v_data;
            v_title := 'Package Feature Values - Update';
            v_result := 2;
            v_type := 'success';
            v_message := 'Record updated successfully.';
        END IF;
    ELSIF p_fnid = 3 THEN
        DELETE FROM look.packagefeaturevalues WHERE "packageFeatureId" = p_packagefeatureid RETURNING to_jsonb(look.packagefeaturevalues.*) INTO v_data;
        v_title := 'Package Feature Values - Delete';
        v_result := 3;
        v_type := 'success';
        v_message := 'Record deleted successfully.';
    ELSE
        v_title := 'Package Feature Values - Error';
        v_result := -1;
        v_type := 'error';
        v_message := 'Invalid Function ID.';
        v_data := NULL;
    END IF;
    RETURN jsonb_build_object(
        'title', v_title,
        'result', v_result,
        'type', v_type,
        'message', v_message,
        'data', COALESCE(v_data, 'null'::jsonb)
    );
END;
$function$;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`
    DROP FUNCTION IF EXISTS look.sp_packagefeaturevalues (
      integer, integer, integer, integer, text
    );
  `);
}
