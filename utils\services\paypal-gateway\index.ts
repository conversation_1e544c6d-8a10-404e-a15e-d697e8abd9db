import payPalKeys from "../../../config/payPalConfigs";
// ───────── paypal.ts ─────────
type PayPalSuccess<T = unknown> = { id: string; raw: T };

export const getAccessToken = async () => {
  const auth = Buffer.from(
    `${payPalKeys.publicKey}:${payPalKeys.secretKey}`
  ).toString("base64");

  try {
    const response = await fetch(
      "https://api-m.sandbox.paypal.com/v1/oauth2/token",
      {
        method: "POST",
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "grant_type=client_credentials",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error("Error getting PayPal access token:", error);
    throw error;
  }
};

/**
 * Create a PayPal Product and return the full response.
 * Throws if the HTTP status is not OK.
 */
export const createPayPalProduct = async (
  name: string,
  description: string
): Promise<PayPalSuccess> => {
  const token = await getAccessToken();

  const res = await fetch(
    "https://api.sandbox.paypal.com/v1/catalogs/products",
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name,
        description,
        type: "SERVICE",
        category: "ONLINE_SERVICES",
      }),
    }
  );

  const body = await res.json();
  if (!res.ok) {
    console.error("PayPal product error", body);
    throw new Error(
      body?.message || `PayPal product creation failed (${res.status})`
    );
  }

  return { id: body.id, raw: body };
};

/**
 * Create a PayPal Plan and return the full response.
 * Throws if the HTTP status is not OK.
 */
export const createPayPalPlan = async ({
  productId,
  planName,
  planPrice,
  description,
  planCurrency = "AED",
}: {
  productId: string;
  planName: string;
  planPrice: number;
  description?: string;
  planCurrency?: string;
}): Promise<PayPalSuccess> => {
  const token = await getAccessToken();

  const payload = {
    name: planName,
    description: description ?? `Subscription for ${planName}`,
    product_id: productId,
    status: "ACTIVE",
    billing_cycles: [
      {
        frequency: { interval_unit: "MONTH", interval_count: 1 },
        tenure_type: "REGULAR",
        sequence: 1,
        total_cycles: 12,
        pricing_scheme: {
          fixed_price: {
            value: planPrice.toFixed(2),
            currency_code: planCurrency,
          },
        },
      },
    ],
    payment_preferences: {
      auto_bill_outstanding: true,
      setup_fee: { value: "0", currency_code: planCurrency },
      setup_fee_failure_action: "CONTINUE",
      payment_failure_threshold: 3,
    },
  };

  const res = await fetch("https://api-m.sandbox.paypal.com/v1/billing/plans", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });

  const body = await res.json();
  if (!res.ok) {
    console.error("PayPal plan error", body);
    throw new Error(
      body?.message || `PayPal plan creation failed (${res.status})`
    );
  }

  return { id: body.id, raw: body };
};

export async function deactivatePayPalPlan(planId: string) {
  const token = await getAccessToken();
  await fetch(`https://api-m.sandbox.paypal.com/v1/billing/plans/${planId}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify([
      { op: "replace", path: "/status", value: "INACTIVE" },
    ]),
  }).catch(() => {
    /* ignore 404 or other errors */
  });
}

export async function deactivatePayPalProduct(productId: string) {
  const token = await getAccessToken();
  await fetch(
    `https://api-m.sandbox.paypal.com/v1/catalogs/products/${productId}`,
    {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify([
        { op: "replace", path: "/status", value: "INACTIVE" },
      ]),
    }
  ).catch(() => {
    /* ignore */
  });
}
