import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("list.subscription", (table) => {
    table
      .integer("profileId")
      .notNullable()
      .references("id")
      .inTable("prf.profile")
      .onDelete("CASCADE");

    table
      .integer("packageTypeId")
      .notNullable()
      .references("id")
      .inTable("look.packagetype")
      .onDelete("CASCADE");

    table.timestamp("startDate").notNullable().defaultTo(knex.fn.now());
    table.timestamp("endDate").nullable();
    table.timestamp("renewalDate").nullable();

    table.decimal("price", 12, 2).notNullable().defaultTo(0.0);
    table.string("currency", 10).notNullable().defaultTo("AED");

    table
      .integer("paymentStatusId")
      .notNullable()
      .references("id")
      .inTable("look.status")
      .onDelete("CASCADE");

    table.text("package").nullable().alter();
    table.integer("typeId").nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("list.subscription", (table) => {
    table.dropColumn("profileId");
    table.dropColumn("packageTypeId");
    table.dropColumn("startDate");
    table.dropColumn("endDate");
    table.dropColumn("renewalDate");
    table.dropColumn("price");
    table.dropColumn("currency");
    table.dropColumn("paymentStatusId");
    table.text("package").notNullable().alter();
    table.integer("typeId").notNullable().alter();
  });
}
