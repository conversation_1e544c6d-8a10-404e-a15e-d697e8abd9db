import nodemailer from "nodemailer";
import transporter from ".";

export const sendExpiredEmail = async (
    email: string,
    username: string,
    documentType: string
): Promise<void> => {
    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string,
        to: email,
        subject: `${documentType} Expired Today - FindAnyAgent`,
        html: await expiredEmailHTMLTemplate(username, documentType),
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log(`Expired email sent to ${email} for ${documentType}`);
    } catch (error) {
        console.error("Error sending expired document email:", error);
    }
};

const expiredEmailHTMLTemplate = async (
    username: string,
    documentType: string
): Promise<string> => {
    return `<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document Expired</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f9f9f9; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 40px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
    .header { font-size: 22px; color: #dc2626; font-weight: bold; }
    .content { font-size: 16px; margin-top: 20px; }
    .highlight { font-weight: bold; color: #b91c1c; }
    .footer { margin-top: 30px; font-size: 14px; color: #777; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">Your ${documentType} has expired</div>
    <div class="content">
      <p>Hi ${username},</p>
      <p>We wanted to inform you that your <span class="highlight">${documentType}</span> has <span class="highlight">expired today</span>.</p>
      <p>Please update or renew the document at your earliest convenience to stay compliant.</p>
    </div>
    <div class="footer">
      <p>Best regards,</p>
      <p><strong>FindAnyAgent Team</strong></p>
    </div>
  </div>
</body>
</html>`;
};
