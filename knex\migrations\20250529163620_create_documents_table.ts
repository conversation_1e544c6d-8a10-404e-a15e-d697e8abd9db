import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.createTable('prf.documents', function (table) {
        table.increments('id').primary();
        table.integer('profileId').notNullable();
        table.integer('serviceId').nullable();
        table.integer('typeId').nullable();
        table.string('documentType').nullable();
        table.text('url').nullable();
        table.text('title').nullable();
        table.text('description').nullable();
        table.integer('statusId').notNullable();
        table.boolean('isFinal').defaultTo(false).nullable();
        table.string('expiryDate', 255).nullable();
        table.float('size').defaultTo(0).nullable();
        table.integer('createdBy').notNullable();
        table.timestamp('createdOn', { useTz: true }).defaultTo(knex.fn.now()).notNullable();
        table.boolean('isDeleted').defaultTo(false).nullable();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.dropTable('prf.documents');
}