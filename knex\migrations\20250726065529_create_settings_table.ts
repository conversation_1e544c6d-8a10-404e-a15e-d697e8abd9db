import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("web.settings", function (table) {
    table.increments("id").primary();

    // General Settings
    table.string("app_name").notNullable();
    table.string("logo").nullable(); // file path or URL
    table.string("favicon").nullable(); // file path or URL
    table.text("copyright_text").nullable();

    // Monthly Subscription Discount
    table
      .enum("monthly_discount_type", ["fixed", "percentage"])
      .defaultTo("percentage");
    table.decimal("monthly_discount_value", 8, 2).defaultTo(0.0);
    table.string("monthly_coupon_code").nullable();
    table.integer("monthly_coupon_id").nullable();

    // Yearly Subscription Discount
    table
      .enum("yearly_discount_type", ["fixed", "percentage"])
      .defaultTo("percentage");
    table.decimal("yearly_discount_value", 8, 2).defaultTo(0.0);
    table.string("yearly_coupon_code").nullable();
    table.integer("yearly_coupon_id").nullable();

    table.timestamps(true, true); // created_at and updated_at
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTableIfExists("web.settings");
}
