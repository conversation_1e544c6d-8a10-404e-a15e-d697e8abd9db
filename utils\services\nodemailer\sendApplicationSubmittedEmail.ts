import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";


export const sendApplicationSubmittedEmail = async (
    username: string,
    email: string,
    res: Response
): Promise<void> => {
    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string,
        to: email,
        subject: "Your Application Has Been Received - FindAnyAgent",
        html: await applicationSubmittedTemplate(username),
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log("Application submitted email sent successfully!");

        response(res, 200, "Agent profile updated successfully");
        return;
    } catch (error) {
        console.error("Error sending application email:", error);
        res.status(500).json({
            message:
                "Failed to send application submission confirmation. Please try again later.",
        });
    }
};

export const applicationSubmittedTemplate = async (username: string) => {
    return `<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Application Received</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                color: #333;
                background-color: #f9f9f9;
            }
            .container {
                max-width: 600px;
                margin: 40px auto;
                background: #fff;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .header {
                font-size: 22px;
                font-weight: bold;
                color: #5e9b6d;
                text-align: center;
                margin-bottom: 20px;
            }
            .content {
                font-size: 16px;
                text-align: center;
            }
            .footer {
                margin-top: 30px;
                font-size: 14px;
                text-align: center;
                color: #777;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">Application Received</div>
            <div class="content">
                <p>Hi ${username},</p>
                <p>Thank you for completing your profile on <strong>FindAnyAgent</strong>.</p>
                <p>Your application has been successfully submitted. Our team will review your information and get back to you shortly.</p>
                <p>If any further information is required, we’ll reach out via email.</p>
            </div>
            <div class="footer">
                <p>Best regards,</p>
                <p><strong>FindAnyAgent Team</strong></p>
            </div>
        </div>
    </body>
    </html>`;
  };
  