import { Request, Response } from "express";
import express from "express";
import * as authController from "../../controller/auth";
import { authMiddleware } from "../../middleware/authMiddleware";
import { storageData, uploadErrorHandler } from "../../utils/services/multer";

const router = express.Router();

const upload = storageData("users");

router.post("/register", upload.none(), authController.register);
router.post("/account-verify", upload.none(), authController.accounrtVerifyOTP);
router.post("/verify-otp", upload.none(), authController.verifyOTP);
router.post("/login", upload.none(), authController.login);
router.post(
  "/forgot-password",
  upload.none(),
  authController.forgotPasswordOTP
);
router.post("/resend-otp", upload.none(), authController.resendOTP);
router.post(
  "/reset-password",
  upload.none(),
  authController.updatePasswordWithOTP
);
router.post("/validate-email", upload.none(), authController.validateEmail);
router.post(
  "/validate-username",
  upload.none(),
  authController.validateUserName
);
router.post(
  "/change-password",
  upload.none(),
  authMiddleware,
  authController.changeOrResetPassword
);

router.get("/logout", authMiddleware, authController.logout);

router.get("/get-user-profile", authMiddleware, authController.getProfile);
router.post(
  "/update-profile",
  authMiddleware,
  upload.single("image"),
  uploadErrorHandler,
  authController.updateProfile
);

router.get(
  "/account-status",
  authMiddleware,
  authController.accountActivatedDeactivated
);
router.put("/password", authMiddleware, authController.updatePassword);

router.get("/session/:id", authController.verifyUserToken);

export default router;
