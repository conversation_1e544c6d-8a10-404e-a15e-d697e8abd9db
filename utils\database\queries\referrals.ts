import { TABLE } from "../table";

export const REFERRALS = {
    INSERT_INTO_REFERRALS: `INSERT INTO "${TABLE.REFERRALS}" 
    ("userId", email, "referralId") 
    VALUES ($1, $2, $3) 
    RETURNING *;`,
    INSERT_INTO_USER_REFERRALS: `INSERT INTO "${TABLE.USER_REFERRALS}" 
    ("userId", "referralId") 
    VALUES ($1, $2) 
    RETURNING *;`,
    SELECT_BY_REFERRAL_ID: `SELECT * FROM "${TABLE.REFERRALS}" WHERE "referralId" = $1 LIMIT 1;`,
};
