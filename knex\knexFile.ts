const dotenv = require("dotenv");
const { resolve } = require("path");

dotenv.config({ path: resolve(__dirname, "../.env") });

module.exports = {
  client: "postgresql",
  connection: {
    host: process.env.DATABASE_HOST || "",
    database: process.env.DATABASE_NAME || "",
    user: process.env.DATABASE_USER || "",
    password: process.env.DATABASE_PASSWORD || "",
    port: parseInt(process.env.DATABASE_PORT || "5452", 10),
  },
  debug: process.env.NODE_ENV === "development",
  pool: {
    min: 2,
    max: 10,
  },
  migrations: {
    tableName: "knex_migrations",
  },
  seeds: {
    directory: "./seeds",
  },
};
