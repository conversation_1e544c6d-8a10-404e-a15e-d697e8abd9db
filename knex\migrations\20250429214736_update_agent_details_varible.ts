import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agencies', function (table) {
 
        table.integer('locationId').nullable().alter();
        table.integer('typeId').nullable().alter();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agencies', function (table) {
 
        table.integer('locationId').alter();
        table.integer('typeId').alter();
    });
}