import type { Knex } from "knex"; 

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').createTable('agentdetails', (table) => { 
    table.increments('id').primary();  
    
    // Foreign Key to agents table in 'agn' schema
    table.integer('agent_id').unsigned().nullable().defaultTo(null); 
    table.foreign('agent_id').references('id').inTable('agn.agents').onDelete('CASCADE'); 
          
    // Foreign Key to profile table in 'prf' schema
    table.integer('profile_id').unsigned().nullable().defaultTo(null); 
    table.foreign('profile_id').references('id').inTable('prf.profile').onDelete('CASCADE');

    // Other columns
    table.string('industryMission').nullable().defaultTo(null);   
    table.string('operationArea').nullable().defaultTo(null);   
    table.string('industrySubCategory').nullable().defaultTo(null);  
    table.string('specializationMission').nullable().defaultTo(null); 
    table.string('yearOfExperience').nullable().defaultTo(null); 
    table.string('otherForFreelancer').nullable().defaultTo(null); 
    table.string('employerName').nullable().defaultTo(null); 
    table.string('position').nullable().defaultTo(null); 
    table.string('employedLocation').nullable().defaultTo(null); 
    table.string('nationality').nullable().defaultTo(null);     
    table.string('summary').nullable().defaultTo(null); 
 

    table.string('workType').nullable().defaultTo(null); // ["freelancer", "employed", "licensedAgent"]
    table.specificType('profilePhotos', 'text[]').nullable().defaultTo(null); // Array of text
    table.specificType('licenseDocs', 'text[]').nullable().defaultTo(null); 
    table.specificType('freelancePermitDocs', 'text[]').nullable().defaultTo(null); 
    table.specificType('tradeLicenseDocs', 'text[]').nullable().defaultTo(null); 
    table.specificType('employmentLetters', 'text[]').nullable().defaultTo(null); 
    table.specificType('certifications', 'text[]').nullable().defaultTo(null); 
    table.specificType('languages', 'text[]').nullable().defaultTo(null); 
    table.specificType('areaCovered', 'text[]').nullable().defaultTo(null); 

    table.timestamps(true, true); // Automatically adds created_at and updated_at
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').dropTableIfExists('agentdetails'); // Drops the table if it exists
}
