import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('prf').createTable('tickets', (table) => {
    table.increments('id').primary();
    table.integer('agent_id').notNullable().references('id').inTable('prf.profile');
    table.string('agent_name', 255).notNullable();
    table.string('agent_email', 255).notNullable();
    table.string('title', 500).notNullable();
    table.text('description').notNullable();
    table.string('status', 50).notNullable().defaultTo('open');
    table.check("status in ('open', 'in-progress', 'resolved', 'closed')");
    table.string('priority', 50).notNullable();
    table.check("priority in ('low', 'medium', 'high', 'urgent')");
    table.string('category', 100).notNullable().defaultTo('General');
    table.check("category in ('General', 'Technical', 'Payment', 'Account', 'Feature Request', 'Bug Report', 'Other')");
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());
    table.integer('created_by').references('id').inTable('prf.profile');
    table.integer('assigned_to').references('id').inTable('prf.profile');
  });

  await knex.schema.withSchema('prf').createTable('responses', (table) => {
    table.increments('id').primary();
    table.integer('ticket_id').notNullable().references('id').inTable('prf.tickets').onDelete('CASCADE');
    table.integer('admin_id').references('id').inTable('prf.profile');
    table.integer('agent_id').references('id').inTable('prf.profile');
    table.text('response').notNullable();
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('prf').dropTableIfExists('responses');
  await knex.schema.withSchema('prf').dropTableIfExists('tickets');
} 