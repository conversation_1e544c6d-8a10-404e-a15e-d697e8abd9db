import { Request, Response, NextFunction } from "express";
import { response } from "../utils/response";

export enum FeatureType {
  TEXT = "TEXT",
  BOOLEAN = "BOOLEAN",
  NUMERIC = "NUMERIC",
  ENUM = "ENUM",
}

interface Feature {
  featureConstant: string;
  featureValue: string;
  featureType: FeatureType;
}

interface SubscriptionPackage {
  isActive: boolean;
  features: Feature[] | null;
  name: string;
}

interface User {
  id: string;
  subscription: SubscriptionPackage;
}

type Operator = "equals" | "gte" | "lte" | "booleanTrue";

interface FeatureRequirement {
  constant: string;
  expectedValue?: string | number;
  operator?: Operator;
}

export const checkFeatureAccess = (requirement: FeatureRequirement) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user as User;

    if (!user || !user.subscription || !user.subscription.isActive) {
      response(
        res,
        403,
        "Access denied. You must have an active subscription to access this resource."
      );
      return;
    }

    const { features } = user.subscription;
    if (!features || features.length === 0) {
      response(
        res,
        403,
        "Access denied. Your current subscription does not include any features."
      );
      return;
    }

    const feature = features.find(
      (f) => f.featureConstant === requirement.constant
    );
    if (!feature) {
      response(
        res,
        403,
        `Access denied. Your subscription does not include the required feature: ${requirement.constant}.`
      );
      return;
    }

    const { expectedValue, operator = "equals" } = requirement;

    switch (operator) {
      case "equals":
        if (
          expectedValue?.toString().toLowerCase() !==
          feature.featureValue.toLowerCase()
        ) {
          response(
            res,
            403,
            `Access denied. Feature "${requirement.constant}" must be "${expectedValue}", but your current plan does not meet this requirement.`
          );
          return;
        }
        break;

      case "booleanTrue":
        if (
          feature.featureValue.toLowerCase() !== "true" &&
          feature.featureValue.toLowerCase() !== "yes"
        ) {
          response(
            res,
            403,
            `Access denied. Feature "${requirement.constant}" must be enabled in your subscription.`
          );
          return;
        }
        break;

      case "gte":
        if (Number(feature.featureValue) < Number(expectedValue)) {
          response(
            res,
            403,
            `Access denied. Your subscription allows up to ${feature.featureValue} for "${requirement.constant}", but at least ${expectedValue} is required.`
          );
          return;
        }
        break;

      case "lte":
        if (Number(feature.featureValue) > Number(expectedValue)) {
          response(
            res,
            403,
            `Access denied. Feature "${requirement.constant}" exceeds the allowed limit.`
          );
          return;
        }
        break;

      default:
        response(res, 500, `Invalid feature comparison operator used.`);
        return;
    }

    next();
  };
};
