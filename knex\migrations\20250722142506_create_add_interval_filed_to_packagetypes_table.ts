import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("look").alterTable("packagetype", (table) => {
    table
      .string("interval")
      .nullable()
      .defaultTo("month")
      .comment("Interval for the package type, e.g., month, year");

  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema("look").alterTable("packagetype", (table) => {
    // Roll back the new fields
    table.dropColumn("interval");
  });
}
