import { TABLE } from "../table";

export const PropertyQueries = {
  // Status & Filtering
  FIND_STATUS_ID_BY_NAME: `
    SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1
  `,
  GET_FILTERED_PROPERTIES: `
    SELECT * FROM get_filtered_properties($1, $2, $3, $4, $5, $6)
  `,
  GET_PROPERTY_STATUS_COUNTS: `
    SELECT * FROM get_property_status_counts($1, $2, $3)
  `,
  GET_PROPERTY_DETAIL_BY_ID: `
    SELECT * FROM get_property_detail($1)
  `,

  // Dynamic: insert/update property
  CREATE_OR_UPDATE_PROPERTY: (count: number) =>
    `SELECT * FROM create_or_update_property(${Array.from(
      { length: count },
      (_, i) => `$${i + 1}`
    ).join(", ")})`,

  // Property Image
  INSERT_PROPERTY_IMAGE: `
    INSERT INTO agn.images ("propertyId", "imageUrl", "statusId", "mediaTypeId", "createdBy")
    VALUES ($1, $2, $3, $4, $5)
  `,

  // Property Features
  CHECK_EXISTING_FEATURE: `
    SELECT 1 FROM agn.features WHERE "propertyId" = $1 AND LOWER("featureName") = LOWER($2) LIMIT 1
  `,
  INSERT_FEATURE: `
    INSERT INTO agn.features ("propertyId", "featureName", "statusId", "createdBy", "createdOn")
    VALUES ($1, $2, $3, $4, NOW())
  `,

  // Update Status
  UPDATE_PROPERTY_STATUS: `
    UPDATE agn.properties SET "statusId" = $1, "modifiedOn" = NOW() WHERE id = $2
  `,

  // Dynamic Toggle (getter and updater)
  GET_PROPERTY_FLAG: (column: string) =>
    `SELECT "${column}" FROM agn.properties WHERE id = $1`,

  UPDATE_PROPERTY_FLAG: (column: string) =>
    `UPDATE agn.properties SET "${column}" = $1, "modifiedOn" = NOW() WHERE id = $2`,

  // Delete Property
  DELETE_PROPERTY_BY_ID: `
    DELETE FROM agn.properties WHERE id = $1
  `,
};
