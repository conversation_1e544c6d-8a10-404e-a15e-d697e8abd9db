import { z } from "zod";

const roleSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, "Name is required"),
  descriptions: z.string().min(1, "Description is required"),
  createdBy: z.number().int().positive(),
  createdOn: z.date().optional(),
  modifiedBy: z.number().int().positive().optional(),
  modifiedOn: z.date().optional(),
});

const createRoleSchema = roleSchema.omit({ id: true, createdOn: true, modifiedOn: true });

const updateRoleSchema = roleSchema.omit({ id: true, createdBy: true, createdOn: true });

export { roleSchema, createRoleSchema, updateRoleSchema };