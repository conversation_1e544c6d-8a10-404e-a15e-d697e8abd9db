import nodemailer from "nodemailer";
import transporter from ".";
import { format } from "date-fns";

export const sendExpiryReminderEmail = async (
    email: string,
    username: string,
    documentType: string,
    expiryDate: Date
): Promise<void> => {
    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string,
        to: email,
        subject: `Reminder: ${documentType} is expiring soon - FindAnyAgent`,
        html: await expiryReminderEmailHTMLTemplate(username, documentType, expiryDate),
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log(`Reminder email sent to ${email} for ${documentType}`);
    } catch (error) {
        console.error("Error sending expiry reminder email:", error);
    }
};

const expiryReminderEmailHTMLTemplate = async (
    username: string,
    documentType: string,
    expiryDate: Date
): Promise<string> => {
    const formattedDate = format(expiryDate, "MMMM dd, yyyy");

    return `<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document Expiry Reminder</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f9f9f9; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 40px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
    .header { font-size: 22px; color: #d97706; font-weight: bold; }
    .content { font-size: 16px; margin-top: 20px; }
    .highlight { font-weight: bold; color: #b45309; }
    .footer { margin-top: 30px; font-size: 14px; color: #777; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">Your ${documentType} will expire soon</div>
    <div class="content">
      <p>Hi ${username},</p>
      <p>This is a friendly reminder that your <span class="highlight">${documentType}</span> is set to expire on <span class="highlight">${formattedDate}</span>.</p>
      <p>Please ensure you renew it before the expiry date to avoid disruption.</p>
    </div>
    <div class="footer">
      <p>Best regards,</p>
      <p><strong>FindAnyAgent Team</strong></p>
    </div>
  </div>
</body>
</html>`;
};
