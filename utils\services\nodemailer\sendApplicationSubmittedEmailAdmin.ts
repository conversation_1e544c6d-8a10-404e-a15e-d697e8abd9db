import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

/* -------------------------------------------------- */
/*  PUBLIC API                                        */
/* -------------------------------------------------- */

export const sendAdminApplicationSubmittedEmail = async (
  firstName: string,
  lastName: string,
  email: string,
  phone: string,
  accountType: string,
  res: Response
): Promise<void> => {
  try {
    // 1️⃣ build both messages
    const mailToApplicant: nodemailer.SendMailOptions = {
      from: process.env.VERIFICATION_EMAIL as string,
      to: email,
      subject: "Your Application Has Been Received - FindAnyAgent",
      html: await applicantTemplate(firstName),
    };

    const mailToAdmin: nodemailer.SendMailOptions = {
      from: process.env.VERIFICATION_EMAIL as string,
      to: process.env.SUPERADMIN_EMAIL as string, // ✅ .env MUST contain this
      subject: "New Registration Application – Action Required",
      html: await adminTemplate({
        firstName,
        lastName,
        email,
        phone,
        accountType,
      }),
    };

    // 2️⃣ send them together – fail fast if either one errors
    await Promise.all([
      transporter.sendMail(mailToApplicant),
      transporter.sendMail(mailToAdmin),
    ]);
    console.log("Applicant + Super-admin emails sent successfully");
  } catch (error) {
    console.error("Error sending application emails:", error);
  }
};

/* -------------------------------------------------- */
/*  EMAIL TEMPLATES                                   */
/* -------------------------------------------------- */

const applicantTemplate = async (firstName: string) => `
<html lang="en">
  <head><meta charset="UTF-8" /></head>
  <body style="font-family:Arial,sans-serif;color:#333;background:#f9f9f9">
    <div style="max-width:600px;margin:40px auto;background:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1)">
      <h2 style="color:#5e9b6d;text-align:center">Application Received</h2>
      <p>Hi ${firstName},</p>
      <p>Thank you for completing your profile on <strong>FindAnyAgent</strong>.</p>
      <p>Your application has been successfully submitted. Our team will review your information and get back to you shortly.</p>
      <p>If any further information is required, we’ll reach out via email.</p>
      <p style="margin-top:30px">Best regards,<br /><strong>FindAnyAgent Team</strong></p>
    </div>
  </body>
</html>
`;

interface AdminTemplateProps {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  accountType: string;
}

const adminTemplate = async ({
  firstName,
  lastName,
  email,
  phone,
  accountType,
}: AdminTemplateProps) => `
<html lang="en">
  <head><meta charset="UTF-8" /></head>
  <body style="font-family:Arial,sans-serif;color:#333;background:#f9f9f9">
    <div style="max-width:600px;margin:40px auto;background:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1)">
      <h2 style="color:#d92b2b;text-align:center">New Registration Application</h2>
      <p>A new agent application has been submitted. Please review it in the admin panel.</p>
      <ul style="list-style:none;padding-left:0;font-size:15px;margin-top:20px">
        <li><strong>Name:</strong> ${firstName} ${lastName}</li>
        <li><strong>Email:</strong> ${email}</li>
        <li><strong>Phone:</strong> ${phone}</li>
        <li><strong>Account&nbsp;Type:</strong> ${accountType}</li>
      </ul>
      <p style="margin-top:30px">— <strong>FindAnyAgent Bot</strong></p>
    </div>
  </body>
</html>
`;
