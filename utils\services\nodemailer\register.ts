import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

export const generateOTP = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

export const sendRegistrationEmail = async (
  username: string,
  email: string,
  res: Response,
  otp: string
): Promise<void> => {

  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject: "Your OTP for Registration - FindAnyAgent",
    html: await sendOtpHTMLTemplate(otp, username),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Registration email sent successfully!");

    response(
      res,
      201,
      "Registration successful. OTP has been sent to your email."
    );
  } catch (error) {
    console.error("Error sending registration email:", error);
    res.status(500).json({
      message:
        "Registration failed. Unable to send OTP. Please try again later.",
    });
  }
};

export const resendVerificationEmail = async (
  email: string,
  res: Response,
  otp: string,
  verified: boolean = true
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject: "Your OTP Code for FindAnyAgent (Valid for 5 Minutes)",
    html: await resendOtpHTMLTemplate(otp, verified),
  };

  try {
    await transporter.sendMail(mailOptions);

    if (!verified) {
      response(
        res,
        201,
        "Your account is not verified, OTP has been sent to your email."
      );
      return;
    } else {
      response(res, 201, "OTP has been sent to your email.");
      return;
    }
  } catch (error) {
    console.error("Error sending email:", error);
    res.status(500).json({
      message:
        "Registration failed. Unable to send OTP. Please try again later.",
    });
    return;
  }
};

export const sendOtpHTMLTemplate = async (otp: string, username: string) => {
  return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration OTP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .header {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            color: #5e9b6d;
        }
        .content {
            margin: 20px 0;
            text-align: center;
        }
        .otp {
            display: inline-block;
            font-size: 24px;
            font-weight: bold;
            color: #5e9b6d;
            background-color: #eefaf1;
            padding: 10px 20px;
            border-radius: 4px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            Welcome to FindAnyAgent!
        </div>
        <div class="content">
            <p>Hi ${username},</p>
            <p>Thank you for registering with FindAnyAgent! Use the OTP below to verify your email address:</p>
            <p class="otp">${otp}</p>
            <p>This OTP is valid for 10 minutes. If you did not request this, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p>FindAnyAgent Team</p>
        </div>
    </div>
</body>
</html>`;
};

export const resendOtpHTMLTemplate = async (otp: string, verified: boolean) => {
  return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${
      verified
        ? "Resend OTP for Verification"
        : "Complete Your Registration - OTP Inside!"
    }</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .header {
            font-size: 22px;
            font-weight: bold;
            color: #5e9b6d;
        }
        .content {
            margin: 20px 0;
            font-size: 16px;
        }
        .otp {
            display: inline-block;
            font-size: 28px;
            font-weight: bold;
            color: #5e9b6d;
            background-color: #eefaf1;
            padding: 12px 24px;
            border-radius: 6px;
            letter-spacing: 3px;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
        }
        .note {
            font-size: 14px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            ${
              verified
                ? "Resend OTP for Verification"
                : "Complete Your Registration - OTP Inside!"
            }
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>We received a request to ${
              verified ? "resend your OTP" : "verify your email"
            } for FindAnyAgent.</p>
            <p>Use the OTP below to ${
              verified
                ? "log in securely"
                : "complete your account registration"
            }:</p>
            <p class="otp">${otp}</p>
            <p class="note">This OTP is valid for 5 minutes. If you did not request this, please ignore this email or contact support.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>FindAnyAgent Team</strong></p>
        </div>
    </div>
</body>
</html>`;
};
