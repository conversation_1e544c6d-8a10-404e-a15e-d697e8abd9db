import express from "express";
import * as servicesController from "../../controller/services/index";

const router = express.Router();

router.get("/services", servicesController.getSharedParentServices);
router.get("/services/:ids", servicesController.getParentServicesByIds); //ids: '1,17,25'
router.get("/services/:parentId/:group", servicesController.getServicesByParentAndGroup);

// router.get("/all-services",  servicesController.getAllServices);
 
export default router;
