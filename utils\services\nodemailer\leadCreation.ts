import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express"; 

export const sendEmailToLeadCreated = async (
  firstName: string, 
  email: string,  
): Promise<void> => {
  try { 
    const mailToApplicant: nodemailer.SendMailOptions = {
      from: process.env.VERIFICATION_EMAIL as string,
      to: email,
      subject: " 🎉 Welcome to FindAnyAgent – You’re on the List!",
      html: await LeadTemplate(firstName),
    };
 
    await Promise.all([
      transporter.sendMail(mailToApplicant),
    
    ]);
    console.log("Email send to Lead successfully");
  } catch (error) {
    console.error("Error sending to Lead emails:", error);
  }
}; 
 

const LeadTemplate = async (firstName: string) => `
<html lang="en">
  <head><meta charset="UTF-8" /></head>
  <body style="font-family:Arial,sans-serif;color:#333;background:#f9f9f9">
    <div style="max-width:600px;margin:40px auto;background:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1)">
      <h2 style="color:#5e9b6d;text-align:center">Welcome to FindAnyAgent!</h2>
      <p>Dear ${firstName},</p>
      <p style="font-size: 16px;">Thank you for joining the <strong>FindAnyAgent</strong> pre-launch waitlist!</p>
      <p style="font-size: 16px;">We’re excited to have you with us as we prepare to launch the UAE’s #1 platform connecting agents and agencies with quality clients across 35+ industries.</p>
      
      <h3 style="color:#004aad;font-size: 16px;">Why Join FindAnyAgent?</h3>
      <ul style="font-size: 16px; line-height: 1.6;">
        <li>✅ 300% More Leads</li>
        <li>✅ 100% Free Forever (Basic Plan)</li>
        <li>✅ Tools Built for Agents – CRM, Listings, Branding & More</li>
        <li>✅ Already 10,000+ Agents Pre-Registered!</li>
      </ul>
      
      <p style="font-size: 16px;">We’ll keep you updated on our official launch and how you can start using FindAnyAgent to grow your connections and opportunities.</p>
      
      <p style="font-size: 16px;">In the meantime, feel free to follow us for updates:<br/>
        <a href="${process.env.INSTAGRAM_URL}" style="color:#004aad;">@findanyagent on Instagram</a>
      </p>
      
      <p style="margin-top:30px">Thanks again for joining the future of agent success in the UAE.</p>
      <p style="margin-top:10px">Best regards,<br /><strong>FindAnyAgent Team</strong></p>
      <p style="margin-top:10px"><a href="${process.env.WEBSITE_URL}" style="color: #004aad;">www.findanyagent.ae</a></p>
    </div>
  </body>
</html>
`;

 
 