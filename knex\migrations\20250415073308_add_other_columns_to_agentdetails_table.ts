import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").alterTable("agentdetails", (table) => {
    table.string("industryMissionOther").nullable();
    table.string("industrySubCategoryOther").nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").alterTable("agentdetails", (table) => {
    table.dropColumn("industryMissionOther");
    table.dropColumn("industrySubCategoryOther");
  });
} 