import express from "express";
import {
  create<PERSON><PERSON><PERSON>,
  delete<PERSON>alesperson,
  getAllSalespersons,
  getSalespersonById,
  updateS<PERSON>person,
  updateSalespersonStatus,
} from "../../../controller/admin/salesperson.controller";
import { storageData } from "../../../utils/services/multer";
const router = express.Router();

const upload = storageData("salespersons");

// -------------------- ROUTES --------------------
router.get("/", getAllSalespersons); // List all
router.get("/:id", getSalespersonById); // Get by ID
router.post("/", upload.none(), createSalesperson); // Create
router.put("/:id", upload.none(), updateSalesperson); // Update
router.delete("/:id", deleteSalesperson); // Delete
router.patch("/:id/status", upload.none(), updateSalespersonStatus); // Update status only

export default router;
