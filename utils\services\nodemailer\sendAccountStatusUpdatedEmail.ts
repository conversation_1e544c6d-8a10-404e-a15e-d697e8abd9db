import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";

export const sendAccountStatusUpdatedEmail = async (
  fullName: string,
  email: string,
  newStatus: string,
  res?: Response
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject: "Account Status Updated - FindAnyAgent",
    html: await accountStatusUpdatedEmailTemplate(fullName, newStatus),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Account status update email sent to:", email);
  } catch (error) {
    console.error("Error sending status update email:", error);
    if (res) {
      res.status(500).json({
        message: "Unable to send update email. Status was updated successfully.",
      });
    }
  }
};

export const accountStatusUpdatedEmailTemplate = async (
  fullName: string,
  newStatus: string
): Promise<string> => {
  return `<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Account Status Updated</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f7f7f7;
        color: #333;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 600px;
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      }
      .title {
        font-size: 22px;
        color: #5e9b6d;
        margin-bottom: 20px;
      }
      .content {
        font-size: 16px;
        line-height: 1.6;
      }
      .status {
        display: inline-block;
        margin-top: 15px;
        font-size: 18px;
        color: #ffffff;
        background-color: #5e9b6d;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
      }
      .footer {
        margin-top: 30px;
        font-size: 14px;
        color: #777;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="title">Account Status Updated</div>
      <div class="content">
        <p>Hello ${fullName},</p>
        <p>Your account status has been updated on <strong>FindAnyAgent</strong>.</p>
        <p>Your new status is:</p>
        <div class="status">${newStatus}</div>
        <p>If you have any questions or need support, please contact our team.</p>
      </div>
      <div class="footer">
        <p>Thanks,</p>
        <p><strong>FindAnyAgent Team</strong></p>
      </div>
    </div>
  </body>
</html>`;
};
