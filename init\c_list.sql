CREATE SCHEMA IF NOT EXISTS list;
DROP TABLE IF EXISTS "list"."currency";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "list"."currency" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "name" text NOT NULL,
    "symbol" text,
    "rate" numeric NOT NULL,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz
);


-- Indices
CREATE UNIQUE INDEX idx_16443_pk__currency__3214ec0753b99180 ON list.currency USING btree (id);

DROP TABLE IF EXISTS "list"."languages";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "list"."languages" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "localName" text,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz
);


-- Indices
CREATE UNIQUE INDEX idx_16450_pk__languages__3214ec07e505efb4 ON list.languages USING btree (id);

DROP TABLE IF EXISTS "list"."location";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "list"."location" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "local" text,
    "typeId" int4 NOT NULL,
    "parentId" int4,
    "postalCode" text,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_location_parent" FOREIGN KEY ("parentId") REFERENCES "list"."location"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16457_pk__location__3214ec07e3cbebe6 ON list.location USING btree (id);

DROP TABLE IF EXISTS "list"."services";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "list"."services" (
    "id" SERIAL PRIMARY KEY,
    "group" text NOT NULL,
    "name" text NOT NULL,
    "description" text,
    "typeId" int4 NOT NULL,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz
);


-- Indices
CREATE UNIQUE INDEX idx_16464_pk__services__3214ec0706444e77 ON list.services USING btree (id);

DROP TABLE IF EXISTS "list"."subscription";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "list"."subscription" (
    "id" SERIAL PRIMARY KEY,
    "package" text NOT NULL,
    "typeId" int4 NOT NULL,
    "details" text,
    "statusId" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz
);


-- Indices
CREATE UNIQUE INDEX idx_16471_pk__subscription__3214ec073a2a1068 ON list.subscription USING btree (id);