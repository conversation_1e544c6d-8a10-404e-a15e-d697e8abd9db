import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.profile', function (table) {
        table.jsonb("tempData").nullable();
        table.jsonb("requiredFields").nullable();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.profile', function (table) {
        table.dropColumn("tempData");
        table.dropColumn("requiredFields");
    });
}
