import { ImageDTO } from "./ImageDTO";

export interface PropertyDTO {
  id: number;
  name: string;
  price: string;
  size: string;
  location_id: number;
  property_type_id: number;
  listing_type: number;
  status_id: number;
  status_name: string;
  is_featured: boolean;
  is_verified: boolean;
  expiry_date: string; // ISO date string
  slug: string;
  meta_title: string;
  images: ImageDTO[];
}
