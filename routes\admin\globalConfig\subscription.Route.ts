import express from "express";
import {
  getAllPackageTypes, getPackageTypeById, createPackageType, updatePackageType, deletePackageType,
  getAllFeatures, getFeatureById, createFeature, updateFeature, deleteFeature,
  getAllFeatureValues, getFeatureValueById, getFeatureValuesByPackageTypeId, createFeatureValue, updateFeatureValue, deleteFeatureValue,
  getPackagesWithFeatures, getPackagesByUserType, getFeatureValuesByPackageAndUserType,
  updatePackageStatus, getDistinctFeatureNames
} from "../../../controller/admin/globalConfig/subscription";

const router = express.Router();

// Package Types
router.get("/packages", getAllPackageTypes);
router.get("/packages/:id", getPackageTypeById);
router.post("/packages", createPackageType);
router.put("/packages/:id", updatePackageType);
router.put("/packages/:id/status", updatePackageStatus);
router.delete("/packages/:id", deletePackageType);

// Features Meta
router.get("/packagefeaturesmeta", getDistinctFeatureNames);
router.get("/features", getAllFeatures);
router.get("/features/:id", getFeatureById);
router.post("/features", createFeature);
router.put("/features/:id", updateFeature);
router.delete("/features/:id", deleteFeature);

// Feature Values
router.get("/feature-values", getAllFeatureValues);
router.get("/feature-values/:id", getFeatureValueById);
router.get("/packages/:packageTypeId/feature-values", getFeatureValuesByPackageTypeId); // it returns all features names, values, types and display order data for a specific package
router.post("/feature-values", createFeatureValue);
router.put("/feature-values/:id", updateFeatureValue);
router.delete("/feature-values/:id", deleteFeatureValue);

// Helper Endpoints
router.get("/packages-with-features", getPackagesWithFeatures); // for table dynamic data
router.get("/packages/user-type/:userType", getPackagesByUserType); // for packages by user
router.get("/feature-values/package/:packageTypeId/user-type/:userType", getFeatureValuesByPackageAndUserType); // for feature values by package and user type

export default router;