import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";

export const sendSubscriptionConfirmationEmail = async (
  fullName: string,
  email: string,
  packageName: string,
  price: number,
  currency: string,
  startDate: string,
  endDate: string,
  res?: Response
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject: "Subscription Confirmation - FindAnyAgent",
    html: await subscriptionConfirmationTemplate(
      fullName,
      packageName,
      price,
      currency,
      startDate,
      endDate
    ),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Subscription email sent to:", email);
  } catch (error) {
    console.error("Error sending subscription email:", error);
    if (res) {
      res.status(500).json({
        message: "Subscription successful, but email could not be sent.",
      });
    }
  }
};

export const subscriptionConfirmationTemplate = async (
  fullName: string,
  packageName: string,
  price: number,
  currency: string,
  startDate: string,
  endDate: string
): Promise<string> => {
  return `<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Subscription Confirmation</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f9f9f9;
        color: #333;
        padding: 0;
        margin: 0;
      }
      .container {
        max-width: 600px;
        margin: 40px auto;
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        text-align: center;
      }
      .title {
        color: #008c6e;
        font-size: 24px;
        margin-bottom: 20px;
      }
      .content {
        font-size: 16px;
        line-height: 1.6;
      }
      .highlight {
        font-weight: bold;
        color: #008c6e;
      }
      .footer {
        margin-top: 30px;
        font-size: 14px;
        color: #999;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="title">Subscription Confirmed</div>
      <div class="content">
        <p>Hello ${fullName},</p>
        <p>Thank you for subscribing to the <span class="highlight">${packageName}</span> package.</p>
        <p>Your subscription details:</p>
        <ul style="text-align: left; display: inline-block;">
          <li><strong>Start Date:</strong> ${startDate}</li>
          <li><strong>End Date:</strong> ${endDate}</li>
          <li><strong>Price:</strong> ${price} ${currency}</li>
        </ul>
        <p>If you have any questions or need help, feel free to contact us.</p>
      </div>
      <div class="footer">
        <p>Thanks,</p>
        <p><strong>FindAnyAgent Team</strong></p>
      </div>
    </div>
  </body>
</html>`;
};
