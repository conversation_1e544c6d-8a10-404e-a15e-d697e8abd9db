import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
    // Deletes ALL existing entries
    await knex("sec.roles").del();

    // Inserts seed entries
    await knex("sec.roles").insert([
        { name: "superAdmin", descriptions: "Super Administrator role", createdBy: 1 },
        { name: "admin", descriptions: "Administrator role", createdBy: 1 },
        { name: "agent", descriptions: "Agent role", createdBy: 1 },
        { name: "agency", descriptions: "Agency role", createdBy: 1 },
        { name: "developer", descriptions: "Developer role", createdBy: 1 },
        { name: "user", descriptions: "User role", createdBy: 1 },
        { name: "agencyAdmin", descriptions: "Agency Administrator role", createdBy: 1 },
    ]);
}
