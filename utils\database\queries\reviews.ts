import { TABLE } from "../table";

export const REVIEWS = {
  // Create a new review
  CREATE_REVIEW: `
    WITH inserted_review AS (
      INSERT INTO agn.${TABLE.REVIEW} (
        "reviewerId", "revieweeId", "reviewText", "rating", "statusId", "createdBy"
      ) VALUES ($1, $2, $3, $4, $5, $6) 
      RETURNING *
    )
    SELECT 
      r.*,
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName"
    FROM inserted_review r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
  `,

  // Get all reviews for admin with pagination
  GET_ALL_REVIEWS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details (agent/agency being reviewed)
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    ORDER BY r."created_at" DESC
    LIMIT $1 OFFSET $2
  `,

  // Get reviews count for pagination
  GET_REVIEWS_COUNT: `
    SELECT COUNT(*) as total 
    FROM agn.${TABLE.REVIEW}
  `,

  // Get reviews by status
  GET_REVIEWS_BY_STATUS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."statusId" = $1
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  // Get single review by ID
  GET_REVIEW_BY_ID: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r.id = $1
  `,

  // Update review status
  UPDATE_REVIEW_STATUS: `
    UPDATE agn.${TABLE.REVIEW} 
    SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP
    WHERE id = $3
    RETURNING *
  `,

  // Update review status with reason (for hiding)
  UPDATE_REVIEW_STATUS_WITH_REASON: `
    UPDATE agn.${TABLE.REVIEW} 
    SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP, "hideReason" = $4
    WHERE id = $3
    RETURNING *
  `,

  // Add note to review
  ADD_REVIEW_NOTE: `
    INSERT INTO agn.review_notes (
      "reviewId", "note", "createdBy", "created_at"
    ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
    RETURNING *
  `,

  // Get review notes
  GET_REVIEW_NOTES: `
    SELECT 
      n.id,
      n.note,
      n."created_at",
      admin."firstName" AS "adminFirstName",
      admin."lastName" AS "adminLastName"
    FROM agn.review_notes n
    LEFT JOIN sec.login l ON n."createdBy" = l.id
    LEFT JOIN prf.profile admin ON l."profileId" = admin.id
    WHERE n."reviewId" = $1
    ORDER BY n."created_at" DESC
  `,

  // Get review action history
  GET_REVIEW_HISTORY: `
    SELECT 
      h.id,
      h.action,
      h."previousStatus",
      h."newStatus",
      h.notes,
      h."created_at",
      admin."firstName" AS "adminFirstName",
      admin."lastName" AS "adminLastName"
    FROM agn.review_history h
    LEFT JOIN sec.login l ON h."createdBy" = l.id
    LEFT JOIN prf.profile admin ON l."profileId" = admin.id
    WHERE h."reviewId" = $1
    ORDER BY h."created_at" DESC
  `,

  // Log review action
  LOG_REVIEW_ACTION: `
    INSERT INTO agn.review_history (
      "reviewId", action, "previousStatus", "newStatus", notes, "createdBy", "created_at"
    ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
    RETURNING *
  `,

  // Get approved reviews for a specific profile (public view)
  GET_APPROVED_REVIEWS_FOR_PROFILE: `
    SELECT 
      r.id,
      r."reviewText",
      r.rating,
      r."created_at",
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail"
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    WHERE r."revieweeId" = $1 AND r."statusId" = 2
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  // Check if user already reviewed this profile
  CHECK_EXISTING_REVIEW: `
    SELECT id FROM agn.${TABLE.REVIEW} 
    WHERE "reviewerId" = $1 AND "revieweeId" = $2
  `,

  // Get average rating and total reviews for profile
  GET_PROFILE_RATING_STATS: `
    SELECT 
      COUNT(*) as "totalReviews",
      ROUND(AVG(rating), 2) as "averageRating"
    FROM agn.${TABLE.REVIEW} 
    WHERE "revieweeId" = $1 AND "statusId" = 2
  `,

  // Delete review (soft delete by changing status)
  DELETE_REVIEW: `
    UPDATE agn.${TABLE.REVIEW} 
    SET "statusId" = 4, "modifiedBy" = $1, "updated_at" = CURRENT_TIMESTAMP
    WHERE id = $2
    RETURNING *
  `,

  // Get reviews count by status
  GET_REVIEWS_COUNT_BY_STATUS: `
    SELECT COUNT(*) as total 
    FROM agn.${TABLE.REVIEW}
    WHERE "statusId" = $1
  `,

  // Search reviews by reviewer or reviewee name
  SEARCH_REVIEWS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE 
      LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE LOWER($1)
      OR LOWER(CONCAT(reviewee."firstName", ' ', reviewee."lastName")) LIKE LOWER($1)
      OR LOWER(agn.name) LIKE LOWER($1)
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  // Get user's own reviews (reviews they have written)
  GET_USER_REVIEWS: `
    SELECT 
      r.id,
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details (the user who wrote the review)
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."reviewerId" = $1
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `
};
