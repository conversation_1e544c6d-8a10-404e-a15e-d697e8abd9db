import { TABLE } from "../table";

export const AGENTS = {
  CREATE_AGENT: `INSERT INTO ${TABLE.AGENT} ("profileId", "statusId","createdBy") VALUES ($1, $2 ,$3) RETURNING *`,
  CREATE_ACCOUNT: `INSERT INTO ${TABLE.ACCOUNT} ("profileId",verified,"createdBy")   VALUES ($1, $2, $3) RETURNING *`,
  ADD_LICIENCE_DETAILS: `UPDATE ${TABLE.ACCOUNT} 
  SET "licenseTypeId" = $1,
      "licenseNo" = $2,
      "licenseIssueDate" = $3,
      "licenseExpiredDate" = $4
  WHERE "profileId" = $5
  RETURNING *`,
  CHECK_EXISTING_AGENT: `SELECT * FROM ${TABLE.AGENT} WHERE "profileId" = $1`,
  CHECK_EXISTING_ACCOUNT: `SELECT * FROM ${TABLE.ACCOUNT} WHERE "profileId" = $1`,
  CHECK_EXISTING_ACCOUNT_DETAILS: `SELECT * FROM ${TABLE.AGENT_DETAILS} WHERE profile_id = $1`,

  GET_AGENT_DETAILS_WITH_STATUS: `
    SELECT 
      profile.*,
      status.name AS "currentStatusName",
      login.id AS "login.id",
      login."profileId" AS "login.profileId",
      login.username AS "login.username",
      login."lastLogin" AS "login.lastLogin",
      login."loginCount" AS "login.loginCount",
      login."isActivated" AS "login.isActivated",
      login."statusId" AS "login.statusId",
      login."createdBy" AS "login.createdBy",
      login."createdOn" AS "login.createdOn",
      login."modifiedBy" AS "login.modifiedBy",
      login."modifiedOn" AS "login.modifiedOn",
      login."accountType" AS "login.accountType",
      login.otp AS "login.otp",
      login."expireOn" AS "login.expireOn",
      login."accountId" AS "login.accountId",
            acc."name"
    FROM ${TABLE.PROFILE_TABLE} AS profile
    LEFT JOIN ${TABLE.LOGIN_TABLE} AS login
      ON login."profileId" = profile.id
    LEFT JOIN ${TABLE.STATUSES} AS status
      ON status.id = profile."statusId"
    LEFT JOIN ${TABLE.AGENCIES} AS acc
      ON acc."profileId" = profile."id"
    WHERE profile."statusId" = $1
      AND profile."accountType" = ANY($2)
    ORDER BY profile."createdOn" DESC;
  `,

  GET_AGENT_DETAILS: `
      SELECT 
        profile.*,
        status.name AS "currentStatusName",
        login.id AS "login.id",
        login."profileId" AS "login.profileId",
        login.username AS "login.username",
        login."lastLogin" AS "login.lastLogin",
        login."loginCount" AS "login.loginCount",
        login."isActivated" AS "login.isActivated",
        login."statusId" AS "login.statusId",
        login."createdBy" AS "login.createdBy",
        login."createdOn" AS "login.createdOn",
        login."modifiedBy" AS "login.modifiedBy",
        login."modifiedOn" AS "login.modifiedOn",
        login."accountType" AS "login.accountType",
        login.otp AS "login.otp",
        login."expireOn" AS "login.expireOn",
        login."accountId" AS "login.accountId",
        acc."name"
      FROM ${TABLE.PROFILE_TABLE} AS profile
      LEFT JOIN ${TABLE.LOGIN_TABLE} AS login
        ON login."profileId" = profile.id
      LEFT JOIN ${TABLE.STATUSES} AS status
        ON status.id = profile."statusId"
      LEFT JOIN ${TABLE.AGENCIES} AS acc
        ON acc."profileId" = profile."id"
      WHERE profile."accountType" = ANY($1)
      ORDER BY profile."createdOn" DESC;
    `,

  GET_APPROVED_AGENT_DETAILS_WITH_STATUS: `
    SELECT 
      profile.*,
      status.name AS "currentStatusName",
      login.id AS "login.id",
      login."profileId" AS "login.profileId",
      login.username AS "login.username",
      login."lastLogin" AS "login.lastLogin",
      login."loginCount" AS "login.loginCount",
      login."isActivated" AS "login.isActivated",
      login."statusId" AS "login.statusId",
      login."createdBy" AS "login.createdBy",
      login."createdOn" AS "login.createdOn",
      login."modifiedBy" AS "login.modifiedBy",
      login."modifiedOn" AS "login.modifiedOn",
      login."accountType" AS "login.accountType",
      login.otp AS "login.otp",
      login."expireOn" AS "login.expireOn",
      login."accountId" AS "login.accountId",
      acc."name"
    FROM ${TABLE.PROFILE_TABLE} AS profile
    LEFT JOIN ${TABLE.LOGIN_TABLE} AS login
      ON login."profileId" = profile.id
    LEFT JOIN ${TABLE.STATUSES} AS status
      ON status.id = profile."statusId"
    LEFT JOIN ${TABLE.AGENCIES} AS acc
      ON acc."profileId" = profile."id"
    WHERE profile."statusId" = $1
    AND profile."accountType" = ANY($2)
    ORDER BY profile."createdOn" DESC;
  `,

  GET_APPROVED_AGENT_DETAILS: `
    SELECT 
      profile.*,
      status.name AS "currentStatusName",
      login.id AS "login.id",
      login."profileId" AS "login.profileId",
      login.username AS "login.username",
      login."lastLogin" AS "login.lastLogin",
      login."loginCount" AS "login.loginCount",
      login."isActivated" AS "login.isActivated",
      login."statusId" AS "login.statusId",
      login."createdBy" AS "login.createdBy",
      login."createdOn" AS "login.createdOn",
      login."modifiedBy" AS "login.modifiedBy",
      login."modifiedOn" AS "login.modifiedOn",
      login."accountType" AS "login.accountType",
      login.otp AS "login.otp",
      login."expireOn" AS "login.expireOn",
      login."accountId" AS "login.accountId"
    FROM ${TABLE.PROFILE_TABLE} AS profile
    LEFT JOIN ${TABLE.LOGIN_TABLE} AS login
      ON login."profileId" = profile.id
    LEFT JOIN ${TABLE.STATUSES} AS status
      ON status.id = profile."statusId"
    WHERE profile."accountType" = ANY($1)
    ORDER BY profile."createdOn" DESC;
  `,

  GET_DOCUMENT_DETAILS_BY_PROFILE_ID: `SELECT 
    prf.id AS "profile_Id",
    prf.*,
    prf."accountType" AS "profileAccountType",
    status.name AS "currentStatusName",
    login.*,
    ad.*,
    acc.*,
    agn.*,
    COALESCE(
      json_agg(DISTINCT ls_main.*) FILTER (WHERE ls_main.id IS NOT NULL AND ls_main."parentId" IS NULL), 
      '[]'
    ) AS industry_mission,
    COALESCE(
      json_agg(DISTINCT ls_sub.*) FILTER (WHERE ls_sub.id IS NOT NULL AND ls_sub."parentId" IS NOT NULL), 
      '[]'
    ) AS industry_subcategory

  FROM ${TABLE.PROFILE_TABLE} prf

  LEFT JOIN ${TABLE.LOGIN_TABLE} login ON login."profileId" = prf.id
  LEFT JOIN ${TABLE.AGENT_DETAILS} ad ON ad.profile_id = prf.id
  LEFT JOIN ${TABLE.ACCOUNT} acc ON acc."profileId" = prf.id
  LEFT JOIN ${TABLE.AGENCIES} agn ON agn."profileId" = prf.id
  LEFT JOIN ${TABLE.STATUSES} AS status ON status.id = prf."statusId"

  LEFT JOIN prf.${TABLE.SERVICE} s ON s."profileId" = prf.id
  LEFT JOIN list.${TABLE.SERVICE} ls_main ON ls_main.id = s."serviceId" AND ls_main."parentId" IS NULL
  LEFT JOIN list.${TABLE.SERVICE} ls_sub ON ls_sub.id = s."serviceId" AND ls_sub."parentId" IS NOT NULL

  WHERE prf."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')

  GROUP BY prf.id, login.id, ad.id, acc.id, agn.id, status.name;`,

  GET_COMPANY_AGENT_DETAILS_BY_ID: `
    SELECT 
      prf.*,
      prf."accountType" as "profileAccountType",
      status.name AS "currentStatusName",
      login.*,
      ad.*,
      acc.*,
      agn.*,
      COALESCE(
        json_agg(DISTINCT ls_main.*) FILTER (WHERE ls_main.id IS NOT NULL AND ls_main."parentId" IS NULL), 
        '[]'
      ) AS industry_mission,
      COALESCE(
        json_agg(DISTINCT ls_sub.*) FILTER (WHERE ls_sub.id IS NOT NULL AND ls_sub."parentId" IS NOT NULL), 
        '[]'
      ) AS industry_subcategory

      FROM ${TABLE.PROFILE_TABLE} prf

      LEFT JOIN ${TABLE.LOGIN_TABLE} login ON login."profileId" = prf.id
      LEFT JOIN ${TABLE.AGENT_DETAILS} ad ON ad.profile_id = prf.id
      LEFT JOIN ${TABLE.ACCOUNT} acc ON acc."profileId" = prf.id
      LEFT JOIN ${TABLE.AGENCIES} agn ON agn."profileId" = prf.id
      LEFT JOIN ${TABLE.STATUSES} AS status ON status.id = prf."statusId"

      LEFT JOIN prf.${TABLE.SERVICE} s ON s."profileId" = prf.id
      LEFT JOIN list.${TABLE.SERVICE} ls_main ON ls_main.id = s."serviceId" AND ls_main."parentId" IS NULL
      LEFT JOIN list.${TABLE.SERVICE} ls_sub ON ls_sub.id = s."serviceId" AND ls_sub."parentId" IS NOT NULL

      WHERE prf."id" = $1
      GROUP BY prf.id, login.id, ad.id, acc.id, agn.id, status.name;
  `,

  GET_INDIVIDUAL_AGENYT_DETAILS_BY_ID: `
    SELECT 
      prf.*,
      prf."accountType" as "profileAccountType",
      status.name AS "currentStatusName",
      login.*,
      ad.*,
      acc.*,
      COALESCE(
        json_agg(DISTINCT ls_main.*) FILTER (WHERE ls_main.id IS NOT NULL AND ls_main."parentId" IS NULL), 
        '[]'
      ) AS industry_mission,
      COALESCE(
        json_agg(DISTINCT ls_sub.*) FILTER (WHERE ls_sub.id IS NOT NULL AND ls_sub."parentId" IS NOT NULL), 
        '[]'
      ) AS industry_subcategory

    FROM prf.profile prf

    LEFT JOIN ${TABLE.LOGIN_TABLE} login ON login."profileId" = prf.id
    LEFT JOIN ${TABLE.AGENT_DETAILS} ad ON ad.profile_id = prf.id
    LEFT JOIN ${TABLE.ACCOUNT} acc ON acc."profileId" = prf.id
    LEFT JOIN ${TABLE.STATUSES} AS status ON status.id = prf."statusId"

    LEFT JOIN prf.${TABLE.SERVICE} s ON s."profileId" = prf.id
    LEFT JOIN list.${TABLE.SERVICE} ls_main ON ls_main.id = s."serviceId" AND ls_main."parentId" IS NULL
    LEFT JOIN list.${TABLE.SERVICE} ls_sub ON ls_sub.id = s."serviceId" AND ls_sub."parentId" IS NOT NULL

    WHERE prf."id" = $1 AND prf."accountType" = 'Individual'

    GROUP BY prf.id, login.id, ad.id, acc.id, status.name;
  `,

  DASHBOARD_AGENT_APPLICATIONS_COUNT: `
    SELECT 
      CASE 
        WHEN profile."accountType" = 'Individual' THEN 'agents'
        WHEN profile."accountType" = 'Company/Agency/PropertyDeveloper' THEN 'agencies'
      END AS type,
      COUNT(*) AS count
    FROM profile AS profile
    WHERE profile."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')
    AND (
      $1::int IS NULL OR profile."statusId" = $1::int
    )
    GROUP BY type;`,

  DASHBOARD_AGENT_APPLICATIONS_COUNT_WITH_FILTER: `SELECT 
    CASE 
    WHEN profile."accountType" = 'Individual' THEN 'agents'
    WHEN profile."accountType" = 'Company/Agency/PropertyDeveloper' THEN 'agencies'
    END AS type,
    COUNT(*) AS count
    FROM ${TABLE.PROFILE_TABLE} AS profile
    WHERE profile."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')
    AND (
      ($1::timestamp IS NOT NULL AND $2::timestamp IS NOT NULL AND profile."createdOn" BETWEEN $1::timestamp AND $2::timestamp)
      OR ($1::timestamp IS NULL OR $2::timestamp IS NULL)
    )
    AND (
      $3::int IS NULL OR profile."statusId" = $3::int
    )
    GROUP BY profile."accountType";`,

  GET_PROFILES_COUNT_STATUS_WISE: `
    SELECT
      prf."statusId",
      st."name" AS "statusName",
      COUNT(*) AS "profileCount"
    FROM ${TABLE.PROFILE_TABLE} prf
    LEFT JOIN ${TABLE.STATUS} st ON st.id = prf."statusId"
    WHERE prf."accountType" = ANY($1)
    AND ($2::int IS NULL OR prf."statusId" = $2)
    GROUP BY prf."statusId", st."name";`,
};
