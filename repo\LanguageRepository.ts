import db from "../config/database";
import { LanguageDTO } from "../dto/language/LanguageDTO";
import { UserQueries } from "../utils/database/queries/LanguageQueries";

export class LanguageRepository {

  async findAll(): Promise<LanguageDTO[]> {
    return (await db.query(UserQueries.QUERY_FIND_ALL)).rows;
  }

  async findById(id: number): Promise<LanguageDTO> {
    const result = await db.query(UserQueries.QUERY_FIND_BY_ID, [id]);
    return result.rows[0];
  }

  async updateName(id: number, name: string): Promise<void> {
    db.query(UserQueries.QUERY_UPDATE_NAME, [name, id]);
  }
}