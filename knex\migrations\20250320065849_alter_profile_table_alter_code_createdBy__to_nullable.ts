import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("prf.profile", (table) => {
    table.string("code").nullable().alter();
    table.integer("createdBy").nullable().alter();
    table.string("firstName").nullable().alter();
    table.string("lastName").nullable().alter();
    table.integer("locationId").nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("prf.profile", (table) => {
    table.string("code").notNullable().alter();
    table.integer("createdBy").notNullable().alter();
    table.string("firstName").notNullable().alter();
    table.string("lastName").notNullable().alter();
    table.integer("locationId").notNullable().alter();
  });
}
