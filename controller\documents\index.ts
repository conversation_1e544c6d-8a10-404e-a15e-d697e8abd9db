import { Request, Response } from "express";
import { errorResponse, response, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import db from "../../config/database";
import fs from "fs/promises";
import { DOCUMENTS } from "../../utils/database/queries/documents";
import { uploadFileToS3 } from "../../utils/services/s3-bucket";
import { promisify } from "util";
import { AUTH } from "../../utils/database/queries/auth";

const unlinkAsync = promisify(fs.unlink);

// Create a new role
export const createDocument = asyncHandler(
  async (req: Request, res: Response) => {
    const profileId = req.user?.id;
    const { keyValue } = req.body;

    const { rows } = await db.query(AUTH.SELECT_BY_ID, [profileId]);
    const loginRes = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [profileId]);
    const loginId = loginRes.rows[0].id;


    if (!rows.length) {
      return errorResponse(res, "Profile data not found");
    }

    let userType = "";

    if (rows[0].accountType === "Individual") {
      userType = "agent";
    } else if (rows[0].accountType === "Company/Agency/PropertyDeveloper") {
      userType = "agency";
    } else {
      userType = "user";
    }
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.status(400).json({ message: "No files uploaded" });
    }

    const results = await Promise.all(
      files.map((file) =>
        uploadFileToS3(file.filename, file, userType, keyValue)
      )
    );

    const failedUploads = results.filter((r) => !r || !r.fileKey);

    if (failedUploads.length > 0) {
      await Promise.all(
        files.map((file) => unlinkAsync(`public/documents/${file.filename}`))
      );
      return res.status(500).json({ message: "Some images failed to upload." });
    }

    const statusNames = ["Verified"];

    const approvedStatus = await db.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );

    // Save uploaded image metadata in DB
    for (const result of results) {
      const { fileKey, fileName, size } = result;

      const url = fileKey;

      const fields: any = {
        profileId,
        url: url,
        title: fileName,
        statusId: approvedStatus?.rows[0]?.id,
        // documentType: keyValue,
        size,
        createdBy: Number(loginId),
      };

      const { query, values } = DOCUMENTS.CREATE_DOCUMENTS(fields);
      await db.query(query, values);
    }

    return responseData(res, 200, "Document uploaded", results);
  }
);

// Get a role by ID
export const deleteDocument = asyncHandler(
  async (req: Request, res: Response) => {
    const { documentIds } = req.body;

    const ids = Object.values(documentIds);

    try {
      for (const id of ids) {
        const { rows } = await db.query(DOCUMENTS.GET_DOCUMENTS_BY_ID, [
          id,
          false,
        ]);

        if (!rows.length) {
          return res
            .status(404)
            .json({ message: `Document with ID ${id} not found` });
        }

        const fields = {
          isDeleted: 1,
        };

        const { query, values } = DOCUMENTS.UPDATE_DOCUMENTS(
          fields,
          Number(id)
        );
        await db.query(query, values);
      }

      return response(res, 200, "Document deleted successfully");
    } catch (error) {
      console.error("Error updating documents:", error);
      return res.status(500).json({ message: "Something went wrong" });
    }
  }
);

// Update a role
export const verifiedDocument = asyncHandler(
  async (req: Request, res: Response) => {
    const { documentIds } = req.body;

    const ids = Object.values(documentIds);

    try {
      for (const id of ids) {
        const { rows } = await db.query(DOCUMENTS.GET_DOCUMENTS_BY_ID, [
          id,
          false,
        ]);

        if (!rows.length) {
          return res
            .status(404)
            .json({ message: `Document with ID ${id} not found` });
        }

        const fields = {
          isVerified: !rows[0].isVerified, // Toggle the isVerified status
        };

        const { query, values } = DOCUMENTS.UPDATE_DOCUMENTS(
          fields,
          Number(id)
        );
        await db.query(query, values);
      }

      return response(res, 200, "Document verified successfully");
    } catch (error) {
      console.error("Error updating documents:", error);
      return res.status(500).json({ message: "Something went wrong" });
    }
  }
);




export const createDocumentByAdmin = asyncHandler(
  async (req: Request, res: Response) => {
   
    const { keyValue } = req.body;
 
    // const { rows } = await db.query(AUTH.SELECT_BY_ID, [profileId]);
    // const loginRes = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [profileId]);
    // const loginId = loginRes.rows[0].id;


    // if (!rows.length) {
    //   return errorResponse(res, "Profile data not found");
    // }

    let userType = "users";

    // if (rows[0].accountType === "Individual") {
    //   userType = "agent";
    // } else if (rows[0].accountType === "Company/Agency/PropertyDeveloper") {
    //   userType = "agency";
    // } else {
    //   userType = "user";
    // }
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.status(400).json({ message: "No files uploaded" });
    }

    const results = await Promise.all(
      files.map((file) =>
        uploadFileToS3(file.filename, file, userType, keyValue)
      )
    );
    // console.log("results >>>>>   >>> ",results)

    const failedUploads = results.filter((r) => !r || !r.fileKey);

    if (failedUploads.length > 0) {
      await Promise.all(
        files.map((file) => unlinkAsync(`public/documents/${file.filename}`))
      );
      return res.status(500).json({ message: "Some images failed to upload." });
    }

    const statusNames = ["Verified"];

    const approvedStatus = await db.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );

    // Save uploaded image metadata in DB
    for (const result of results) {
      const { fileKey, fileName, size } = result;

      const url = fileKey;

      const fields: any = {
        profileId:1,
        url: url,
        title: fileName,
        statusId: approvedStatus?.rows[0]?.id,
        // documentType: keyValue,
        size,
        createdBy:1,
      };

      const { query, values } = DOCUMENTS.CREATE_DOCUMENTS(fields);
      await db.query(query, values);
    }

    return responseData(res, 200, "Document uploaded", results);
  }
);