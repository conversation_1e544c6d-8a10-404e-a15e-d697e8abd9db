import { Request, Response } from "express";
import { errorCatchResponse, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import { LanguageService } from "../../service/LanguageService";
import { LanguageDTO } from "../../dto/language/LanguageDTO";
import { UpdateLanguageDTO } from "../../dto/language/UpdateLanguageDTO";

export class LanguageController {

    private languageService = new LanguageService();

    getAllLanguages = asyncHandler(
        async (req: Request, res: Response) => {
            try {
                const result: LanguageDTO[] = await this.languageService.getAllLanguages();
                return responseData(res, 200, "Languages fetched successfully", result);
            } catch (error) {
                console.error("Error fetching languages:", error);
                return errorCatchResponse(res, "Something went wrong");
            }
        }
    );

    updateLanguage = asyncHandler(
        async (req: Request, res: Response) => {
            try {
                const data: UpdateLanguageDTO = req.body as UpdateLanguageDTO;
                await this.languageService.updateLanguage(data);
                return responseData(res, 201, "language updated successfully");
            } catch (error) {
                console.error("Error creating languages:", error);
                return errorCatchResponse(res, "Something went wrong");
            }
        }
    );

}