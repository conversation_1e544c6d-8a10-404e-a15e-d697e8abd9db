import { TABLE } from "../table";

export const DOCUMENTS = {

  CREATE_DOCUMENTS: (fields: { [key: string]: any }) => {
    const keys = Object.keys(fields).map((key) => `"${key}"`).join(", ");
    const values = Object.values(fields);
    const placeholders = values.map((_, index) => `$${index + 1}`).join(", ");

    return {
      query: `INSERT INTO prf.images (${keys}) VALUES (${placeholders}) RETURNING *;`,
      values,
    };
  },

  // CREATE_DOCUMENTS: (fields: { [key: string]: any }) => {
  //   const keys = Object.keys(fields).map((key) => `"${key}"`).join(", ");
  //   const values = Object.values(fields);
  //   const placeholders = values.map((_, index) => `$${index + 1}`).join(", ");

  //   return {
  //     query: `INSERT INTO prf.documents (${keys}) VALUES (${placeholders}) RETURNING *;`,
  //     values,
  //   };
  // },

  UPDATE_DOCUMENTS: (fields: { [key: string]: any }, id: number) => {
    const keys = Object.keys(fields);
    const values = Object.values(fields);

    const setClause = keys
      .map((key, index) => `"${key}" = $${index + 1}`)
      .join(", ");

    return {
      query: `UPDATE prf.images SET ${setClause} WHERE id = $${keys.length + 1
        } RETURNING *;`,
      values: [...values, id],
    };
  },

  DELETE_DOCUMENT_BY_ID: `DELETE FROM prf.images WHERE id = $1`,

  GET_DOCUMENTS_BY_ID: `SELECT url FROM prf.images WHERE id = $1 AND "isDeleted" = $2`,

  GET_AGENTS_AND_AGENCIES_DOCUMENTS_WITH_STATUS: `
    SELECT 
    prf.*,
    prf."accountType" as "profileAccountType",
    status.name AS "currentStatusName",
    login.*,
    ad.*,
    acc.*,
    agn.*,

    COALESCE(
      json_agg(
        json_build_object(
          'id', img.id,
          'url', img.url,
          'title', img.title,
          'createdOn', img."createdOn",
          'size', img.size,
          'statusId', img."statusId",
          'statusName', img_status.name
        )
      ) FILTER (WHERE img.id IS NOT NULL), 
      '[]'
    ) AS final_images

  FROM ${TABLE.PROFILE_TABLE} prf

  LEFT JOIN ${TABLE.LOGIN_TABLE} login ON login."profileId" = prf.id
  LEFT JOIN ${TABLE.AGENT_DETAILS} ad ON ad.profile_id = prf.id
  LEFT JOIN ${TABLE.ACCOUNT} acc ON acc."profileId" = prf.id
  LEFT JOIN ${TABLE.AGENCIES} agn ON agn."profileId" = prf.id
  LEFT JOIN ${TABLE.STATUSES} AS status ON status.id = prf."statusId"

  LEFT JOIN prf.images img ON img."profileId" = prf.id AND img."isFinal" = TRUE AND img."statusId" = $1
  LEFT JOIN ${TABLE.STATUSES} AS img_status ON img_status.id = img."statusId"

  WHERE prf."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')

  GROUP BY prf.id, login.id, ad.id, acc.id, agn.id, status.name;`,

  GET_AGENTS_AND_AGENCIES_DOCUMENTS: `
    SELECT 
    prf.*,
    prf."accountType" as "profileAccountType",
    status.name AS "currentStatusName",
    
    login.*,
    ad.*,
    acc.*,
    agn.*,

    COALESCE(
      json_agg(
        json_build_object(
          'id', img.id,
          'url', img.url,
          'title', img.title,
          'createdOn', img."createdOn",
          'size', img.size,
          'statusId', img."statusId",
          'statusName', img_status.name
        )
      ) FILTER (WHERE img.id IS NOT NULL), 
      '[]'
    ) AS final_images

  FROM ${TABLE.PROFILE_TABLE} prf

  LEFT JOIN ${TABLE.LOGIN_TABLE} login ON login."profileId" = prf.id
  LEFT JOIN ${TABLE.AGENT_DETAILS} ad ON ad.profile_id = prf.id
  LEFT JOIN ${TABLE.ACCOUNT} acc ON acc."profileId" = prf.id
  LEFT JOIN ${TABLE.AGENCIES} agn ON agn."profileId" = prf.id
  LEFT JOIN ${TABLE.STATUSES} AS status ON status.id = prf."statusId"

  LEFT JOIN prf.images img ON img."profileId" = prf.id AND img."isFinal" = TRUE
  LEFT JOIN ${TABLE.STATUSES} AS img_status ON img_status.id = img."statusId"

  WHERE prf."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')

  GROUP BY prf.id, login.id, ad.id, acc.id, agn.id, status.name;`,

  GET_DOCUMENTS_COUNTS: `
    SELECT
      img."statusId",
      st."name" AS "statusName",
      COUNT(*) AS "mediaCount"
    FROM prf.images img
    -- Inner join ensures image must be tied to a matching profile
    INNER JOIN ${TABLE.PROFILE_TABLE} prf ON prf.id = img."profileId"
    LEFT JOIN ${TABLE.STATUSES} st ON st.id = img."statusId"
    WHERE img."isFinal" = TRUE
      AND prf."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')
    GROUP BY img."statusId", st."name";`,

  GET_DOCUMENTS_COUNTS_WITH_STATUS: `
    SELECT
      img."statusId",
      st."name" AS "statusName",
      COUNT(*) AS "mediaCount"
    FROM prf.images img
    INNER JOIN ${TABLE.PROFILE_TABLE} prf ON prf.id = img."profileId"
    LEFT JOIN ${TABLE.STATUSES} st ON st.id = img."statusId"
    WHERE img."isFinal" = TRUE
      AND img."statusId" = $1
      AND prf."accountType" IN ('Individual', 'Company/Agency/PropertyDeveloper')
    GROUP BY img."statusId", st."name";`,
};
