CREATE SCHEMA IF NOT EXISTS sec;

DROP TABLE IF EXISTS "sec"."login";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "sec"."login" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "username" text NOT NULL,
    "passwordHash" text NOT NULL,
    "lastLogin" timestamptz,
    "loginCount" int4 DEFAULT 0,
    "isActivated" bool NOT NULL DEFAULT false,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_login_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16627_pk__login__3214ec07a0c2ae5e ON sec.login USING btree (id);


DROP TABLE IF EXISTS "sec"."auditlogs";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "sec"."auditlogs" (
    "id" SERIAL PRIMARY KEY,
    "userId" int4 NOT NULL,
    "action" text NOT NULL,
    "timestamp" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_auditlogs_user" FOREIGN KEY ("userId") REFERENCES "sec"."login"("id") ON DELETE CASCADE
);


-- Indices
CREATE UNIQUE INDEX idx_16620_pk__auditlogs__3214ec07cdc8522c ON sec."auditlogs" USING btree (id);

DROP TABLE IF EXISTS "sec"."roles";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "sec"."roles" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "descriptions" text NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz
);

-- Indices
CREATE UNIQUE INDEX idx_16651_pk__roles__3214ec07e2af88fd ON sec.roles USING btree (id);


DROP TABLE IF EXISTS "sec"."loginrole";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "sec"."loginrole" (
    "id" SERIAL PRIMARY KEY,
    "loginId" int4 NOT NULL,
    "roleId" int4 NOT NULL,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_loginRole_login" FOREIGN KEY ("loginId") REFERENCES "sec"."login"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_loginRole_role" FOREIGN KEY ("roleId") REFERENCES "sec"."roles"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_loginRole_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16635_pk__loginrole__3214ec076162d94f ON sec."loginrole" USING btree (id);

DROP TABLE IF EXISTS "sec"."logintoken";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "sec"."logintoken" (
    "id" SERIAL PRIMARY KEY,
    "userId" int4 NOT NULL,
    "token" text NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "expiresOn" timestamptz NOT NULL,
    "statusId" int4,
    CONSTRAINT "fk_loginToken_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_loginToken_user" FOREIGN KEY ("userId") REFERENCES "sec"."login"("id") ON DELETE CASCADE
);


-- Indices
CREATE UNIQUE INDEX idx_16639_pk__logintoken__3214ec0703d3a1ad ON sec."logintoken" USING btree (id);

DROP TABLE IF EXISTS "sec"."notifications";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "sec"."notifications" (
    "id" SERIAL PRIMARY KEY,
    "userId" int4 NOT NULL,
    "message" text NOT NULL,
    "statusId" int4,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "createdBy" int4 NOT NULL,
    CONSTRAINT "fk_notifications_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_notifications_user" FOREIGN KEY ("userId") REFERENCES "sec"."login"("id") ON DELETE CASCADE
);


-- Indices
CREATE UNIQUE INDEX idx_16645_pk__notifications__3214ec076a6d5da8 ON sec.notifications USING btree (id);
