import { User } from "../../types/user";
import { TABLE } from "../table";

export const USERS = {
  GET_ALL_USERS: `SELECT 
    u.id, u."userName", u."fullName", u."email", u."role", u."profileImage", 
    u."isVerified", u."isActive", u."status", u."isSubscribed",
    ud.designation, ud."shortDescription", ud.description, ud.specialization, 
    ud.experience, ud.languages, ud.industry, ud."accountType", ud.association, 
    ud.certified, ud."issuedBy", ud."certificateNumber", ud."expiryDate", 
    ud."contactNumber", ud."whatsappContact", ud."contactEmail", 
    ud."cardHolderName", ud."cardType", ud."cardNumber", ud."userId"
  FROM "${TABLE.USER}" u
  LEFT JOIN "${TABLE.USERDETAIL}" ud ON3 u.id = ud."userId"
  WHERE u.role = $1 
  LIMIT $2 OFFSET $3`,

  USER_DETAIL_BY_ID: `SELECT 
    u.id, u."userName", u."fullName", u."phoneNumber", u."email", u."role", u."profileImage", 
    u."isVerified", u."isActive", u."status", u."isSubscribed",
    ud.designation, ud."shortDescription", ud.description, ud.specialization, 
    ud.experience, ud.languages, ud.industry, ud."accountType", ud.association, 
    ud.certified, ud."issuedBy", ud."certificateNumber", ud."expiryDate", 
    ud."contactNumber", ud."whatsappContact", ud."contactEmail", 
    ud."cardHolderName", ud."cardType", ud."cardNumber", ud."userId"
  FROM "${TABLE.USER}" u
  LEFT JOIN "${TABLE.USERDETAIL}" ud ON u.id = ud."userId"
  WHERE u.id = $1`,

  GET_TOTAL_USERS: `SELECT COUNT(*) AS total_users FROM "${TABLE.USER}" WHERE role = $1`,

  USER_DETAIL_BY_EMAIL: `SELECT 
    u.id, u."userName", u."fullName", u."phoneNumber", u."email", u."role", u."profileImage", 
    u."isVerified", u."isActive", u."status", u."isSubscribed",
    ud.designation, ud."shortDescription", ud.description, ud.specialization, 
    ud.experience, ud.languages, ud.industry, ud."accountType", ud.association, 
    ud.certified, ud."issuedBy", ud."certificateNumber", ud."expiryDate", 
    ud."contactNumber", ud."whatsappContact", ud."contactEmail", 
    ud."cardHolderName", ud."cardType", ud."cardNumber", ud."userId"
  FROM "${TABLE.USER}" u
  LEFT JOIN "${TABLE.USERDETAIL}" ud ON u.id = ud."userId"
  WHERE u.email = $1`,

  USER_DETAIL_WITH_PASSWORD: `SELECT * FROM "${TABLE.USER}" WHERE email = $1`,

  CREATE_USER: (fields: Record<string, any>) => {
    const columns = Object.keys(fields).map((col) => `"${col}"`).join(", ");
    const values = Object.keys(fields)
      .map((_, index) => `$${index + 1}`)
      .join(", ");

    return `
      INSERT INTO "${TABLE.USER}" (${columns}) 
      VALUES (${values}) 
      RETURNING id, "userName", "phoneNumber", "email";
    `;
  },

  // Dynamic UPDATE_USER query that can update any provided fields
  UPDATE_USER: (fields: Record<string, any>) => {
    const setClause = Object.keys(fields)
      .filter((key) => key !== "id")
      .map((key, index) => `"${key}" = $${index + 1}`)
      .join(", ");

    return `
      UPDATE "${TABLE.USER}" 
      SET ${setClause} 
      WHERE id = $${Object.keys(fields).length} 
      RETURNING *;
    `;
  },

  DELETE_USER: `DELETE FROM "${TABLE.USER}" WHERE id = $1`,
  GET_USER_BY_ID: `SELECT * FROM "${TABLE.USER}" WHERE id =$1`,
  GET_USER_BY_USERNAME: `SELECT * FROM "${TABLE.USER}" WHERE "userName" = $1 AND "id" <> $2`,
};
