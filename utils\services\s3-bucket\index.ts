import { S3Client, DeleteObjectCommand } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import fs from "fs";
import { promisify } from "util";

const unlinkAsync = promisify(fs.unlink);

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

interface UploadResult {
  fileName: string;
  fileKey: string;
  size: number;
  mimeType: string;
}

export const uploadFileToS3 = async (
  filePath: string,
  file: Express.Multer.File,
  userType: string,
  keyValue: string
): Promise<UploadResult> => {
  let folder = "others/";
  if (file.mimetype.startsWith("image/")) {
    folder = `images/${userType}/verifications/`;
  } else if (
    file.mimetype.startsWith("application/") ||
    file.mimetype.startsWith("text/")
  ) {
    folder = `documents/${userType}/verifications/`;
  }

  const fileKey = `${folder}${keyValue}-${Date.now()}.${file.mimetype.split("/")[1]}`;
  // Use a file stream instead of buffer
  const fileStream = fs.createReadStream(`public/documents/${filePath}`);

  // Use the Upload class for better handling of file uploads
  const upload = new Upload({
    client: s3Client,
    params: {
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: fileKey,
      Body: fileStream,
      ContentType: file.mimetype,
    },
  });

  try {
    await upload.done();

    // Delete the local file after upload
    await unlinkAsync(`public/documents/${filePath}`);

    return {
      fileName: file.originalname,
      fileKey: fileKey,
      size: file.size,
      mimeType: file.mimetype,
    };
  } catch (error) {
    console.error("Error uploading file to S3:", error);
    throw error;
  }
};

// Method to delete a file from S3
export const deleteFileFromS3 = async (fileKey: string): Promise<boolean> => {
  const command = new DeleteObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET!,
    Key: fileKey,
  });

  try {
    await s3Client.send(command);
    return true;
  } catch (error) {
    console.error("Error deleting file from S3:", error);
    return false;
  }
};
