import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('prf.profile', (table) => {
    table.string('accountType').nullable();
    table.boolean('association').notNullable().defaultTo(false);
    table.string('issuedBy').nullable();
    table.string('profileImage').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('prf.profile', (table) => {
    table.dropColumn('accountType');
    table.dropColumn('association');
    table.dropColumn('issuedBy');
    table.dropColumn('profileImage');
  });
}
