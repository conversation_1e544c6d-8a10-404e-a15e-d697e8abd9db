/agent/auth/login:
  post:
    tags:
      - Auth
    summary: Login Agent
    description: Logs in a user and sets authToken cookie
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - email
              - password
            properties:
              email:
                type: string
                example: d<PERSON><PERSON><PERSON>@mechanicspedia.com
              password:
                type: string
                example: Secure123@
    responses:
      "200":
        description: Login successful, authToken cookie set
        headers:
          Set-Cookie:
            description: JWT authentication token
            schema:
              type: string
              example: authToken=eyJhbGci...; HttpOnly; Path=/; Max-Age=3600
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: number
                success:
                  type: boolean
                message:
                  type: string
