 import type { Knex } from "knex";
 
 


export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('agentlicenses', function(table) {
    table.increments('id').primary(); // Primary key
    table.integer('roleId').notNullable(); // Required
    table.integer('agentId').notNullable(); // Required
    table.specificType('licenseFile', 'text[]').nullable(); // Array of strings
    table.date('licenseexpiryDate').nullable();
    table.string('licenseAuthorityOther').nullable();
    table.string('roletype').nullable();
    table.string('licenseAuthority').nullable();
    table.string('licenseNumber').nullable();
    table.boolean('hasLicense').nullable();
    table.timestamp('timestamp').defaultTo(knex.fn.now()); // Optional created timestamp
  });
};

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('agentlicenses');
};


