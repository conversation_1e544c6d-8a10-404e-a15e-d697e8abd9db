import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('agentlicenses', function(table) {
    table.integer('agencyId').nullable(); // Add agencyId (nullable)
    table.date('licenseIssueDate').nullable(); // Add licenseIssueDate (nullable)
    table.integer('agentId').nullable().alter(); // Make agentId nullable
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('agentlicenses', function(table) {
    table.dropColumn('agencyId');
    table.dropColumn('licenseIssueDate');
    table.integer('agentId').notNullable().alter(); // Revert agentId back to not nullable
  });
}
