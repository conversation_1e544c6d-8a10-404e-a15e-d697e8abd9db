import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agencies', function (table) {
        table.string("issuingAuthorityOther").nullable();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agencies', function (table) {
        table.dropColumn("issuingAuthorityOther");
    });
}