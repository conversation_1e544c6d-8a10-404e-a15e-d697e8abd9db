import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agencies', function (table) {
        table.string('companyEmail').nullable().defaultTo(null);
        table.string('companyPhone').nullable().defaultTo(null);
        table.string('inviteAgents').nullable().defaultTo(null);
        table.string('issuingAuthority').nullable().defaultTo(null);
        table.specificType('passportDoc', 'text[]').nullable().defaultTo(null);
        table.specificType('visaDoc', 'text[]').nullable().defaultTo(null);
        table.specificType('supportingDocsDoc', 'text[]').nullable().defaultTo(null);
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agencies', function (table) {
        table.dropColumn('companyEmail');
        table.dropColumn('companyPhone');
        table.dropColumn('issuingAuthority');
        table.dropColumn('inviteAgents');
        table.dropColumn('passportDoc');
        table.dropColumn('visaDoc');
        table.dropColumn('supportingDocsDoc');
    });
}