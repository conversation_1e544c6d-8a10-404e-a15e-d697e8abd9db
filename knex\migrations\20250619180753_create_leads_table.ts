// migrations/20250619_create_leads.ts
import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("look.leads", (table) => {
    table.increments("id").primary();
    table.string("fullName").notNullable();
    table.string("email").notNullable();
    table.string("phone").nullable();
    table.string("licenseNumber").nullable();
    table.string("company").nullable();
    table.string("leadType").notNullable();
    table.integer("statusId").nullable();
    table.string("source").nullable();
    table.boolean("isConverted").defaultTo(false); // new flag
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("look.leads");
}
