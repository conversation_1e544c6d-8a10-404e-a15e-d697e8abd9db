import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

export const sendVerificationEmailOnLoginAttempt = async (
  username: string,
  email: string,
  res: Response,
  otp: string
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject: "Verify Your Account - FindAnyAgent",
    html: await verificationEmailHTMLTemplate(otp, username),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Verification email sent successfully!");

    response(
      res,
      200,
      "Your account is not verified. A verification email has been sent to your email."
    );
  } catch (error) {
    console.error("Error sending verification email:", error);
    res.status(500).json({
      message: "Unable to send verification email. Please try again later.",
    });
  }
};

export const verificationEmailHTMLTemplate = async (otp: string, username: string) => {
  return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Account</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .header {
            font-size: 22px;
            font-weight: bold;
            color: #5e9b6d;
        }
        .content {
            margin: 20px 0;
            font-size: 16px;
        }
        .otp {
            display: inline-block;
            font-size: 28px;
            font-weight: bold;
            color: #5e9b6d;
            background-color: #eefaf1;
            padding: 12px 24px;
            border-radius: 6px;
            letter-spacing: 3px;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
        }
        .note {
            font-size: 14px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Verify Your Account</div>
        <div class="content">
            <p>Hi ${username},</p>
            <p>It looks like you tried to log in, but your account has not been verified yet.</p>
            <p>Use the OTP below to verify your account and gain access:</p>
            <p class="otp">${otp}</p>
            <p class="note">This OTP is valid for 10 minutes. If you did not request this, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>FindAnyAgent Team</strong></p>
        </div>
    </div>
</body>
</html>`;
};
