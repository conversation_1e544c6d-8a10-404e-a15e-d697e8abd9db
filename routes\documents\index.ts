import { Router } from "express";
import multer from "multer";

import {
  createDocument,
  deleteDocument,
  verifiedDocument,
} from "../../controller/documents";
import { storageData } from "../../utils/services/multer";
import { authMiddleware } from "../../middleware/authMiddleware";

const router = Router();

const upload = storageData("documents")

/**
 * POST /documentUploads
 * Upload and save metadata in DB
 */
router.post("/", authMiddleware, upload.array('document', 10), createDocument);

/**
 * DELETE /documentDeletions/:id
 * Deletes image record and file
 */
router.put("/", upload.none(), deleteDocument);

/**
 * GET /verifyDocument/:id
 * Returns document info (mock verification)
 */
router.patch("/", upload.none(), verifiedDocument);

export default router;
