import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

/**
 * Send a bulk email.
 *
 * @param subject - Email subject line
 * @param message - HTML message body (plain text can be handled inside the template if needed)
 * @param bcc - Array of recipient addresses (sent via BCC)
 * @param res - Express response object for sending HTTP responses
 * @param attachments - Optional array of email attachments
 */

export const sendBulkEmail = async (
    subject: string,
    message: string,
    bcc: string[],
    cc: string[],
    to: string[],
    res: Response,
    attachments?: Array<{ filename: string; content: Buffer }>,
    // attachments: Array<{ filename: string; content: Buffer }>,
): Promise<void> => {
    // Build basic mail options
    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string,
        bcc,
        cc,
        to,
        subject,
        html: await bulkEmailHTMLTemplate(message),
        // attachments
        // "attachments" is included only when a non‑empty array is provided
        ...(attachments?.length ? { attachments } : {}),
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log("Bulk email sent successfully!");
        response(res, 200, "Bulk email sent successfully");
    } catch (error) {
        console.error("Error sending bulk email:", error);
        response(res, 500, "Failed to send bulk email");
    }
};


/*
 * Generates the HTML body for the bulk email.
*/

const bulkEmailHTMLTemplate = async (message: string): Promise<string> => `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FindAnyAgent Message</title>
  </head>
  <body style="margin:0;padding:0;background:#f9f9f9;">
    <!-- ===== Outer wrapper (100% width) ===== -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f9f9f9;">
      <tr>
        <td align="center" style="padding:40px 10px;">
          <!-- ===== Centered card (max 600px) ===== -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0"
                 style="max-width:600px;background:#ffffff;border-radius:8px;
                        box-shadow:0 0 10px rgba(0,0,0,0.1);">
            <tr>
              <td style="padding:24px 32px;font-family:Arial,sans-serif;color:#333333;line-height:1.6;">
                <!-- Header -->
                <h1 style="margin:0 0 20px 0;font-size:22px;color:#5e9b6d;">
                  FindAnyAgent Message
                </h1>

                <!-- Dynamic content -->
                <div style="font-size:16px;">${message}</div>

                <!-- Footer -->
                <p style="margin:32px 0 0 0;font-size:14px;color:#777777;">
                  Best regards,<br />
                  <strong>FindAnyAgent Team</strong>
                </p>
              </td>
            </tr>
          </table>
          <!-- ===== End card ===== -->
        </td>
      </tr>
    </table>
    <!-- ===== End wrapper ===== -->
  </body>
</html>`;

// {
//     return `<html lang="en">
// <head>
//     <meta charset="UTF-8">
//     <meta name="viewport" content="width=device-width, initial-scale=1.0">
//     <title>FindAnyAgent Message</title>
//     <style>
//         body {
//             font-family: Arial, sans-serif;
//             color: #333;
//             background-color: #f9f9f9;
//             margin: 0;
//             padding: 0;
//         }
//         .container {
//             max-width: 600px;
//             /* margin: 40px auto; */
//             background-color: #ffffff;
//             padding: 20px;
//             border-radius: 8px;
//             box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
//         }
//         .header {
//             font-size: 22px;
//             color: #5e9b6d;
//             margin-bottom: 20px;
//             /* text-align: center; */
//         }
//         .content {
//             font-size: 16px;
//             line-height: 1.6;
//             /* white-space: pre-wrap; */
//         }
//         .footer {
//             margin-top: 30px;
//             font-size: 14px;
//             color: #777;
//             /* text-align: center; */
//         }
//     </style>
// </head>
// <body>
//     <div class="container">
//         <div class="header">FindAnyAgent Message</div>
//         <div class="content">
//             ${message}
//         </div>
//         <div class="footer">
//             <p>Best regards,</p>
//             <p><strong>FindAnyAgent Team</strong></p>
//         </div>
//     </div>
// </body>
// </html>`;
// }; 