import express from "express";
import {
  getPackagesByUserType,
} from "../../../controller/admin/globalConfig/subscription";
import { storageData } from "../../../utils/services/multer";
import { cancelSubscription, createSubscriprionWithPackageTypeId, deleteSubscription, getFeatureValuesByUserType } from "../../../controller/agents/subscription.Controller";

const router = express.Router();
const upload = storageData("subscriptions");

// Helper Endpoints
router.get("/packages", getFeatureValuesByUserType); // for table dynamic data
router.post("/packages", upload.none(), createSubscriprionWithPackageTypeId); // for table dynamic data
router.get("/packages/user-type/:userType", getPackagesByUserType); // for packages by user
router.put("/:subscriptionId/cancel", upload.none(), cancelSubscription);
router.delete("/:subscriptionId", upload.none(), deleteSubscription);

export default router;