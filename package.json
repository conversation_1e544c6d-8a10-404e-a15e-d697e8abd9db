{"name": "findanyagent-backend", "version": "1.0.0", "main": "server.ts", "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/lib-storage": "^3.787.0", "acorn": "^8.14.0", "acorn-walk": "^8.3.4", "arg": "^4.1.3", "bcryptjs": "^2.4.3", "concurrently": "^9.1.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "create-require": "^1.1.1", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "diff": "^4.0.2", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.7", "node-cron": "^4.0.7", "nodemailer": "^6.10.0", "pg": "^8.13.2", "slugify": "^1.6.6", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "undici-types": "^6.20.0", "v8-compile-cache-lib": "^3.0.1", "yn": "^3.1.1", "zod": "^3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/csv-parse": "^1.1.12", "@types/express": "^5.0.0", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.7", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.1", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "scripts": {"dev": "concurrently \"nodemon server.ts\" \"docker-compose up\"", "start": "ts-node server.ts", "build": "tsc", "knex": "knex --knexfile knex/knexFile.ts", "migration": "bash scripts/run_migrations.sh"}, "keywords": [], "author": "", "license": "ISC", "description": ""}