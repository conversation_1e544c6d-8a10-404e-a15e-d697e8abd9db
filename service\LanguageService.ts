import { UpdateLanguageDTO } from "../dto/language/UpdateLanguageDTO";
import { LanguageDTO } from "../dto/language/LanguageDTO";
import { LanguageRepository } from "../repo/LanguageRepository";

export class LanguageService {

    private languageRepository = new LanguageRepository();

    async getAllLanguages(): Promise<LanguageDTO[]> {
        return this.languageRepository.findAll();
    }

    async updateLanguage(updateLanguageDTO: UpdateLanguageDTO) {
        const language = await this.languageRepository.findById(updateLanguageDTO.id);
        if (!language) throw new Error("User not found");

        return this.languageRepository.updateName(updateLanguageDTO.id, updateLanguageDTO.name);
    }
}