import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    await knex.schema.table("list.services", (table) => {
        // 1. Make group column nullable
        table.text("group").nullable().alter();

        // 2. Add parentId column, nullable, with foreign key
        table.integer("parentId").nullable();
        table.foreign("parentId").references("id").inTable("list.services").withKeyName("services_parentId_fk");
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.table("list.services", (table) => {
        // Revert parentId: drop foreign key and column
        table.dropForeign("parentId", "services_parentId_fk");
        table.dropColumn("parentId");

        // Revert group: make it not nullable again (assumes no null values exist after rollback)
        table.text("group").notNullable().alter();
    });
}