 
import transporter from ".";  
 

export const generatePassword = () => {
    const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const lower = "abcdefghijklmnopqrstuvwxyz";
    const number = "0123456789";
    const special = "!@#$%^&*()_+-=[]{}|";
  
    const all = upper + lower + number + special;
    const pick = (str: string) => str[Math.floor(Math.random() * str.length)];
   
    const password =
      pick(upper) + pick(lower) + pick(number) + pick(special) +
      Array.from({ length: 4 }, () => pick(all)).join("");
  
    return password
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("");
  }
  
 
  
  export const sendWelcomeEmail = async (name: string, email: string, password: string) => {
    const mailOptions = {
      from: `"Support Team" <${process.env.SMTP_USER}>`,
      to: email,
      subject: "🎉 Your Account Has Been Successfully Created",
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h2 style="color: #2c3e50;">Hi ${name},</h2>
          <p>We're excited to let you know that your account has been successfully created!</p>
          <p>Use the following credentials to log in:</p>
          <ul>
            <li><strong>Email:</strong> ${email}</li>
            <li><strong>Password:</strong> ${password}</li>
          </ul>
          <p>You can log in using the following link:</p>
          <a href="https://dashboard-faa.findanyagent.ae/login" style="color: #3498db;">Login to Your Account</a>
          <br><br>
          <p>If you did not request this, please contact our support team immediately.</p>
          <p>Welcome to FindAnyAgent!<br>FindAnyAgent Support </p>
        </div>
      `,
    };
  
    await transporter.sendMail(mailOptions);
  }



  export const sendBulkEmails = async (name: string, email: string, subject: string, message: string) => {
    const mailOptions = {
      from: `"Support Team" <${process.env.SMTP_USER}>`,
      to: email,
      subject: subject,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h2 style="color: #2c3e50;">Hi ${name},</h2>
          <p>${message}</p>
          <p>Welcome to FindAnyAgent!<br>FindAnyAgent Support </p>
        </div>
      `,
    };
  
    await transporter.sendMail(mailOptions);
  }