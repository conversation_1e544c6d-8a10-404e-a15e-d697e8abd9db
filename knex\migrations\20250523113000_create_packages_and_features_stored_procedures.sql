-- Create stored procedure for packagetype
CREATE OR REPLACE FUNCTION look.sp_packagetype (
  p_fnid integer,
  p_id integer DEFAULT NULL::integer,
  p_name text DEFAULT NULL::text,
  p_description text DEFAULT NULL::text,
  p_statusid integer DEFAULT NULL::integer,
  p_createdby integer DEFAULT NULL::integer,
  p_price numeric DEFAULT NULL::numeric,
  p_currency text DEFAULT NULL::text,
  p_usertype text DEFAULT NULL::text,
  p_colortheme text DEFAULT NULL::text,
  p_sortby text DEFAULT 'id'::text,

-- NEW parameters (added last so old positional calls still work)
  p_stripeplanid  text    DEFAULT NULL::text,
  p_paypalplanid  text    DEFAULT NULL::text
) RETURNS jsonb LANGUAGE plpgsql AS $function$
DECLARE
    v_data JSONB;
    v_title TEXT;
    v_result INT;
    v_type TEXT;
    v_message TEXT;
BEGIN
    IF p_fnid = 0 THEN
        SELECT to_jsonb(t) INTO v_data FROM look.packagetype t WHERE t.id = p_id;
        v_title := 'Package Type - Single Record';
        v_result := 1;
        v_type := 'success';
        v_message := 'Record selected successfully.';
    ELSIF p_fnid = 1 THEN
        WITH sorted_rows AS (
            SELECT to_jsonb(t) AS row_json
            FROM look.packagetype t
            WHERE (p_id IS NULL OR t.id = p_id)
            ORDER BY
                CASE
                    WHEN p_sortby = 'name' THEN t.name
                    ELSE t.id::text
                END
        )
        SELECT jsonb_agg(row_json) INTO v_data FROM sorted_rows;
        v_title := 'Package Type - List Records';
        v_result := 1;
        v_type := 'success';
        v_message := 'Records selected successfully.';
    ELSIF p_fnid = 2 THEN
        IF p_id IS NULL THEN
            INSERT INTO look.packagetype (
                name, description, "statusId", "createdBy", "createdOn",
                price, currency, "userType", "colorTheme",
                "stripePlanId", "payPalPlanId"
            ) VALUES (
                p_name, p_description, p_statusid, p_createdby, NOW(),
                p_price, p_currency, p_usertype, p_colortheme,
                p_stripeplanid, p_paypalplanid
            ) RETURNING to_jsonb(look.packagetype.*) INTO v_data;
            v_title := 'Package Type - Insert';
            v_result := 1;
            v_type := 'success';
            v_message := 'Record inserted successfully.';
        ELSE
            UPDATE look.packagetype
            SET
                name = p_name,
                description = p_description,
                "statusId" = p_statusid,
                "modifiedOn" = NOW(),
                price = p_price,
                currency = p_currency,
                "userType" = p_usertype,
                "colorTheme" = p_colortheme,
                "stripePlanId" = p_stripeplanid,
                "payPalPlanId" = p_paypalplanid
            WHERE id = p_id
            RETURNING to_jsonb(look.packagetype.*) INTO v_data;
            v_title := 'Package Type - Update';
            v_result := 2;
            v_type := 'success';
            v_message := 'Record updated successfully.';
        END IF;
    ELSIF p_fnid = 3 THEN
        DELETE FROM look.packagetype WHERE id = p_id RETURNING to_jsonb(look.packagetype.*) INTO v_data;
        v_title := 'Package Type - Delete';
        v_result := 3;
        v_type := 'success';
        v_message := 'Record deleted successfully.';
    ELSIF p_fnid = 4 THEN
        UPDATE look.packagetype
        SET "statusId" = p_statusid, "modifiedOn" = NOW()
        WHERE id = p_id
        RETURNING to_jsonb(look.packagetype.*) INTO v_data;
        v_title := 'Package Type - Status Update';
        v_result := 2;
        v_type := 'success';
        v_message := 'Status updated successfully.';
    ELSE
        v_title := 'Package Type - Error';
        v_result := -1;
        v_type := 'error';
        v_message := 'Invalid Function ID.';
        v_data := NULL;
    END IF;
    RETURN jsonb_build_object(
        'title', v_title,
        'result', v_result,
        'type', v_type,
        'message', v_message,
        'data', COALESCE(v_data, 'null'::jsonb)
    );
END;
$function$;

-- Create stored procedure for packagefeaturesmeta
CREATE OR REPLACE FUNCTION look.sp_packagefeaturesmeta (
  p_fnid integer,
  p_featureid integer DEFAULT NULL::integer,
  p_featurename text DEFAULT NULL::text,
  p_featuretype text DEFAULT NULL::text,
  p_featureconstant text DEFAULT NULL::text,
  p_displayorder integer DEFAULT NULL::integer,
  p_description text DEFAULT NULL::text,
  p_sortby integer DEFAULT 1::integer
) RETURNS jsonb LANGUAGE plpgsql AS $function$
DECLARE
    v_data JSONB;
    v_title TEXT;
    v_result INT;
    v_type TEXT;
    v_message TEXT;
BEGIN
    IF p_fnid = 0 THEN
        SELECT to_jsonb(t) INTO v_data FROM look.packagefeaturesmeta t WHERE t."featureId" = p_featureid;
        v_title := 'Package Features Meta - Single Record';
        v_result := 1;
        v_type := 'success';
        v_message := 'Record selected successfully.';
    ELSIF p_fnid = 1 THEN        
        WITH sorted_rows AS (
            SELECT to_jsonb(t) AS row_json
            FROM look.packagefeaturesmeta t
            WHERE (p_featureid IS NULL OR t."featureId" = p_featureid)
            ORDER BY
          			CASE p_sortby
                    WHEN 1 THEN t."displayOrder"
                    ELSE t."featureId"
                END NULLS LAST
        )
        SELECT jsonb_agg(row_json) INTO v_data FROM sorted_rows;
        v_title := 'Package Features Meta - List Records';
        v_result := 1;
        v_type := 'success';
        v_message := 'Records selected successfully.';
    ELSIF p_fnid = 2 THEN
        IF p_featureid IS NULL THEN
            INSERT INTO look.packagefeaturesmeta (
                "featureName", "featureType", "featureConstant", "displayOrder", description, "created_at"
            ) VALUES (
                p_featurename, p_featuretype, p_featureconstant, p_displayorder, p_description, NOW()
            ) RETURNING to_jsonb(look.packagefeaturesmeta.*) INTO v_data;
            v_title := 'Package Features Meta - Insert';
            v_result := 1;
            v_type := 'success';
            v_message := 'Record inserted successfully.';
        ELSE
            UPDATE look.packagefeaturesmeta
            SET
                "featureName" = p_featurename,
                "featureType" = p_featuretype,
                "featureConstant" = p_featureconstant,
                "displayOrder" = p_displayorder,
                description = p_description,
                "updated_at" = NOW()
            WHERE "featureId" = p_featureid
            RETURNING to_jsonb(look.packagefeaturesmeta.*) INTO v_data;
            v_title := 'Package Features Meta - Update';
            v_result := 2;
            v_type := 'success';
            v_message := 'Record updated successfully.';
        END IF;
    ELSIF p_fnid = 3 THEN
        DELETE FROM look.packagefeaturesmeta WHERE "featureId" = p_featureid RETURNING to_jsonb(look.packagefeaturesmeta.*) INTO v_data;
        v_title := 'Package Features Meta - Delete';
        v_result := 3;
        v_type := 'success';
        v_message := 'Record deleted successfully.';
    ELSE
        v_title := 'Package Features Meta - Error';
        v_result := -1;
        v_type := 'error';
        v_message := 'Invalid Function ID.';
        v_data := NULL;
    END IF;
    RETURN jsonb_build_object(
        'title', v_title,
        'result', v_result,
        'type', v_type,
        'message', v_message,
        'data', COALESCE(v_data, 'null'::jsonb)
    );
END;
$function$;

-- Create stored procedure for packagefeaturevalues
CREATE OR REPLACE FUNCTION look.sp_packagefeaturevalues (
  p_fnid integer,
  p_packagefeatureid integer DEFAULT NULL::integer,
  p_packagetypeid integer DEFAULT NULL::integer,
  p_featureid integer DEFAULT NULL::integer,
  p_featurevalue text DEFAULT NULL::text
) RETURNS jsonb LANGUAGE plpgsql AS $function$
DECLARE
    v_data JSONB;
    v_title TEXT;
    v_result INT;
    v_type TEXT;
    v_message TEXT;
BEGIN
    IF p_fnid = 0 THEN
        SELECT to_jsonb(t) INTO v_data FROM look.packagefeaturevalues t WHERE t."packageFeatureId" = p_packagefeatureid;
        v_title := 'Package Feature Values - Single Record';
        v_result := 1;
        v_type := 'success';
        v_message := 'Record selected successfully.';
    ELSIF p_fnid = 1 THEN
        SELECT jsonb_agg(to_jsonb(t)) INTO v_data FROM look.packagefeaturevalues t 
        WHERE (p_packagefeatureid IS NULL OR t."packageFeatureId" = p_packagefeatureid)
        AND (p_packagetypeid IS NULL OR t."packageTypeId" = p_packagetypeid)
        AND (p_featureid IS NULL OR t."featureId" = p_featureid);
        v_title := 'Package Feature Values - List Records';
        v_result := 1;
        v_type := 'success';
        v_message := 'Records selected successfully.';
    ELSIF p_fnid = 2 THEN
        IF p_packagefeatureid IS NULL THEN
            INSERT INTO look.packagefeaturevalues (
                "packageTypeId", "featureId", "featureValue", "created_at"
            ) VALUES (
                p_packagetypeid, p_featureid, p_featurevalue, NOW()
            ) RETURNING to_jsonb(look.packagefeaturevalues.*) INTO v_data;
            v_title := 'Package Feature Values - Insert';
            v_result := 1;
            v_type := 'success';
            v_message := 'Record inserted successfully.';
        ELSE
            UPDATE look.packagefeaturevalues
            SET
                "packageTypeId" = p_packagetypeid,
                "featureId" = p_featureid,
                "featureValue" = p_featurevalue,
                "updated_at" = NOW()
            WHERE "packageFeatureId" = p_packagefeatureid
            RETURNING to_jsonb(look.packagefeaturevalues.*) INTO v_data;
            v_title := 'Package Feature Values - Update';
            v_result := 2;
            v_type := 'success';
            v_message := 'Record updated successfully.';
        END IF;
    ELSIF p_fnid = 3 THEN
        DELETE FROM look.packagefeaturevalues WHERE "packageFeatureId" = p_packagefeatureid RETURNING to_jsonb(look.packagefeaturevalues.*) INTO v_data;
        v_title := 'Package Feature Values - Delete';
        v_result := 3;
        v_type := 'success';
        v_message := 'Record deleted successfully.';
    ELSE
        v_title := 'Package Feature Values - Error';
        v_result := -1;
        v_type := 'error';
        v_message := 'Invalid Function ID.';
        v_data := NULL;
    END IF;
    RETURN jsonb_build_object(
        'title', v_title,
        'result', v_result,
        'type', v_type,
        'message', v_message,
        'data', COALESCE(v_data, 'null'::jsonb)
    );
END;
$function$;
