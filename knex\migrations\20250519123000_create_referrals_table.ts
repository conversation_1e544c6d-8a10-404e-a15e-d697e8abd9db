import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    await knex.schema.withSchema('agn').createTable('referrals', (table) => {
        table.increments('id').primary();
        table.integer('userId').unique().notNullable();
        table.string('name').nullable().defaultTo(null);
        table.string('email').nullable().defaultTo(null);
        table.string('contact').nullable().defaultTo(null);
        table.string('referralId').unique().notNullable();
        table.timestamp('createdOn').notNullable().defaultTo(knex.fn.now());
        table.timestamp('updatedOn').notNullable().defaultTo(knex.fn.now());
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.withSchema('agn').dropTableIfExists('referrals');
}