-- ⬇︎  CREATE OR REPLA<PERSON> keeps callers intact
CREATE OR <PERSON><PERSON>LACE FUNCTION "look"."sp_subscription"(
    IN p_fnid              INTEGER,                     -- what to do
    IN p_id                INTEGER            DEFAULT NULL,  -- DB primary key
    IN p_subscriptionid    TEXT               DEFAULT NULL,  -- 🔸 new param
    IN p_package           TEXT               DEFAULT NULL,
    IN p_typeid            INTEGER            DEFAULT NULL,
    IN p_details           TEXT               DEFAULT NULL,
    IN p_statusid          INTEGER            DEFAULT NULL,
    IN p_profileid         INTEGER            DEFAULT NULL,
    IN p_packagetypeid     INTEGER            DEFAULT NULL,
    IN p_startdate         TIMESTAMPTZ        DEFAULT NULL,
    IN p_enddate           TIMESTAMPTZ        DEFAULT NULL,
    IN p_renewaldate       TIMESTAMPTZ        DEFAULT NULL,
    IN p_price             NUMERIC            DEFAULT NULL,
    IN p_currency          VARCHAR            DEFAULT NULL,
    IN p_paymentstatusid   INTEGER            DEFAULT NULL,
    IN p_sortby            TEXT               DEFAULT NULL
) RETURNS JSONB
LANGUAGE plpgsql
AS
$$
DECLARE
    v_data    JSONB;
    v_title   TEXT;
    v_result  INT;
    v_type    TEXT;
    v_message TEXT;
BEGIN
    --------------------------------------------------------------------------
    -- 0 → SINGLE RECORD  (by id OR subscriptionId)
    --------------------------------------------------------------------------
    IF p_fnid = 0 THEN
        SELECT to_jsonb(t) INTO v_data
        FROM   list.subscription t
        WHERE  (p_id IS NOT NULL            AND t.id              = p_id)
            OR (p_subscriptionid IS NOT NULL AND t."subscriptionId" = p_subscriptionid)
        LIMIT 1;

        v_title   := 'Subscription – Single Record';
        v_result  := 1;
        v_type    := 'success';
        v_message := 'Record selected successfully.';

    --------------------------------------------------------------------------
    -- 1 → LIST (optionally filtered by id / subscriptionId)
    --------------------------------------------------------------------------
    ELSIF p_fnid = 1 THEN
        WITH rows AS (
            SELECT to_jsonb(t) AS row_json
            FROM   list.subscription t
            WHERE  (p_id IS NULL            OR t.id              = p_id)
              AND  (p_subscriptionid IS NULL OR t."subscriptionId" = p_subscriptionid)
            ORDER  BY
              CASE p_sortby
                   WHEN 'startDate'      THEN t."startDate"
                   WHEN 'package'        THEN t.package
                   WHEN 'subscriptionId' THEN t."subscriptionId"
                   ELSE t.id::TEXT
              END
        )
        SELECT jsonb_agg(row_json) INTO v_data FROM rows;

        v_title   := 'Subscription – List Records';
        v_result  := 1;
        v_type    := 'success';
        v_message := 'Records selected successfully.';

    --------------------------------------------------------------------------
    -- 2 → INSERT OR UPDATE
    --------------------------------------------------------------------------
    ELSIF p_fnid = 2 THEN
        ----------------------------------------------------------------------
        -- INSERT  (p_id IS NULL)
        ----------------------------------------------------------------------
        IF p_id IS NULL THEN
            INSERT INTO list.subscription (
                "subscriptionId",
                package,
                "typeId",
                details,
                "statusId",
                "createdOn",
                "profileId",
                "packageTypeId",
                "startDate",
                "endDate",
                "renewalDate",
                price,
                currency,
                "paymentStatusId"
            )
            VALUES (
                p_subscriptionid,                -- 🔸 new column
                p_package,
                p_typeid,
                p_details,
                p_statusid,
                NOW(),
                p_profileid,
                p_packagetypeid,
                COALESCE(p_startdate, NOW()),
                p_enddate,
                p_renewaldate,
                COALESCE(p_price, 0.00),
                COALESCE(p_currency, 'AED'),
                p_paymentstatusid
            )
            RETURNING to_jsonb(list.subscription.*) INTO v_data;

            v_title   := 'Subscription – Insert';
            v_result  := 1;
            v_type    := 'success';
            v_message := 'Record inserted successfully.';

        ----------------------------------------------------------------------
        -- UPDATE  (p_id NOT NULL)
        ----------------------------------------------------------------------
        ELSE
            UPDATE list.subscription
            SET
                "subscriptionId"  = COALESCE(p_subscriptionid, "subscriptionId"),
                package           = p_package,
                "typeId"          = p_typeid,
                details           = p_details,
                "statusId"        = p_statusid,
                "modifiedOn"      = NOW(),
                "profileId"       = p_profileid,
                "packageTypeId"   = p_packagetypeid,
                "startDate"       = COALESCE(p_startdate, "startDate"),
                "endDate"         = p_enddate,
                "renewalDate"     = p_renewaldate,
                price             = p_price,
                currency          = p_currency,
                "paymentStatusId" = p_paymentstatusid
            WHERE id = p_id
            RETURNING to_jsonb(list.subscription.*) INTO v_data;

            v_title   := 'Subscription – Update';
            v_result  := 2;
            v_type    := 'success';
            v_message := 'Record updated successfully.';
        END IF;

    --------------------------------------------------------------------------
    -- 3 → DELETE  (by id)
    --------------------------------------------------------------------------
    ELSIF p_fnid = 3 THEN
        DELETE FROM list.subscription
        WHERE id = p_id
        RETURNING to_jsonb(list.subscription.*) INTO v_data;

        v_title   := 'Subscription – Delete';
        v_result  := 3;
        v_type    := 'success';
        v_message := 'Record deleted successfully.';

    --------------------------------------------------------------------------
    -- 4 → CANCEL  (status + endDate)
    --------------------------------------------------------------------------
    ELSIF p_fnid = 4 THEN
        UPDATE list.subscription
        SET
            "statusId"  = p_statusid,
            "endDate"   = NOW(),
            "modifiedOn"= NOW()
        WHERE id = p_id
        RETURNING to_jsonb(list.subscription.*) INTO v_data;

        v_title   := 'Subscription – Cancel';
        v_result  := 4;
        v_type    := 'success';
        v_message := 'Subscription cancelled successfully.';

    --------------------------------------------------------------------------
    -- 5 → STATUS-ONLY UPDATE
    --------------------------------------------------------------------------
    ELSIF p_fnid = 5 THEN
        UPDATE list.subscription
        SET
            "statusId"   = p_statusid,
            "modifiedOn" = NOW()
        WHERE id = p_id
        RETURNING to_jsonb(list.subscription.*) INTO v_data;

        v_title   := 'Subscription – Status Update';
        v_result  := 5;
        v_type    := 'success';
        v_message := 'Status updated successfully.';

    --------------------------------------------------------------------------
    -- INVALID p_fnid
    --------------------------------------------------------------------------
    ELSE
        v_title   := 'Subscription – Error';
        v_result  := -1;
        v_type    := 'error';
        v_message := 'Invalid Function ID.';
        v_data    := NULL;
    END IF;

    --------------------------------------------------------------------------
    -- RETURN CONSISTENT WRAPPER
    --------------------------------------------------------------------------
    RETURN jsonb_build_object(
        'title',   v_title,
        'result',  v_result,
        'type',    v_type,
        'message', v_message,
        'data',    COALESCE(v_data, 'null'::jsonb)
    );
END;
$$;
