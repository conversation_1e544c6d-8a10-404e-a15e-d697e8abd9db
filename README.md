# FindAnyAgent - Developer Onboarding Document

Welcome to the team! This document will guide you through setting up your development environment for the Node.js project.

## Prerequisites
Before starting, ensure you have the following installed:
- **Node.js** (LTS version recommended)
- **Beekeeper Studio** (For viewing and managing the database)
- **Git** (For version control)
- **<PERSON>man** (For API testing)
- **A package manager** (npm)
- **Docker** (Setup the docker and make sure it's running, so we can have the local database running)

## Project Setup
### 1. Clone the Repository
```sh
git clone https://github.com/gfk07/findanyagent-backend.git
cd findanyagent-backend
```

### 2. Install Dependencies
```sh
npm install
```

### 3. Environment Configuration
Create a `.env` file in the root directory and configure environment variables:
```env
PORT=5000
BASE_URL=http://localhost:5000
DATABASE_HOST=localhost
DATABASE_PORT=5440
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=findanyagent
JWT_SECRET=jwtsecretforsecurtiyofappfindanyagent
JWT_EXPIRE=3d
FRONTEND_URL=http://localhost:3000
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL> 
SMTP_PASSWORD=wsfrifizpcocidhq
VERIFICATION_EMAIL=<EMAIL>
```

### 4. Running the Project
Start the development server:
```sh
npm run dev
```

### 5. Database Setup
#### Connect to PostgreSQL Database
1. Open **Beekeeper Studio**.
2. Click on **New Connection**.
3. Select **Import from URL**.
4. Add the following connection URL and hit save connect by providing connection name.
```sh
postgresql://postgres:password@localhost:5440/findanyagent
```

#### Run Migrations
Run the following command to run Knex Migrations:
```sh
npm run migrations
```



Verify the setup by accessing `http://localhost:5000` (or the defined PORT in .env) in your browser or Postman.

### 6. API Testing with Postman
1. Open Postman.
2. Import the provided Postman collection. **[Postman Collection](https://muhammadadil-654095.postman.co/workspace/Muhammad-Adil's-Workspace~59fd69cf-ff4c-4425-8ce9-616253718168/collection/43448717-8e8679d1-a17e-4452-b2e5-fde2c28ab20b?action=share&creator=43448717)** 
3. Set up environment variables matching your `.env` file.
4. Test the API endpoints.

## Code Management & Collaboration
- Always **create a new branch** for each feature or bug fix:
  ```sh
  git checkout -b feature-branch-name
  ```
- Follow the **branch naming convention**: `feature/<name>` or `bugfix/<name>`.
- Commit changes frequently and write **clear commit messages**.
- Push changes and create a **pull request (PR)** for review.
- Follow the **code review process** before merging to `main` or `develop`.

## Additional Notes
- Reach out to the team for any setup or debugging issues.

Welcome aboard! 🎉
