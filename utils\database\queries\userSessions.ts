import { TABLE } from "../table";

export const USERSESSION = {
  GET_ALL: `SELECT * FROM "${TABLE.USERSESSION}" LIMIT $1 OFFSET $2`,

  GET_BY_ID: `SELECT * FROM "${TABLE.USERSESSION}" WHERE id = $1`,

  GET_BY_USER_ID: `SELECT * FROM "${TABLE.USERSESSION}" WHERE "userId" = $1`,

  GET_TOTAL_COUNT: `SELECT COUNT(*) AS total FROM "${TABLE.USERSESSION}"`,

  CREATE: `INSERT INTO "${TABLE.USERSESSION}" ("userId", token, "expiresOn", "createdOn") VALUES ($1, $2, $3, NOW()) RETURNING *`,

  UPDATE: `UPDATE "${TABLE.USERSESSION}" SET token = COALESCE($2, token), "expiresOn" = COALESCE($3, "expiresOn") WHERE id = $1 RETURNING *`,

  DELETE: `DELETE FROM "${TABLE.USERSESSION}" WHERE id = $1`,

  CHECK_EXISTING: `SELECT id FROM "${TABLE.USERSESSION}" WHERE "userId" = $1 AND "expiresOn" > NOW()`,

  UPDATE_TOKEN: `UPDATE "${TABLE.USERSESSION}" SET token = $2, "expiresOn" = $3 WHERE id = $1 RETURNING *`,

  CHECK_ACTIVE_SESSION: `SELECT id FROM "${TABLE.USERSESSION}" WHERE "userId" = $1 AND token = $2 AND "expiresOn" > NOW()`,
};
