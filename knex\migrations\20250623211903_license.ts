import type { Knex } from "knex";
  
export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('license _activity', function(table) {
    table.increments('id').primary(); // Primary key
    table.integer('profileId').nullable();
    table.integer('industryId').nullable(); // Required
    table.integer('industryName').nullable(); // Required 
    table.specificType('rolesList', 'text[]').nullable();  
    table.timestamp('timestamp').defaultTo(knex.fn.now()); // Optional created timestamp
  });
};

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('license _activity');
};


