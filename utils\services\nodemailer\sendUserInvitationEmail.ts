import transporter from ".";
import nodemailer from "nodemailer";

const baseTemplate = (content: string) => `
  <div style="font-family: Arial, sans-serif; color: #333; padding: 20px; background-color: #f7f7f7;">
    <div style="max-width: 600px; margin: auto; background-color: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
      <div style="background-color: #004aad; padding: 20px; color: #ffffff; text-align: center;">
        <h1 style="margin: 0; font-size: 24px;">FindAnyAgent</h1>
      </div>
      <div style="padding: 30px;">
        ${content}
        <p style="margin-top: 40px; font-size: 14px; color: #777;">If you didn’t request this, you can safely ignore this email.</p>
        <p style="font-size: 14px; color: #777;">— The FindAnyAgent Team</p>
      </div>
    </div>
  </div>
`;

export const sendUserInvitationEmail = async (
  fullName: string,
  email: string,
  userType: string,
  registrationLink?: string,
  password?: string // Only for sub admin
) => {
  let subject = "Invitation to Join FindAnyAgent";
  let htmlContent = "";

  if (userType === "webuser") {
    htmlContent = `
      <p style="font-size: 16px;">Dear ${fullName},</p>
      <p style="font-size: 16px;">You have been invited to join <strong>FindAnyAgent</strong> as a <strong>Web User</strong>.</p>
      <p style="font-size: 16px;">Please <a href="${registrationLink}" style="color: #004aad;">click here</a> to register and get started.</p>
    `;
  } else if (["agent", "agency", "agencyadmin"].includes(userType)) {
    const roleMap: Record<string, string> = {
      agent: "Agent",
      agency: "Agency",
      agencyadmin: "Agency Admin",
    };
   htmlContent = `
  <div style="font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;">
    <p style="font-size: 16px;">Hi ${email},</p>
    
    <p style="font-size: 16px;">
      You've been invited to join <strong>${roleMap[userType]}</strong> on <strong>FindAnyAgent</strong> — the UAE’s #1 platform for trusted, verified agents across 35+ industries.
    </p>
    
    <p style="font-size: 16px;">As a team member, you’ll gain access to:</p>
    <ul style="font-size: 16px; padding-left: 20px;">
      <li>✅ A verified agency profile</li>
      <li>✅ Lead generation tools</li>
      <li>✅ Enhanced visibility across the UAE</li>
      <li>✅ Client trust through verified badges</li>
    </ul>

    <p style="font-size: 16px;">
      👉 <a href="${registrationLink}" style="color: #004aad; text-decoration: none; font-weight: bold;">Click here to accept your invitation and complete your profile</a>
    </p>

    <p style="font-size: 16px;">
      If you’re new to FindAnyAgent, setting up your profile takes just a few minutes — and it’s 100% free!
    </p>

    <p style="font-size: 16px;">Welcome aboard,</p>
    <p style="font-size: 16px;"><strong>FindAnyAgent</strong><br/>
    <a href="${process.env.WEBSITE_URL}" style="color: #004aad;">www.FindAnyAgent.ae</a></p>
  </div>
`;

    // htmlContent = `
    //   <p style="font-size: 16px;">Dear ${fullName},</p>
    //   <p style="font-size: 16px;">You have been invited to join <strong>FindAnyAgent</strong> as a <strong>${roleMap[userType]}</strong>.</p>
    //   <p style="font-size: 16px;">Please <a href="${registrationLink}" style="color: #004aad;">click here</a> to register and get started.</p>
    // `;
  } else if (userType === "sub-admin" && password) {
    subject = "Your Sub Admin Account for FindAnyAgent";
    htmlContent = `
      <p style="font-size: 16px;">Dear ${fullName},</p>
      <p style="font-size: 16px;">Your sub admin account has been created with the following credentials:</p>
      <p style="font-size: 16px;"><strong>Email:</strong> ${email}<br/><strong>Password:</strong> ${password}</p>
      <p style="font-size: 16px;">Please <a href="${registrationLink}" style="color: #004aad;">click here</a> to log in and change your password after the first login.</p>
    `;
  } else if (userType === "invitation") {
    subject = "Team Member Invitation";
    htmlContent = `
      <p style="font-size: 16px;">Dear User,</p>
      <p style="font-size: 16px;">You have been invited to join <strong>FindAnyAgent</strong> by ${email}.</p>
      <p style="font-size: 16px;">Please <a href="${registrationLink}" style="color: #004aad;">click here</a> to register and get started.</p>
    `;
  }

  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject,
    html: baseTemplate(htmlContent),
  };

  await transporter.sendMail(mailOptions);
};
