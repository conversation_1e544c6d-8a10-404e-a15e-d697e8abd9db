import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
    CREATE OR REPLACE FUNCTION look.sp_packagefeaturesmeta (
  p_fnid integer,
  p_featureid integer DEFAULT NULL::integer,
  p_featurename text DEFAULT NULL::text,
  p_featuretype text DEFAULT NULL::text,
  p_featureconstant text DEFAULT NULL::text,
  p_displayorder integer DEFAULT NULL::integer,
  p_description text DEFAULT NULL::text,
  p_sortby integer DEFAULT 1::integer
) RETURNS jsonb LANGUAGE plpgsql AS $function$
DECLARE
    v_data JSONB;
    v_title TEXT;
    v_result INT;
    v_type TEXT;
    v_message TEXT;
BEGIN
    IF p_fnid = 0 THEN
        SELECT to_jsonb(t) INTO v_data FROM look.packagefeaturesmeta t WHERE t."featureId" = p_featureid;
        v_title := 'Package Features Meta - Single Record';
        v_result := 1;
        v_type := 'success';
        v_message := 'Record selected successfully.';
    ELSIF p_fnid = 1 THEN        
        WITH sorted_rows AS (
            SELECT to_jsonb(t) AS row_json
            FROM look.packagefeaturesmeta t
            WHERE (p_featureid IS NULL OR t."featureId" = p_featureid)
            ORDER BY
          			CASE p_sortby
                    WHEN 1 THEN t."displayOrder"
                    ELSE t."featureId"
                END NULLS LAST
        )
        SELECT jsonb_agg(row_json) INTO v_data FROM sorted_rows;
        v_title := 'Package Features Meta - List Records';
        v_result := 1;
        v_type := 'success';
        v_message := 'Records selected successfully.';
    ELSIF p_fnid = 2 THEN
        IF p_featureid IS NULL THEN
            INSERT INTO look.packagefeaturesmeta (
                "featureName", "featureType", "featureConstant", "displayOrder", description, "created_at"
            ) VALUES (
                p_featurename, p_featuretype, p_featureconstant, p_displayorder, p_description, NOW()
            ) RETURNING to_jsonb(look.packagefeaturesmeta.*) INTO v_data;
            v_title := 'Package Features Meta - Insert';
            v_result := 1;
            v_type := 'success';
            v_message := 'Record inserted successfully.';
        ELSE
            UPDATE look.packagefeaturesmeta
            SET
                "featureName" = p_featurename,
                "featureType" = p_featuretype,
                "featureConstant" = p_featureconstant,
                "displayOrder" = p_displayorder,
                description = p_description,
                "updated_at" = NOW()
            WHERE "featureId" = p_featureid
            RETURNING to_jsonb(look.packagefeaturesmeta.*) INTO v_data;
            v_title := 'Package Features Meta - Update';
            v_result := 2;
            v_type := 'success';
            v_message := 'Record updated successfully.';
        END IF;
    ELSIF p_fnid = 3 THEN
        DELETE FROM look.packagefeaturesmeta WHERE "featureId" = p_featureid RETURNING to_jsonb(look.packagefeaturesmeta.*) INTO v_data;
        v_title := 'Package Features Meta - Delete';
        v_result := 3;
        v_type := 'success';
        v_message := 'Record deleted successfully.';
    ELSE
        v_title := 'Package Features Meta - Error';
        v_result := -1;
        v_type := 'error';
        v_message := 'Invalid Function ID.';
        v_data := NULL;
    END IF;
    RETURN jsonb_build_object(
        'title', v_title,
        'result', v_result,
        'type', v_type,
        'message', v_message,
        'data', COALESCE(v_data, 'null'::jsonb)
    );
END;
$function$;
`)
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`
    DROP FUNCTION IF EXISTS look.sp_packagefeaturesmeta (
      integer, integer, text, text, text, integer, text, integer
    );
  `);
}
