import { Request, Response } from "express";
import { errorCatchResponse, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import db from "../../config/database";
import { TYPE } from "../../utils/database/queries/type";

export const getAllLocations = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const result = await db.query(TYPE.GET_ALL_LOCATIONS);
      return responseData(res, 200, "Locations fetched successfully", result.rows);
    } catch (error) {
      console.error("Error fetching locations:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);
