import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').alterTable('packagetype', (table) => {
    // NEW: external billing plan identifiers
    table.string('stripePlanId').nullable();
    table.string('payPalPlanId').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').alterTable('packagetype', (table) => {
    // Roll back the new fields
    table.dropColumn('stripePlanId');
    table.dropColumn('payPalPlanId');
  });
}
