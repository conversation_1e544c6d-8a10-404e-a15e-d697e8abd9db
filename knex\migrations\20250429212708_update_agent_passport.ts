import type { Knex } from "knex"; 

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.specificType('passport', 'text[]').nullable().defaultTo(null); 
 
     
 
  });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.dropColumn('passport');
 
        
    });
 
}



