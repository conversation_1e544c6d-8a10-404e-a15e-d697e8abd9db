CREATE SCHEMA IF NOT EXISTS agn;

DROP TABLE IF EXISTS "agn"."agencies";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."agencies" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "profileId" int4 NOT NULL,
    "typeId" int4 NOT NULL,
    "businessLicense" text,
    "brokerLicense" text,
    "referenceded" text,
    "specialization" text,
    "experience" text,
    "officeHours" text,
    "website" text,
    "contacts" text,
    "logo" text,
    "locationId" int4 NOT NULL,
    "address" text,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_agencies_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_agencies_location" FOREIGN KEY ("locationId") REFERENCES "list"."location"("id"),
    CONSTRAINT "fk_agencies_modifiedBy" FOREIGN KEY ("modifiedBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_agencies_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_agencies_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id"),
    CONSTRAINT "fk_agencies_type" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16392_pk__agencies__3214ec07e7279efb ON agn.agencies USING btree (id);

DROP TABLE IF EXISTS "agn"."agents";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."agents" (
    "id" SERIAL PRIMARY KEY,
    "agencyId" int4 NOT NULL,
    "profileId" int4 NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_agents_agency" FOREIGN KEY ("agencyId") REFERENCES "agn"."agencies"("id"),
    CONSTRAINT "fk_agents_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_agents_modifiedBy" FOREIGN KEY ("modifiedBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_agents_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_agents_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16398_pk__agents__3214ec077d9b502a ON agn.agents USING btree (id);

DROP TABLE IF EXISTS "agn"."projects";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."projects" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "name" text NOT NULL,
    "agencyId" int4 NOT NULL,
    "details" text,
    "descriptions" text,
    "startedDate" timestamptz NOT NULL,
    "endDate" timestamptz NOT NULL,
    "handoverDate" timestamptz NOT NULL,
    "locationId" int4 NOT NULL,
    "address" text,
    "currencyId" int4 NOT NULL,
    "price" numeric,
    "paymentPlan" text,
    "parking" bool NOT NULL,
    "swimmingPools" bool NOT NULL,
    "gym" bool NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_projects_agency" FOREIGN KEY ("agencyId") REFERENCES "agn"."agencies"("id"),
    CONSTRAINT "fk_projects_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_projects_currency" FOREIGN KEY ("currencyId") REFERENCES "list"."currency"("id"),
    CONSTRAINT "fk_projects_location" FOREIGN KEY ("locationId") REFERENCES "list"."location"("id"),
    CONSTRAINT "fk_projects_modifiedBy" FOREIGN KEY ("modifiedBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_projects_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16420_pk__projects__3214ec07aa641aed ON agn.projects USING btree (id);

DROP TABLE IF EXISTS "agn"."properties";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."properties" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "name" text NOT NULL,
    "local" text,
    "agencyId" int4 NOT NULL,
    "propertyTypeId" int4 NOT NULL,
    "apartmentTypeId" int4 NOT NULL,
    "totalRooms" int4 NOT NULL,
    "locationId" int4 NOT NULL,
    "address" text,
    "currencyId" int4 NOT NULL,
    "price" numeric,
    "size" numeric,
    "permitNo" text,
    "parking" bool NOT NULL,
    "swimmingPools" bool NOT NULL,
    "gym" bool NOT NULL,
    "startDate" timestamptz NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_properties_agency" FOREIGN KEY ("agencyId") REFERENCES "agn"."agencies"("id"),
    CONSTRAINT "fk_properties_apartmentType" FOREIGN KEY ("apartmentTypeId") REFERENCES "look"."type"("id"),
    CONSTRAINT "fk_properties_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_properties_currency" FOREIGN KEY ("currencyId") REFERENCES "list"."currency"("id"),
    CONSTRAINT "fk_properties_location" FOREIGN KEY ("locationId") REFERENCES "list"."location"("id"),
    CONSTRAINT "fk_properties_propertyType" FOREIGN KEY ("propertyTypeId") REFERENCES "look"."type"("id"),
    CONSTRAINT "fk_properties_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16426_pk__properties__3214ec075d91505e ON agn.properties USING btree (id);

DROP TABLE IF EXISTS "agn"."features";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."features" (
    "id" SERIAL PRIMARY KEY,
    "propertyId" int4,
    "projectId" int4,
    "featureName" text NOT NULL,
    "typeId" int4 NOT NULL,
    "size" text,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_features_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_features_project" FOREIGN KEY ("projectId") REFERENCES "agn"."projects"("id"),
    CONSTRAINT "fk_features_property" FOREIGN KEY ("propertyId") REFERENCES "agn"."properties"("id"),
    CONSTRAINT "fk_features_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id"),
    CONSTRAINT "fk_features_type" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16402_pk__features__3214ec07383540bd ON agn.features USING btree (id);


DROP TABLE IF EXISTS "agn"."images";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."images" (
    "id" SERIAL PRIMARY KEY,
    "propertyId" int4,
    "projectId" int4,
    "imageUrl" text NOT NULL,
    "mediaTypeId" int4 NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_images_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_images_mediaType" FOREIGN KEY ("mediaTypeId") REFERENCES "look"."type"("id"),
    CONSTRAINT "fk_images_project" FOREIGN KEY ("projectId") REFERENCES "agn"."projects"("id"),
    CONSTRAINT "fk_images_property" FOREIGN KEY ("propertyId") REFERENCES "agn"."properties"("id"),
    CONSTRAINT "fk_images_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16408_pk__images__3214ec07f3a4ef77 ON agn.images USING btree (id);

DROP TABLE IF EXISTS "agn"."listings";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."listings" (
    "id" SERIAL PRIMARY KEY,
    "propertyId" int4,
    "projectId" int4,
    "listingType" text NOT NULL,
    "price" numeric NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_listings_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_listings_project" FOREIGN KEY ("projectId") REFERENCES "agn"."projects"("id"),
    CONSTRAINT "fk_listings_property" FOREIGN KEY ("propertyId") REFERENCES "agn"."properties"("id"),
    CONSTRAINT "fk_listings_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16414_pk__listings__3214ec07225794a0 ON agn.listings USING btree (id);


DROP TABLE IF EXISTS "agn"."viewings";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "agn"."viewings" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "propertyId" int4,
    "projectId" int4,
    "viewerProfileId" int4 NOT NULL,
    "dateScheduled" timestamptz NOT NULL,
    "timeScheduled" timestamptz NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_viewings_agent" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_viewings_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_viewings_modifiedBy" FOREIGN KEY ("modifiedBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_viewings_project" FOREIGN KEY ("projectId") REFERENCES "agn"."projects"("id"),
    CONSTRAINT "fk_viewings_property" FOREIGN KEY ("propertyId") REFERENCES "agn"."properties"("id"),
    CONSTRAINT "fk_viewings_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id"),
    CONSTRAINT "fk_viewings_viewer" FOREIGN KEY ("viewerProfileId") REFERENCES "prf"."profile"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16432_pk__viewings__3214ec07c1456c4b ON agn.viewings USING btree (id);