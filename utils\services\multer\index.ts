const multer = require("multer");
import fs from "fs";
import { Request } from "express";
export const storageData = (name: string) => {
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb: CallableFunction) => {
    const path = `public/${name}`;
    
    try {
      fs.mkdirSync(path, { recursive: true });
      cb(null, path);
    } catch (err: any) {
      console.error("❌ Error creating folder:", err);
      cb(err.message, null);
    }
  },
  filename: (
    req: Request,
    file: Express.Multer.File,
    cb: CallableFunction
  ) => {
    const sanitizedOriginalName =
      typeof file.originalname === "string"
        ? file.originalname.replace(/[^\w.]/g, "_")
        : file.originalname;

    const finalName = Date.now() + "-" + sanitizedOriginalName;

 
    cb(null, finalName);
  },
});

  const upload = multer({ storage });

  return upload;
};

export const uploadErrorHandler = (err: any, req: any, res: any, next: any) => {
  if (err instanceof multer.MulterError) {
    // Multer-specific errors
    if (err.code === "LIMIT_UNEXPECTED_FILE") {
      return res.status(400).json({
        status: 400,
        success: false,
        message: `Too many files uploaded for field "${err.field}"`,
      });
    }

    if (err.code === "LIMIT_FILE_COUNT") {
      return res.status(400).json({
        status: 400,
        success: false,
        message: `Maximum number of files exceeded.`,
      });
    }

    return res.status(400).json({
      status: 400,
      success: false, message: err.message
    });
  } else if (err) {
    // General errors
    return res.status(500).json({
      status: 500,
      success: false, message: "Something went wrong", error: err
    });
  }

  next();
}
