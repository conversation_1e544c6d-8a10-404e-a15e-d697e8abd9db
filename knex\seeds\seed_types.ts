import type { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Step 1: Get all existing names (lowercased)
  const existing = await knex("look.type")
    .select("name")
    .then((rows) => rows.map((r) => r.name.toLowerCase()));

  // Step 2: Define parent types
  const parentDefinitions = [
    {
      name: "subscription_packages",
      description: "Categories of subscription plans for agents and agencies",
    },
    {
      name: "service_category",
      description: "Categories for services and tasks offered by agents",
    },
    {
      name: "agent_types",
      description: "Types of agents operating on the platform",
    },
    {
      name: "media_types",
      description: "Types of media files (photos, videos, documents)",
    },
    {
      name: "property_types",
      description: "Types of real estate properties",
    },
  ];

  // Step 3: Insert parents if not already in DB (case-insensitive)
  const parentIds: Record<string, number> = {};
  for (const parent of parentDefinitions) {
    const existingRow = await knex("look.type")
      .whereRaw("LOWER(name) = ?", [parent.name.toLowerCase()])
      .first();

    if (existingRow) {
      parentIds[parent.name] = existingRow.id;
    } else {
      const [inserted] = await knex("look.type")
        .insert({ ...parent, parentId: null })
        .returning("id");
      parentIds[parent.name] = inserted.id;
    }
  }

  // Step 4: Define children (linked by parent keys)
  const childDefinitions = [
    // subscription_packages
    {
      name: "pro",
      description: "Premium tier",
      parentKey: "subscription_packages",
    },
    {
      name: "standard",
      description: "Standard tier",
      parentKey: "subscription_packages",
    },
    {
      name: "elite",
      description: "Elite tier",
      parentKey: "subscription_packages",
    },

    // service_category
    {
      name: "service",
      description: "Service offering",
      parentKey: "service_category",
    },
    {
      name: "mission",
      description: "Mission type",
      parentKey: "service_category",
    },

    // agent_types
    {
      name: "individual (employed)",
      description: "Employed agent",
      parentKey: "agent_types",
    },
    {
      name: "freelancer - Emirates",
      description: "UAE freelancer",
      parentKey: "agent_types",
    },
    {
      name: "freelancer - Other than Emirates",
      description: "Non-UAE freelancer",
      parentKey: "agent_types",
    },
    {
      name: "company",
      description: "Agency or company",
      parentKey: "agent_types",
    },
    {
      name: "developer",
      description: "Property developer",
      parentKey: "agent_types",
    },

    // media_types
    { name: "photo", description: "Photo file", parentKey: "media_types" },
    { name: "video", description: "Video file", parentKey: "media_types" },
    {
      name: "document",
      description: "Document or PDF",
      parentKey: "media_types",
    },

    // property_types
    {
      name: "apartment",
      description: "Apartment unit",
      parentKey: "property_types",
    },
    {
      name: "villa",
      description: "Villa or house",
      parentKey: "property_types",
    },
    {
      name: "office",
      description: "Office space",
      parentKey: "property_types",
    },
    {
      name: "warehouse",
      description: "Warehouse building",
      parentKey: "property_types",
    },
    { name: "shop", description: "Retail shop", parentKey: "property_types" },
    { name: "land", description: "Empty land", parentKey: "property_types" },
  ];

  // Step 5: Insert children if not present (case-insensitive)
  for (const child of childDefinitions) {
    if (!existing.includes(child.name.toLowerCase())) {
      await knex("look.type").insert({
        name: child.name,
        description: child.description,
        parentId: parentIds[child.parentKey],
      });
    }
  }
}
