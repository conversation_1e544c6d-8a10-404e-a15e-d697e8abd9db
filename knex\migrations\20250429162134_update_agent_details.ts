import type { Knex } from "knex"; 

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
    table.string('companyEmail').nullable().defaultTo(null);   
    table.string('companyPhone').nullable().defaultTo(null);   
    table.boolean('hasFreelancerPermit').nullable().defaultTo(null);  
    table.string('freelancerLicenseNumber').nullable().defaultTo(null);  
    table.string('freelancerLicenseNumberExpiryDate').nullable().defaultTo(null); 
    table.string('freelancerLicenseAuthority').nullable().defaultTo(null); 
    table.string('freelancerLicenseAuthorityOther').nullable().defaultTo(null);  
    table.string('agentRole').nullable().defaultTo(null); 
    table.boolean('hasLicense').nullable().defaultTo(null);  
    table.boolean('termsAgree').nullable().defaultTo(null);  
    table.boolean('accuracyConfirm').nullable().defaultTo(null);  
    table.boolean('communicationConsent').nullable().defaultTo(null);  
    table.string('licenseAuthority').nullable().defaultTo(null); 
 
  });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.dropColumn('companyEmail');
        table.dropColumn('companyPhone');
        table.dropColumn('hasFreelancerPermit');
        table.dropColumn('freelancerLicenseNumber');
        table.dropColumn('freelancerLicenseNumberExpiryDate');
        table.dropColumn('freelancerLicenseAuthority');
        table.dropColumn('freelancerLicenseAuthorityOther');
        table.dropColumn('agentRole');
        table.dropColumn('hasLicense');
        table.dropColumn('termsAgree');
        table.dropColumn('accuracyConfirm');
        table.dropColumn('communicationConsent');
        table.dropColumn('licenseAuthority');
    });
 
}



