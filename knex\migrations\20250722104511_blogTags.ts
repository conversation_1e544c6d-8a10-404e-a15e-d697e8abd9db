import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("blogsTags", (table) => {
    table.increments("id").primary();
    table.bigInteger("blogId").notNullable();
    table.text("tag") .notNullable(); 
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("blogsTags");
}
