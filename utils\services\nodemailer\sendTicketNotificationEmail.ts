import nodemailer from "nodemailer";
import transporter from ".";

export const sendTicketNotificationEmail = async (
  subject: string,
  message: string,
  to: string,
  attachments?: Array<{ filename: string; content: Buffer }>
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to,
    subject,
    html: await ticketNotificationHTMLTemplate(message),
    ...(attachments?.length ? { attachments } : {}),
  };
  try {
    await transporter.sendMail(mailOptions);
    console.log("Ticket notification email sent successfully!");
  } catch (error) {
    console.error("Error sending ticket notification email:", error);
  }
};

const ticketNotificationHTMLTemplate = async (message: string): Promise<string> => `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FindAnyAgent Ticket Notification</title>
  </head>
  <body style="margin:0;padding:0;background:#f9f9f9;">
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f9f9f9;">
      <tr>
        <td align="center" style="padding:40px 10px;">
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0"
                 style="max-width:600px;background:#ffffff;border-radius:8px;
                        box-shadow:0 0 10px rgba(0,0,0,0.1);">
            <tr>
              <td style="padding:24px 32px;font-family:Arial,sans-serif;color:#333333;line-height:1.6;">
                <h1 style="margin:0 0 20px 0;font-size:22px;color:#5e9b6d;">
                  FindAnyAgent Ticket Notification
                </h1>
                <div style="font-size:16px;">${message}</div>
                <p style="margin:32px 0 0 0;font-size:14px;color:#777777;">
                  Best regards,<br />
                  <strong>FindAnyAgent Team</strong>
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>`; 