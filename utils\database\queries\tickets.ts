import { TABLE } from "../table";

export const TICKETS = {
  CREATE_TICKET: `INSERT INTO prf.tickets (agent_id, agent_name, agent_email, title, description, priority, category, status, created_by, assigned_to) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *`,
  GET_ALL_TICKETS: `SELECT t.*, p.email AS agent_email, 
    CONCAT(ap."firstName", ' ', COALESCE(ap."middleName", ''), ' ', COALESCE(ap."lastName", '')) AS admin_name,
    ap.email AS admin_email
    FROM prf.tickets t 
    LEFT JOIN prf.profile p ON t.agent_id = p.id 
    LEFT JOIN prf.profile ap ON t.created_by = ap.id 
    ORDER BY t.created_at DESC`,
  GET_TICKET_BY_ID: `SELECT t.*, p.email AS agent_email,
    CONCAT(ap."firstName", ' ', COALESCE(ap."middleName", ''), ' ', COALESCE(ap."lastName", '')) AS admin_name,
    ap.email AS admin_email
    FROM prf.tickets t 
    LEFT JOIN prf.profile p ON t.agent_id = p.id 
    LEFT JOIN prf.profile ap ON t.created_by = ap.id 
    WHERE t.id = $1`,
  UPDATE_TICKET: `UPDATE prf.tickets SET status = $1, priority = $2, agent_id = $3, agent_name = $4, agent_email = $5, description = $6, updated_at = CURRENT_TIMESTAMP, assigned_to = $7 WHERE id = $8 RETURNING *`,
  SOFT_DELETE_TICKET: `UPDATE prf.tickets SET status = 'closed', updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *`,
  FILTER_TICKETS: `SELECT t.*, p.email AS agent_email,
    CONCAT(ap."firstName", ' ', COALESCE(ap."middleName", ''), ' ', COALESCE(ap."lastName", '')) AS admin_name,
    ap.email AS admin_email
    FROM prf.tickets t 
    LEFT JOIN prf.profile p ON t.agent_id = p.id 
    LEFT JOIN prf.profile ap ON t.created_by = ap.id 
    WHERE ($1::text IS NULL OR t.status = $1) AND ($2::text IS NULL OR t.priority = $2) AND ($3::text IS NULL OR t.category = $3) AND (($4::text IS NULL) OR (t.title ILIKE '%' || $4 || '%') OR (t.agent_name ILIKE '%' || $4 || '%') OR (t.agent_email ILIKE '%' || $4 || '%')) 
    ORDER BY t.created_at DESC`,

  // Responses
  GET_RESPONSES_BY_TICKET_ID: `SELECT r.*, 
    ap.email AS agent_email, 
    CONCAT(ap."firstName", ' ', COALESCE(ap."middleName", ''), ' ', COALESCE(ap."lastName", '')) AS agent_name,
    adp.email AS admin_email,
    CONCAT(adp."firstName", ' ', COALESCE(adp."middleName", ''), ' ', COALESCE(adp."lastName", '')) AS admin_name
    FROM prf.responses r 
    LEFT JOIN prf.profile ap ON r.agent_id = ap.id 
    LEFT JOIN prf.profile adp ON r.admin_id = adp.id 
    WHERE r.ticket_id = $1 
    ORDER BY r.created_at ASC`,
  GET_RESPONSE_BY_ID: `SELECT r.*, 
    ap.email AS agent_email, 
    CONCAT(ap."firstName", ' ', COALESCE(ap."middleName", ''), ' ', COALESCE(ap."lastName", '')) AS agent_name,
    adp.email AS admin_email,
    CONCAT(adp."firstName", ' ', COALESCE(adp."middleName", ''), ' ', COALESCE(adp."lastName", '')) AS admin_name
    FROM prf.responses r 
    LEFT JOIN prf.profile ap ON r.agent_id = ap.id 
    LEFT JOIN prf.profile adp ON r.admin_id = adp.id 
    WHERE r.id = $1`,
  CREATE_RESPONSE: `INSERT INTO prf.responses (ticket_id, agent_id, admin_id, response) VALUES ($1, $2, $3, $4) RETURNING *`,
}; 