import type { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Delete all existing entries
  await knex("list.services").del();

  // Inserts seed entries
  await knex("list.services")
    .insert([
      // Primary Industries For Both Agent & Agency
      {
        id: 1,
        group: null,
        name: "Real Estate",
        description: "Real Estate",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 2,
        group: null,
        name: "Accounting & Tax",
        description: "Accounting & Tax",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 3,
        group: null,
        name: "IT & Tech",
        description: "IT & Tech",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 4,
        group: null,
        name: "Mortgage & Finance",
        description: "Mortgage & Finance",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 5,
        group: null,
        name: "Visa & Immigration",
        description: "Visa & Immigration",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 6,
        group: null,
        name: "Insurance Providers",
        description: "Insurance Providers",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 7,
        group: null,
        name: "Marketing & Advertising",
        description: "Marketing & Advertising",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 8,
        group: null,
        name: "Legal & Compliance",
        description: "Legal & Compliance",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 9,
        group: null,
        name: "Maintenance & Repair",
        description: "Maintenance & Repair",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 10,
        group: null,
        name: "Logistics & Transportation",
        description: "Logistics & Transportation",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 11,
        group: null,
        name: "Travel & Hospitality",
        description: "Travel & Hospitality",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 12,
        group: null,
        name: "Post-Purchase / Rental",
        description: "Post-Purchase / Rental",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 13,
        group: null,
        name: "Human Resources & Recruitment",
        description: "Human Resources & Recruitment",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 14,
        group: null,
        name: "Retail & E-Commerce",
        description: "Retail & E-Commerce",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 15,
        group: null,
        name: "Commercial Real Estate",
        description: "Commercial Real Estate",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 16,
        group: null,
        name: "Real Estate Investment",
        description: "Real Estate Investment",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 17,
        group: null,
        name: "Health & Beauty",
        description: "Health & Beauty",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 18,
        group: null,
        name: "Training & Education",
        description: "Training & Education",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 19,
        group: null,
        name: "Hajj & Umrah",
        description: "Hajj & Umrah",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 20,
        group: null,
        name: "Events & Entertainment",
        description: "Events & Entertainment",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 21,
        group: null,
        name: "Creative Services",
        description: "Creative Services",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 22,
        group: null,
        name: "AI Agents",
        description: "AI Agents",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 23,
        group: null,
        name: "Personal Services",
        description: "Personal Services",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 24,
        group: null,
        name: "Emerging & Niche Categories",
        description: "Emerging & Niche Categories",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 25,
        group: null,
        name: "Other",
        description: "Other",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // ********************************* Sub-Industries for Agents *********************************

      // Real State
      {
        id: 26,
        group: "agent",
        name: "Real Estate Broker",
        description: "Real Estate Broker",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 27,
        group: "agent",
        name: "Real Estate Sales Agent",
        description: "Real Estate Sales Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 28,
        group: "agent",
        name: "Real Estate Sales Team",
        description: "Real Estate Sales Team",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 29,
        group: "agent",
        name: "Real Estate Sales Manager",
        description: "Real Estate Sales Manager",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 30,
        group: "agent",
        name: "Real Estate Consultant",
        description: "Real Estate Consultant",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 31,
        group: "agent",
        name: "Real Estate Customer Representative",
        description: "Real Estate Customer Representative",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 32,
        group: "agent",
        name: "Real Estate Advisor",
        description: "Real Estate Advisor",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 33,
        group: "agent",
        name: "Apartment/Flat Sales Agent",
        description: "Apartment/Flat Sales Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 34,
        group: "agent",
        name: "Villa Sales Agent",
        description: "Villa Sales Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 35,
        group: "agent",
        name: "Residential Leasing Agent",
        description: "Residential Leasing Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 36,
        group: "agent",
        name: "Off-Plan Property Agent",
        description: "Off-Plan Property Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 37,
        group: "agent",
        name: "Holiday Homes & Short-Term Rentals",
        description: "Holiday Homes & Short-Term Rentals",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 38,
        group: "agent",
        name: "Office Leasing Agent",
        description: "Office Leasing Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 39,
        group: "agent",
        name: "Retail & Shop Leasing Agent",
        description: "Retail & Shop Leasing Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 40,
        group: "agent",
        name: "Industrial & Warehouse Property Agent",
        description: "Industrial & Warehouse Property Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 41,
        group: "agent",
        name: "Commercial Sales Agent",
        description: "Commercial Sales Agent",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 42,
        group: "agent",
        name: "Residential Property Managers",
        description: "Residential Property Managers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 43,
        group: "agent",
        name: "Commercial Property Managers",
        description: "Commercial Property Managers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 44,
        group: "agent",
        name: "Mixed-use Property Managers",
        description: "Mixed-use Property Managers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 45,
        group: "agent",
        name: "Building Maintenance Services",
        description: "Building Maintenance Services",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 46,
        group: "agent",
        name: "Cleaning & Landscaping Agents",
        description: "Cleaning & Landscaping Agents",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 47,
        group: "agent",
        name: "Utilities & Energy Management",
        description: "Utilities & Energy Management",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 48,
        group: "agent",
        name: "Lease Renewal Managers",
        description: "Lease Renewal Managers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 49,
        group: "agent",
        name: "Rent Collection Officers",
        description: "Rent Collection Officers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 50,
        group: "agent",
        name: "Dispute Resolution Officers",
        description: "Dispute Resolution Officers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 51,
        group: "agent",
        name: "Buy-to-Let Investment Advisors",
        description: "Buy-to-Let Investment Advisors",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 52,
        group: "agent",
        name: "Flipping & Renovation Consultants",
        description: "Flipping & Renovation Consultants",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 53,
        group: "agent",
        name: "ROI & Portfolio Analysis Experts",
        description: "ROI & Portfolio Analysis Experts",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 54,
        group: "agent",
        name: "REIT Consultants",
        description: "REIT Consultants",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 55,
        group: "agent",
        name: "Large-scale Asset Managers",
        description: "Large-scale Asset Managers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 56,
        group: "agent",
        name: "Property Fund Advisors",
        description: "Property Fund Advisors",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 57,
        group: "agent",
        name: "Market Research & Data Consultants",
        description: "Market Research & Data Consultants",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 58,
        group: "agent",
        name: "Demand Forecasting Analysts",
        description: "Demand Forecasting Analysts",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 59,
        group: "agent",
        name: "Lawyer",
        description: "Lawyer",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 60,
        group: "agent",
        name: "Real Estate Lawyers",
        description: "Real Estate Lawyers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 61,
        group: "agent",
        name: "Tenancy Contract Specialists",
        description: "Tenancy Contract Specialists",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 62,
        group: "agent",
        name: "RERA & Land Department Liaisons",
        description: "RERA & Land Department Liaisons",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 63,
        group: "agent",
        name: "Regulatory Compliance Agents",
        description: "Regulatory Compliance Agents",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 64,
        group: "agent",
        name: "Escrow Account Advisors",
        description: "Escrow Account Advisors",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 65,
        group: "agent",
        name: "Ownership Documentation Experts",
        description: "Ownership Documentation Experts",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 66,
        group: "agent",
        name: "RERA-Licensed Valuation Experts",
        description: "RERA-Licensed Valuation Experts",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 67,
        group: "agent",
        name: "Mortgage Valuation Agents",
        description: "Mortgage Valuation Agents",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 68,
        group: "agent",
        name: "Asset Revaluation Specialists",
        description: "Asset Revaluation Specialists",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 69,
        group: "agent",
        name: "Mortgage Brokers",
        description: "Mortgage Brokers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 70,
        group: "agent",
        name: "Islamic Home Finance Specialists",
        description: "Islamic Home Finance Specialists",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 71,
        group: "agent",
        name: "Refinance & Loan Transfer Agents",
        description: "Refinance & Loan Transfer Agents",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 72,
        group: "agent",
        name: "Property Financing Experts",
        description: "Property Financing Experts",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 73,
        group: "agent",
        name: "Down Payment Assistance Consultants",
        description: "Down Payment Assistance Consultants",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 74,
        group: "agent",
        name: "Real Estate Banking Advisors",
        description: "Real Estate Banking Advisors",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 75,
        group: "agent",
        name: "Credit Evaluation Officers",
        description: "Credit Evaluation Officers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 76,
        group: "agent",
        name: "Listing Optimization Agents",
        description: "Listing Optimization Agents",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 77,
        group: "agent",
        name: "Real Estate Social Media Experts",
        description: "Real Estate Social Media Experts",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 78,
        group: "agent",
        name: "Campaign Managers (Google, Meta etc.)",
        description: "Campaign Managers (Google, Meta etc.)",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 79,
        group: "agent",
        name: "Property Photography & Videography",
        description: "Property Photography & Videography",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 80,
        group: "agent",
        name: "Virtual Tours & 3D Modeling",
        description: "Virtual Tours & 3D Modeling",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 81,
        group: "agent",
        name: "Floor Plan Designers",
        description: "Floor Plan Designers",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 82,
        group: "agent",
        name: "CRM for Real Estate",
        description: "CRM for Real Estate",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 83,
        group: "agent",
        name: "Property Listing Portals",
        description: "Property Listing Portals",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 84,
        group: "agent",
        name: "Smart Home Integration Consultants",
        description: "Smart Home Integration Consultants",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Accounting & Tax
      {
        id: 85,
        group: "agent",
        name: "Certified Public Accountant (CPA)",
        description: "Certified Public Accountant (CPA)",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 86,
        group: "agent",
        name: "Chartered Accountant (CA)",
        description: "Chartered Accountant (CA)",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 87,
        group: "agent",
        name: "Accountant",
        description: "Accountant",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 88,
        group: "agent",
        name: "Tax Agent",
        description: "Tax Agent",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 89,
        group: "agent",
        name: "Tax Consultant",
        description: "Tax Consultant",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 90,
        group: "agent",
        name: "Bookkeeper",
        description: "Bookkeeper",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 91,
        group: "agent",
        name: "VAT Specialist",
        description: "VAT Specialist",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 92,
        group: "agent",
        name: "Internal Auditor",
        description: "Internal Auditor",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 93,
        group: "agent",
        name: "Financial Auditor",
        description: "Financial Auditor",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 94,
        group: "agent",
        name: "Forensic Accountant",
        description: "Forensic Accountant",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 95,
        group: "agent",
        name: "Payroll Specialist",
        description: "Payroll Specialist",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 96,
        group: "agent",
        name: "Budget Analyst",
        description: "Budget Analyst",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 97,
        group: "agent",
        name: "Corporate Tax Filing Agent",
        description: "Corporate Tax Filing Agent",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 98,
        group: "agent",
        name: "Excise Tax Consultant",
        description: "Excise Tax Consultant",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 99,
        group: "agent",
        name: "Zakat & Islamic Finance Accounting Specialist",
        description: "Zakat & Islamic Finance Accounting Specialist",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 100,
        group: "agent",
        name: "Transfer Pricing Advisor",
        description: "Transfer Pricing Advisor",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 101,
        group: "agent",
        name: "Customs & Import Tax Consultant",
        description: "Customs & Import Tax Consultant",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 102,
        group: "agent",
        name: "Risk & Compliance Advisory",
        description: "Risk & Compliance Advisory",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 103,
        group: "agent",
        name: "ESR (Economic Substance Regulation) Consultant",
        description: "ESR (Economic Substance Regulation) Consultant",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 104,
        group: "agent",
        name: "UBO (Ultimate Beneficial Owner) Compliance Expert",
        description: "UBO (Ultimate Beneficial Owner) Compliance Expert",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 105,
        group: "agent",
        name: "AML & Financial Due Diligence Advisor",
        description: "AML & Financial Due Diligence Advisor",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // IT & Tech
      {
        id: 106,
        group: "agent",
        name: "IT Support Agents / Technicians",
        description: "IT Support Agents / Technicians",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 107,
        group: "agent",
        name: "System Administrators",
        description: "System Administrators",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 108,
        group: "agent",
        name: "Managed Service Providers (MSPs)",
        description: "Managed Service Providers (MSPs)",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 109,
        group: "agent",
        name: "Network Engineers",
        description: "Network Engineers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 110,
        group: "agent",
        name: "Helpdesk Agencies",
        description: "Helpdesk Agencies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 111,
        group: "agent",
        name: "Cybersecurity Consultants",
        description: "Cybersecurity Consultants",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 112,
        group: "agent",
        name: "Penetration Testers / Ethical Hackers",
        description: "Penetration Testers / Ethical Hackers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 113,
        group: "agent",
        name: "SOC Analysts / Security Managers",
        description: "SOC Analysts / Security Managers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 114,
        group: "agent",
        name: "Compliance & Risk Experts",
        description: "Compliance & Risk Experts",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 115,
        group: "agent",
        name: "Freelance Developers",
        description: "Freelance Developers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 116,
        group: "agent",
        name: "Software Development Agencies",
        description: "Software Development Agencies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 117,
        group: "agent",
        name: "Tech Project Managers / Scrum Masters",
        description: "Tech Project Managers / Scrum Masters",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 118,
        group: "agent",
        name: "Solution Architects",
        description: "Solution Architects",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 119,
        group: "agent",
        name: "DevOps Engineers",
        description: "DevOps Engineers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 120,
        group: "agent",
        name: "Web Designers / Developers",
        description: "Web Designers / Developers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 121,
        group: "agent",
        name: "Mobile App Developers",
        description: "Mobile App Developers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 122,
        group: "agent",
        name: "UX/UI Designers",
        description: "UX/UI Designers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 123,
        group: "agent",
        name: "CMS Specialists",
        description: "CMS Specialists",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 124,
        group: "agent",
        name: "Data Analysts / Scientists",
        description: "Data Analysts / Scientists",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 125,
        group: "agent",
        name: "Machine Learning Engineers",
        description: "Machine Learning Engineers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 126,
        group: "agent",
        name: "AI Consultants",
        description: "AI Consultants",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 127,
        group: "agent",
        name: "Data Engineers",
        description: "Data Engineers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 128,
        group: "agent",
        name: "Cloud Architects / Consultants",
        description: "Cloud Architects / Consultants",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 129,
        group: "agent",
        name: "DevOps Agencies",
        description: "DevOps Agencies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 130,
        group: "agent",
        name: "SREs (Site Reliability Engineers)",
        description: "SREs (Site Reliability Engineers)",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 131,
        group: "agent",
        name: "Digital Transformation Consultants",
        description: "Digital Transformation Consultants",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 132,
        group: "agent",
        name: "IT Business Analysts",
        description: "IT Business Analysts",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 133,
        group: "agent",
        name: "Technology Advisors",
        description: "Technology Advisors",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 134,
        group: "agent",
        name: "Tech Recruiters / Head-hunters",
        description: "Tech Recruiters / Head-hunters",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 135,
        group: "agent",
        name: "Freelance Agent Platforms",
        description: "Freelance Agent Platforms",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 136,
        group: "agent",
        name: "Remote Team Agencies",
        description: "Remote Team Agencies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Mortgage & Finance
      {
        id: 137,
        group: "agent",
        name: "Mortgage Broker (Individual)",
        description: "Mortgage Broker (Individual)",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 138,
        group: "agent",
        name: "Home Loan Consultant",
        description: "Home Loan Consultant",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 139,
        group: "agent",
        name: "Independent Mortgage Advisor",
        description: "Independent Mortgage Advisor",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 140,
        group: "agent",
        name: "Independent Financial Advisor (IFA)",
        description: "Independent Financial Advisor (IFA)",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 141,
        group: "agent",
        name: "Certified Financial Planner (CFP)",
        description: "Certified Financial Planner (CFP)",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 142,
        group: "agent",
        name: "Wealth Management Advisor",
        description: "Wealth Management Advisor",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 143,
        group: "agent",
        name: "Debt Restructuring Consultant",
        description: "Debt Restructuring Consultant",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 144,
        group: "agent",
        name: "Personal Loan Consultant",
        description: "Personal Loan Consultant",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 145,
        group: "agent",
        name: "Auto Loan Specialist",
        description: "Auto Loan Specialist",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 146,
        group: "agent",
        name: "Credit Card Sales Agent",
        description: "Credit Card Sales Agent",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 147,
        group: "agent",
        name: "Business Loan Advisor",
        description: "Business Loan Advisor",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 148,
        group: "agent",
        name: "SME Financing Agent",
        description: "SME Financing Agent",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 149,
        group: "agent",
        name: "Mortgage Protection Insurance Agent",
        description: "Mortgage Protection Insurance Agent",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 150,
        group: "agent",
        name: "Property Insurance Consultant",
        description: "Property Insurance Consultant",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 151,
        group: "agent",
        name: "Life & Critical Illness Insurance Broker",
        description: "Life & Critical Illness Insurance Broker",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 152,
        group: "agent",
        name: "Real Estate Finance Consultant",
        description: "Real Estate Finance Consultant",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 153,
        group: "agent",
        name: "Property Valuation Agent",
        description: "Property Valuation Agent",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 154,
        group: "agent",
        name: "Equity Release Specialist",
        description: "Equity Release Specialist",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 155,
        group: "agent",
        name: "Mortgage Legal Advisor",
        description: "Mortgage Legal Advisor",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 156,
        group: "agent",
        name: "Real Estate Finance Lawyer",
        description: "Real Estate Finance Lawyer",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 157,
        group: "agent",
        name: "Financial Regulatory Consultant",
        description: "Financial Regulatory Consultant",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 158,
        group: "agent",
        name: "Loan Processing Specialist",
        description: "Loan Processing Specialist",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 159,
        group: "agent",
        name: "Credit Report Specialist",
        description: "Credit Report Specialist",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 160,
        group: "agent",
        name: "Mortgage Underwriter",
        description: "Mortgage Underwriter",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 161,
        group: "agent",
        name: "Banking Relationship Manager",
        description: "Banking Relationship Manager",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Visa & Immigration
      {
        id: 162,
        group: "agent",
        name: "Visa Consultant",
        description: "Visa Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 163,
        group: "agent",
        name: "Student Visa Advisor",
        description: "Student Visa Advisor",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 164,
        group: "agent",
        name: "Family Sponsorship Consultant",
        description: "Family Sponsorship Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 165,
        group: "agent",
        name: "Golden Visa Consultant",
        description: "Golden Visa Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 166,
        group: "agent",
        name: "Freelance Visa Consultant",
        description: "Freelance Visa Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 167,
        group: "agent",
        name: "Immigration Lawyer",
        description: "Immigration Lawyer",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 168,
        group: "agent",
        name: "Citizenship & Naturalization Lawyer",
        description: "Citizenship & Naturalization Lawyer",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 169,
        group: "agent",
        name: "Legal Advisor – Immigration Law",
        description: "Legal Advisor – Immigration Law",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 170,
        group: "agent",
        name: "Immigration Litigation Specialist",
        description: "Immigration Litigation Specialist",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 171,
        group: "agent",
        name: "Immigration Advisor",
        description: "Immigration Advisor",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 172,
        group: "agent",
        name: "Canada Immigration Advisor",
        description: "Canada Immigration Advisor",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 173,
        group: "agent",
        name: "UK Immigration Consultant",
        description: "UK Immigration Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 174,
        group: "agent",
        name: "Europe/Schengen Immigration Advisor",
        description: "Europe/Schengen Immigration Advisor",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 175,
        group: "agent",
        name: "USA Immigration & Green Card Consultant",
        description: "USA Immigration & Green Card Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 176,
        group: "agent",
        name: "Australian/New Zealand Visa Expert",
        description: "Australian/New Zealand Visa Expert",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 177,
        group: "agent",
        name: "Document Clearing Specialist",
        description: "Document Clearing Specialist",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 178,
        group: "agent",
        name: "Translation & Notarization Agent",
        description: "Translation & Notarization Agent",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 179,
        group: "agent",
        name: "Attestation Services Provider",
        description: "Attestation Services Provider",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 180,
        group: "agent",
        name: "Medical Test & Emirates ID Facilitation Agent",
        description: "Medical Test & Emirates ID Facilitation Agent",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Insurance Providers
      {
        id: 181,
        group: "agent",
        name: "Insurance Agent",
        description: "Insurance Agent",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 182,
        group: "agent",
        name: "Insurance Broker",
        description: "Insurance Broker",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 183,
        group: "agent",
        name: "Insurance Consultant",
        description: "Insurance Consultant",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 184,
        group: "agent",
        name: "Insurance Sales Executive",
        description: "Insurance Sales Executive",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 185,
        group: "agent",
        name: "Bancassurance Advisor",
        description: "Bancassurance Advisor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 186,
        group: "agent",
        name: "Telesales Insurance Representative",
        description: "Telesales Insurance Representative",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 187,
        group: "agent",
        name: "Independent Insurance Representative",
        description: "Independent Insurance Representative",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 188,
        group: "agent",
        name: "Life Insurance Advisor",
        description: "Life Insurance Advisor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 189,
        group: "agent",
        name: "Health Insurance Advisor",
        description: "Health Insurance Advisor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 190,
        group: "agent",
        name: "General Insurance Agent",
        description: "General Insurance Agent",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 191,
        group: "agent",
        name: "Corporate Insurance Consultant",
        description: "Corporate Insurance Consultant",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 192,
        group: "agent",
        name: "Claims Adjuster",
        description: "Claims Adjuster",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 193,
        group: "agent",
        name: "Claims Assessor",
        description: "Claims Assessor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 194,
        group: "agent",
        name: "Claims Handler",
        description: "Claims Handler",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 195,
        group: "agent",
        name: "Insurance Customer Service Representative",
        description: "Insurance Customer Service Representative",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 196,
        group: "agent",
        name: "Policy Renewal Agent",
        description: "Policy Renewal Agent",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 197,
        group: "agent",
        name: "Underwriting Assistant",
        description: "Underwriting Assistant",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 198,
        group: "agent",
        name: "Underwriter",
        description: "Underwriter",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 199,
        group: "agent",
        name: "Risk Analyst",
        description: "Risk Analyst",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 200,
        group: "agent",
        name: "Actuarial Analyst",
        description: "Actuarial Analyst",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 201,
        group: "agent",
        name: "Reinsurance Specialist",
        description: "Reinsurance Specialist",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 202,
        group: "agent",
        name: "Marine Insurance Consultant",
        description: "Marine Insurance Consultant",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 203,
        group: "agent",
        name: "Property & Casualty Insurance Expert",
        description: "Property & Casualty Insurance Expert",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 204,
        group: "agent",
        name: "Cyber Insurance Advisor",
        description: "Cyber Insurance Advisor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 205,
        group: "agent",
        name: "Travel Insurance Agent",
        description: "Travel Insurance Agent",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 206,
        group: "agent",
        name: "Insurance Compliance Officer",
        description: "Insurance Compliance Officer",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 207,
        group: "agent",
        name: "Insurance Auditor",
        description: "Insurance Auditor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 208,
        group: "agent",
        name: "Policy Documentation Specialist",
        description: "Policy Documentation Specialist",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 209,
        group: "agent",
        name: "Regulatory Affairs Consultant (Insurance)",
        description: "Regulatory Affairs Consultant (Insurance)",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 210,
        group: "agent",
        name: "Freelance Insurance Broker",
        description: "Freelance Insurance Broker",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 211,
        group: "agent",
        name: "Mobile Insurance Advisor",
        description: "Mobile Insurance Advisor",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 212,
        group: "agent",
        name: "Part-Time Policy Seller",
        description: "Part-Time Policy Seller",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 213,
        group: "agent",
        name: "Self-Employed Insurance Agent",
        description: "Self-Employed Insurance Agent",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Marketing & Advertising
      {
        id: 214,
        group: "agent",
        name: "Digital Marketing Consultant",
        description: "Digital Marketing Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 215,
        group: "agent",
        name: "Brand Strategy Consultant",
        description: "Brand Strategy Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 216,
        group: "agent",
        name: "SEO/SEM Specialist",
        description: "SEO/SEM Specialist",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 217,
        group: "agent",
        name: "Marketing Automation Expert",
        description: "Marketing Automation Expert",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 218,
        group: "agent",
        name: "Email Marketing Specialist",
        description: "Email Marketing Specialist",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 219,
        group: "agent",
        name: "Influencer Marketing Consultant",
        description: "Influencer Marketing Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 220,
        group: "agent",
        name: "Growth Marketing Advisor",
        description: "Growth Marketing Advisor",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 221,
        group: "agent",
        name: "Product Marketing Specialist",
        description: "Product Marketing Specialist",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 222,
        group: "agent",
        name: "Influencers",
        description: "Influencers",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 223,
        group: "agent",
        name: "Social Media Marketing Consultant",
        description: "Social Media Marketing Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 224,
        group: "agent",
        name: "Social Media Management Agency",
        description: "Social Media Management Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 225,
        group: "agent",
        name: "Paid Social Advertising Expert",
        description: "Paid Social Advertising Expert",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 226,
        group: "agent",
        name: "Community Engagement Specialist",
        description: "Community Engagement Specialist",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 227,
        group: "agent",
        name: "Short-Form Video Strategist (TikTok, Reels, etc.)",
        description: "Short-Form Video Strategist (TikTok, Reels, etc.)",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 228,
        group: "agent",
        name: "SEO Expert (Search Engine Optimization)",
        description: "SEO Expert (Search Engine Optimization)",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 229,
        group: "agent",
        name: "PPC Specialist (Pay-Per-Click)",
        description: "PPC Specialist (Pay-Per-Click)",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 230,
        group: "agent",
        name: "Google Ads Certified Consultant",
        description: "Google Ads Certified Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 231,
        group: "agent",
        name: "Conversion Rate Optimization (CRO) Expert",
        description: "Conversion Rate Optimization (CRO) Expert",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 232,
        group: "agent",
        name: "Billboard Advertising Provider",
        description: "Billboard Advertising Provider",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 233,
        group: "agent",
        name: "Radio & TV Advertising Consultant",
        description: "Radio & TV Advertising Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 234,
        group: "agent",
        name: "Print & Magazine Advertising Expert",
        description: "Print & Magazine Advertising Expert",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 235,
        group: "agent",
        name: "Event Marketing Agent",
        description: "Event Marketing Agent",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 236,
        group: "agent",
        name: "Copywriting & Content Creation Specialist",
        description: "Copywriting & Content Creation Specialist",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 237,
        group: "agent",
        name: "Graphic Design Consultant",
        description: "Graphic Design Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 238,
        group: "agent",
        name: "Creative Direction & Art Director",
        description: "Creative Direction & Art Director",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 239,
        group: "agent",
        name: "Video Production Agency",
        description: "Video Production Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 240,
        group: "agent",
        name: "Photography & Videography Services",
        description: "Photography & Videography Services",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 241,
        group: "agent",
        name: "Real Estate Marketing Expert",
        description: "Real Estate Marketing Expert",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 242,
        group: "agent",
        name: "Luxury Brand Marketing Consultant",
        description: "Luxury Brand Marketing Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 243,
        group: "agent",
        name: "Franchise Marketing Specialist",
        description: "Franchise Marketing Specialist",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 244,
        group: "agent",
        name: "E-commerce Marketing Consultant",
        description: "E-commerce Marketing Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 245,
        group: "agent",
        name: "SaaS/Tech Marketing Consultant",
        description: "SaaS/Tech Marketing Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 246,
        group: "agent",
        name: "Marketing Analyst",
        description: "Marketing Analyst",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 247,
        group: "agent",
        name: "Market Research Expert",
        description: "Market Research Expert",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 248,
        group: "agent",
        name: "Consumer Behavior Analyst",
        description: "Consumer Behavior Analyst",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 249,
        group: "agent",
        name: "Marketing Data Consultant",
        description: "Marketing Data Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Legal & Compliance
      {
        id: 250,
        group: "agent",
        name: "Corporate Lawyer",
        description: "Corporate Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 251,
        group: "agent",
        name: "Criminal Defense Lawyer",
        description: "Criminal Defense Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 252,
        group: "agent",
        name: "Civil Litigation Lawyer",
        description: "Civil Litigation Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 253,
        group: "agent",
        name: "Family & Divorce Lawyer",
        description: "Family & Divorce Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 254,
        group: "agent",
        name: "Labor & Employment Lawyer",
        description: "Labor & Employment Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 255,
        group: "agent",
        name: "Real Estate Lawyer",
        description: "Real Estate Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 256,
        group: "agent",
        name: "Commercial Contracts Lawyer",
        description: "Commercial Contracts Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 257,
        group: "agent",
        name: "Intellectual Property (IP) Lawyer",
        description: "Intellectual Property (IP) Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 258,
        group: "agent",
        name: "Immigration & Visa Lawyer",
        description: "Immigration & Visa Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 259,
        group: "agent",
        name: "Arbitration & Dispute Resolution Specialist",
        description: "Arbitration & Dispute Resolution Specialist",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 260,
        group: "agent",
        name: "Notary Public / Legal Attestation Specialist",
        description: "Notary Public / Legal Attestation Specialist",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 261,
        group: "agent",
        name: "Compliance Officer / Consultant",
        description: "Compliance Officer / Consultant",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 262,
        group: "agent",
        name: "Risk Management Specialist",
        description: "Risk Management Specialist",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 263,
        group: "agent",
        name: "AML (Anti-Money Laundering) Consultant",
        description: "AML (Anti-Money Laundering) Consultant",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 264,
        group: "agent",
        name: "KYC (Know Your Customer) Compliance Specialist",
        description: "KYC (Know Your Customer) Compliance Specialist",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 265,
        group: "agent",
        name: "Data Protection & Privacy Officer (DPO)",
        description: "Data Protection & Privacy Officer (DPO)",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 266,
        group: "agent",
        name: "Regulatory Affairs Consultant",
        description: "Regulatory Affairs Consultant",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 267,
        group: "agent",
        name: "Trade Compliance Advisor",
        description: "Trade Compliance Advisor",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 268,
        group: "agent",
        name: "Startup & Business Setup Legal Advisor",
        description: "Startup & Business Setup Legal Advisor",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 269,
        group: "agent",
        name: "Franchise Agreement Consultant",
        description: "Franchise Agreement Consultant",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 270,
        group: "agent",
        name: "Mergers & Acquisitions (M&A) Lawyer",
        description: "Mergers & Acquisitions (M&A) Lawyer",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 271,
        group: "agent",
        name: "Tax Law & Compliance Advisor",
        description: "Tax Law & Compliance Advisor",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 272,
        group: "agent",
        name: "Maritime & Shipping Law Expert",
        description: "Maritime & Shipping Law Expert",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 273,
        group: "agent",
        name: "Banking & Finance Legal Advisor",
        description: "Banking & Finance Legal Advisor",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 274,
        group: "agent",
        name: "Technology & Cyber Law Consultant",
        description: "Technology & Cyber Law Consultant",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Maintenance & Repair
      {
        id: 275,
        group: "agent",
        name: "General Maintenance Technician",
        description: "General Maintenance Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 276,
        group: "agent",
        name: "General Maintenance Manager",
        description: "General Maintenance Manager",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 277,
        group: "agent",
        name: "Annual Maintenance Contract (AMC) Provider",
        description: "Annual Maintenance Contract (AMC) Provider",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 278,
        group: "agent",
        name: "Electrician (Certified)",
        description: "Electrician (Certified)",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 279,
        group: "agent",
        name: "Home Electrical Inspection Specialist",
        description: "Home Electrical Inspection Specialist",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 280,
        group: "agent",
        name: "Lighting & Fixture Installation Expert",
        description: "Lighting & Fixture Installation Expert",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 281,
        group: "agent",
        name: "Plumber",
        description: "Plumber",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 282,
        group: "agent",
        name: "Drainage & Sewer Specialist",
        description: "Drainage & Sewer Specialist",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 283,
        group: "agent",
        name: "Water Pump Installation & Repair",
        description: "Water Pump Installation & Repair",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 284,
        group: "agent",
        name: "Plumbing Maintenance Company",
        description: "Plumbing Maintenance Company",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 285,
        group: "agent",
        name: "AC Technician",
        description: "AC Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 286,
        group: "agent",
        name: "HVAC Engineer",
        description: "HVAC Engineer",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 287,
        group: "agent",
        name: "AC Installation & Repair",
        description: "AC Installation & Repair",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 288,
        group: "agent",
        name: "Duct Cleaning & Ventilation Services",
        description: "Duct Cleaning & Ventilation Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 289,
        group: "agent",
        name: "Painter (Residential/Commercial)",
        description: "Painter (Residential/Commercial)",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 290,
        group: "agent",
        name: "Wallpaper & Wall Décor Expert",
        description: "Wallpaper & Wall Décor Expert",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 291,
        group: "agent",
        name: "Renovation Contractor",
        description: "Renovation Contractor",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 292,
        group: "agent",
        name: "Interior Finishing Specialist",
        description: "Interior Finishing Specialist",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 293,
        group: "agent",
        name: "Refrigerator Repair Expert",
        description: "Refrigerator Repair Expert",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 294,
        group: "agent",
        name: "Washing Machine Repair Technician",
        description: "Washing Machine Repair Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 295,
        group: "agent",
        name: "Dishwasher & Oven Repair Specialist",
        description: "Dishwasher & Oven Repair Specialist",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 296,
        group: "agent",
        name: "Home Appliances Repair Company",
        description: "Home Appliances Repair Company",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 297,
        group: "agent",
        name: "Carpenter (Residential/Commercial)",
        description: "Carpenter (Residential/Commercial)",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 298,
        group: "agent",
        name: "Furniture Repair Specialist",
        description: "Furniture Repair Specialist",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 299,
        group: "agent",
        name: "Custom Woodwork & Joinery Services",
        description: "Custom Woodwork & Joinery Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 300,
        group: "agent",
        name: "General Cleaning Services",
        description: "General Cleaning Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 301,
        group: "agent",
        name: "Deep Cleaning Expert",
        description: "Deep Cleaning Expert",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 302,
        group: "agent",
        name: "Pest Control Technician",
        description: "Pest Control Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 303,
        group: "agent",
        name: "Disinfection & Sanitization Provider",
        description: "Disinfection & Sanitization Provider",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 304,
        group: "agent",
        name: "Computer/Laptop Repair Technician",
        description: "Computer/Laptop Repair Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 305,
        group: "agent",
        name: "Mobile & Tablet Repair Services",
        description: "Mobile & Tablet Repair Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 306,
        group: "agent",
        name: "Network & Cable Repair Specialist",
        description: "Network & Cable Repair Specialist",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 307,
        group: "agent",
        name: "CCTV & Security System Technician",
        description: "CCTV & Security System Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 308,
        group: "agent",
        name: "Swimming Pool Maintenance",
        description: "Swimming Pool Maintenance",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 309,
        group: "agent",
        name: "Water Tank Cleaning Services",
        description: "Water Tank Cleaning Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 310,
        group: "agent",
        name: "Glass & Aluminium Repair Technician",
        description: "Glass & Aluminium Repair Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 311,
        group: "agent",
        name: "Roofing & Waterproofing Expert",
        description: "Roofing & Waterproofing Expert",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 312,
        group: "agent",
        name: "Lift Technician",
        description: "Lift Technician",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 313,
        group: "agent",
        name: "Elevator Repair Company",
        description: "Elevator Repair Company",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 314,
        group: "agent",
        name: "Escalator Maintenance Services",
        description: "Escalator Maintenance Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Logistics & Transportation
      {
        id: 315,
        group: "agent",
        name: "Freight Forwarder",
        description: "Freight Forwarder",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 316,
        group: "agent",
        name: "International Shipping Agent",
        description: "International Shipping Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 317,
        group: "agent",
        name: "Freight Broker",
        description: "Freight Broker",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 318,
        group: "agent",
        name: "Air Cargo Agent",
        description: "Air Cargo Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 319,
        group: "agent",
        name: "Sea Cargo Agent",
        description: "Sea Cargo Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 320,
        group: "agent",
        name: "Customs Clearance Agent",
        description: "Customs Clearance Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 321,
        group: "agent",
        name: "3PL (Third-Party Logistics) Provider",
        description: "3PL (Third-Party Logistics) Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 322,
        group: "agent",
        name: "Cold Chain Logistics Specialist",
        description: "Cold Chain Logistics Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 323,
        group: "agent",
        name: "Last Mile Delivery Provider",
        description: "Last Mile Delivery Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 324,
        group: "agent",
        name: "Warehousing & Storage Solutions",
        description: "Warehousing & Storage Solutions",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 325,
        group: "agent",
        name: "Express Delivery Agent",
        description: "Express Delivery Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 326,
        group: "agent",
        name: "Parcel Pickup & Drop Specialist",
        description: "Parcel Pickup & Drop Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 327,
        group: "agent",
        name: "School Transportation Services",
        description: "School Transportation Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 328,
        group: "agent",
        name: "Employee Transportation Provider",
        description: "Employee Transportation Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 329,
        group: "agent",
        name: "Tour Bus Operator",
        description: "Tour Bus Operator",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 330,
        group: "agent",
        name: "Luxury Vehicle Transport Services",
        description: "Luxury Vehicle Transport Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 331,
        group: "agent",
        name: "Car Rental Company",
        description: "Car Rental Company",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 332,
        group: "agent",
        name: "Commercial Vehicle Leasing",
        description: "Commercial Vehicle Leasing",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 333,
        group: "agent",
        name: "Truck & Trailer Rental Services",
        description: "Truck & Trailer Rental Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 334,
        group: "agent",
        name: "Chauffeur & Driver Service Agency",
        description: "Chauffeur & Driver Service Agency",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 335,
        group: "agent",
        name: "Household Movers",
        description: "Household Movers",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 336,
        group: "agent",
        name: "Office Relocation Specialists",
        description: "Office Relocation Specialists",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 337,
        group: "agent",
        name: "International Moving Agent/Manager",
        description: "International Moving Agent/Manager",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 338,
        group: "agent",
        name: "Packing & Unpacking Services",
        description: "Packing & Unpacking Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 339,
        group: "agent",
        name: "Heavy Equipment Transportation",
        description: "Heavy Equipment Transportation",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 340,
        group: "agent",
        name: "Vehicle Shipping Specialist",
        description: "Vehicle Shipping Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 341,
        group: "agent",
        name: "Medical & Pharmaceutical Logistics",
        description: "Medical & Pharmaceutical Logistics",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 342,
        group: "agent",
        name: "Event Logistics Provider",
        description: "Event Logistics Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 343,
        group: "agent",
        name: "Customs Documentation Specialist",
        description: "Customs Documentation Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 344,
        group: "agent",
        name: "Import/Export Compliance Agent",
        description: "Import/Export Compliance Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 345,
        group: "agent",
        name: "Free Zone Logistics Facilitator",
        description: "Free Zone Logistics Facilitator",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Travel & Hospitality
      {
        id: 346,
        group: "agent",
        name: "Travel Agent",
        description: "Travel Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 347,
        group: "agent",
        name: "Travel Consultant",
        description: "Travel Consultant",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 348,
        group: "agent",
        name: "Hajj & Umrah Travel Organizer",
        description: "Hajj & Umrah Travel Organizer",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 349,
        group: "agent",
        name: "Luxury Travel Advisor",
        description: "Luxury Travel Advisor",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 350,
        group: "agent",
        name: "Group Tour Operator",
        description: "Group Tour Operator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 351,
        group: "agent",
        name: "Cruise Booking Agent",
        description: "Cruise Booking Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 352,
        group: "agent",
        name: "Adventure & Safari Tour Specialist",
        description: "Adventure & Safari Tour Specialist",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 353,
        group: "agent",
        name: "Student & Educational Travel Coordinator",
        description: "Student & Educational Travel Coordinator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 354,
        group: "agent",
        name: "Airline Ticketing Agent",
        description: "Airline Ticketing Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 355,
        group: "agent",
        name: "Visa Application & Processing Agent",
        description: "Visa Application & Processing Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 356,
        group: "agent",
        name: "Travel Insurance Provider",
        description: "Travel Insurance Provider",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 357,
        group: "agent",
        name: "Holiday Package Specialist",
        description: "Holiday Package Specialist",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 358,
        group: "agent",
        name: "Corporate Travel Coordinator",
        description: "Corporate Travel Coordinator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 359,
        group: "agent",
        name: "International Travel Planner",
        description: "International Travel Planner",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 360,
        group: "agent",
        name: "Hotel Booking Agent",
        description: "Hotel Booking Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 361,
        group: "agent",
        name: "Event Venue Booking Agent",
        description: "Event Venue Booking Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 362,
        group: "agent",
        name: "Resort & Retreat Coordinator",
        description: "Resort & Retreat Coordinator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 363,
        group: "agent",
        name: "Vacation Home Rental Agency",
        description: "Vacation Home Rental Agency",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 364,
        group: "agent",
        name: "Serviced Apartments Provider",
        description: "Serviced Apartments Provider",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 365,
        group: "agent",
        name: "Airport Pickup & Drop Service",
        description: "Airport Pickup & Drop Service",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 366,
        group: "agent",
        name: "Hotel Shuttle Service",
        description: "Hotel Shuttle Service",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 367,
        group: "agent",
        name: "Chauffeur & Limousine Service",
        description: "Chauffeur & Limousine Service",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 368,
        group: "agent",
        name: "Tour Bus Operator",
        description: "Tour Bus Operator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 369,
        group: "agent",
        name: "Yacht & Boat Rental Agent",
        description: "Yacht & Boat Rental Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 370,
        group: "agent",
        name: "MICE (Meetings, Incentives, Conferences, Exhibitions) Organizer",
        description:
          "MICE (Meetings, Incentives, Conferences, Exhibitions) Organizer",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 371,
        group: "agent",
        name: "Destination Wedding Planner",
        description: "Destination Wedding Planner",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 372,
        group: "agent",
        name: "Medical Tourism Facilitator",
        description: "Medical Tourism Facilitator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 373,
        group: "agent",
        name: "Cultural & Heritage Tour Guide",
        description: "Cultural & Heritage Tour Guide",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 374,
        group: "agent",
        name: "Eco-Tourism Specialist",
        description: "Eco-Tourism Specialist",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Post-Purchase / Rental
      {
        id: 375,
        group: "agent",
        name: "Relocation Consultant",
        description: "Relocation Consultant",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 376,
        group: "agent",
        name: "House Moving Services",
        description: "House Moving Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 377,
        group: "agent",
        name: "Packing & Unpacking Services",
        description: "Packing & Unpacking Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 378,
        group: "agent",
        name: "Furniture Assembly Expert",
        description: "Furniture Assembly Expert",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 379,
        group: "agent",
        name: "Temporary Storage Solutions",
        description: "Temporary Storage Solutions",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 380,
        group: "agent",
        name: "Internet & Telecom Connection Agent",
        description: "Internet & Telecom Connection Agent",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 381,
        group: "agent",
        name: "Utility Connection Support (Electricity, Water, Gas)",
        description: "Utility Connection Support (Electricity, Water, Gas)",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 382,
        group: "agent",
        name: "Satellite TV Installer",
        description: "Satellite TV Installer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 383,
        group: "agent",
        name: "Smart Home Setup Specialist",
        description: "Smart Home Setup Specialist",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 384,
        group: "agent",
        name: "Home Security System Installer",
        description: "Home Security System Installer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 385,
        group: "agent",
        name: "Deep Cleaning Services",
        description: "Deep Cleaning Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 386,
        group: "agent",
        name: "Post-Construction Cleaning",
        description: "Post-Construction Cleaning",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 387,
        group: "agent",
        name: "Pest Control Services",
        description: "Pest Control Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 388,
        group: "agent",
        name: "Sanitization & Disinfection Services",
        description: "Sanitization & Disinfection Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 389,
        group: "agent",
        name: "Water Tank Cleaning Services",
        description: "Water Tank Cleaning Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 390,
        group: "agent",
        name: "Interior Designer",
        description: "Interior Designer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 391,
        group: "agent",
        name: "Curtain & Blinds Installer",
        description: "Curtain & Blinds Installer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 392,
        group: "agent",
        name: "Landscaping & Gardening Services",
        description: "Landscaping & Gardening Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 393,
        group: "agent",
        name: "Swimming Pool Maintenance",
        description: "Swimming Pool Maintenance",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 394,
        group: "agent",
        name: "Painting & Wall Treatment Services",
        description: "Painting & Wall Treatment Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 395,
        group: "agent",
        name: "AC Servicing & Repair",
        description: "AC Servicing & Repair",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 396,
        group: "agent",
        name: "Plumbing Services",
        description: "Plumbing Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 397,
        group: "agent",
        name: "Electrical Services",
        description: "Electrical Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 398,
        group: "agent",
        name: "Handyman Services",
        description: "Handyman Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 399,
        group: "agent",
        name: "Appliance Installation & Repair",
        description: "Appliance Installation & Repair",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 400,
        group: "agent",
        name: "Home Insurance Consultant",
        description: "Home Insurance Consultant",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 401,
        group: "agent",
        name: "Homeowners Association (HOA) Consultant",
        description: "Homeowners Association (HOA) Consultant",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 402,
        group: "agent",
        name: "Waste Management & Recycling Setup",
        description: "Waste Management & Recycling Setup",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 403,
        group: "agent",
        name: "Storage Shed/Container Provider",
        description: "Storage Shed/Container Provider",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 404,
        group: "agent",
        name: "Vehicle Registration & Parking Setup Consultant",
        description: "Vehicle Registration & Parking Setup Consultant",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Human Resources & Recruitment
      {
        id: 405,
        group: "agent",
        name: "General Recruitment Agent",
        description: "General Recruitment Agent",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 406,
        group: "agent",
        name: "Executive Search Consultant",
        description: "Executive Search Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 407,
        group: "agent",
        name: "Temporary Staffing Provider",
        description: "Temporary Staffing Provider",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 408,
        group: "agent",
        name: "Blue-Collar Recruitment Specialist",
        description: "Blue-Collar Recruitment Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 409,
        group: "agent",
        name: "Freelance & Gig Economy Recruiter",
        description: "Freelance & Gig Economy Recruiter",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 410,
        group: "agent",
        name: "Overseas Recruitment Consultant",
        description: "Overseas Recruitment Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 411,
        group: "agent",
        name: "Government Sector Recruitment Advisor",
        description: "Government Sector Recruitment Advisor",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 412,
        group: "agent",
        name: "Emiratisation Recruitment Specialist",
        description: "Emiratisation Recruitment Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 413,
        group: "agent",
        name: "HR Policy & Strategy Consultant",
        description: "HR Policy & Strategy Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 414,
        group: "agent",
        name: "Labour Law Compliance Consultant",
        description: "Labour Law Compliance Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 415,
        group: "agent",
        name: "HR Auditing Services",
        description: "HR Auditing Services",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 416,
        group: "agent",
        name: "Workforce Planning Consultant",
        description: "Workforce Planning Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 417,
        group: "agent",
        name: "Performance Management Consultant",
        description: "Performance Management Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 418,
        group: "agent",
        name: "Compensation & Benefits Advisor",
        description: "Compensation & Benefits Advisor",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 419,
        group: "agent",
        name: "Corporate Training Provider",
        description: "Corporate Training Provider",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 420,
        group: "agent",
        name: "Soft Skills Trainer",
        description: "Soft Skills Trainer",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 421,
        group: "agent",
        name: "Technical Skills Trainer",
        description: "Technical Skills Trainer",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 422,
        group: "agent",
        name: "Leadership & Executive Coach",
        description: "Leadership & Executive Coach",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 423,
        group: "agent",
        name: "Team Building Specialist",
        description: "Team Building Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 424,
        group: "agent",
        name: "Diversity & Inclusion Consultant",
        description: "Diversity & Inclusion Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 425,
        group: "agent",
        name: "HR Outsourcing Firm (HRO)",
        description: "HR Outsourcing Firm (HRO)",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 426,
        group: "agent",
        name: "Payroll Management Services",
        description: "Payroll Management Services",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 427,
        group: "agent",
        name: "Employee Onboarding & Exit Management",
        description: "Employee Onboarding & Exit Management",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 428,
        group: "agent",
        name: "Time & Attendance Management Provider",
        description: "Time & Attendance Management Provider",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 429,
        group: "agent",
        name: "HR Software & Systems Integrator",
        description: "HR Software & Systems Integrator",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 430,
        group: "agent",
        name: "Healthcare Recruitment Specialist",
        description: "Healthcare Recruitment Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 431,
        group: "agent",
        name: "IT & Tech Talent Recruiter",
        description: "IT & Tech Talent Recruiter",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 432,
        group: "agent",
        name: "Hospitality & Tourism Staffing",
        description: "Hospitality & Tourism Staffing",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 433,
        group: "agent",
        name: "Construction & Engineering Recruitment",
        description: "Construction & Engineering Recruitment",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 434,
        group: "agent",
        name: "Education & Academic Recruitment",
        description: "Education & Academic Recruitment",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Retail & E-Commerce
      {
        id: 435,
        group: "agent",
        name: "Retail Business Consultant",
        description: "Retail Business Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 436,
        group: "agent",
        name: "Store Design & Merchandising Expert",
        description: "Store Design & Merchandising Expert",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 437,
        group: "agent",
        name: "POS (Point-of-Sale) Systems Provider",
        description: "POS (Point-of-Sale) Systems Provider",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 438,
        group: "agent",
        name: "Inventory Management Consultant",
        description: "Inventory Management Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 439,
        group: "agent",
        name: "Franchise Consultant",
        description: "Franchise Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 440,
        group: "agent",
        name: "Retail Staff Recruitment Specialist",
        description: "Retail Staff Recruitment Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 441,
        group: "agent",
        name: "E-commerce Platform Developer (Shopify, Magento, WooCommerce, etc.)",
        description:
          "E-commerce Platform Developer (Shopify, Magento, WooCommerce, etc.)",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 442,
        group: "agent",
        name: "Website & Mobile App Developer",
        description: "Website & Mobile App Developer",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 443,
        group: "agent",
        name: "Marketplace Setup Expert (Amazon, Noon, etc.)",
        description: "Marketplace Setup Expert (Amazon, Noon, etc.)",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 444,
        group: "agent",
        name: "Drop shipping Consultant",
        description: "Drop shipping Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 445,
        group: "agent",
        name: "Payment Gateway Integration Specialist",
        description: "Payment Gateway Integration Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 446,
        group: "agent",
        name: "E-commerce Marketing Specialist",
        description: "E-commerce Marketing Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 447,
        group: "agent",
        name: "SEO/SEM Expert for Online Stores",
        description: "SEO/SEM Expert for Online Stores",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 448,
        group: "agent",
        name: "Social Media Marketing for Retail",
        description: "Social Media Marketing for Retail",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 449,
        group: "agent",
        name: "Email Marketing & Automation Expert",
        description: "Email Marketing & Automation Expert",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 450,
        group: "agent",
        name: "Influencer & Affiliate Marketing Manager",
        description: "Influencer & Affiliate Marketing Manager",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 451,
        group: "agent",
        name: "Product Photographer/Videographer",
        description: "Product Photographer/Videographer",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 452,
        group: "agent",
        name: "Graphic Designer for Online Stores",
        description: "Graphic Designer for Online Stores",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 453,
        group: "agent",
        name: "Branding & Packaging Consultant",
        description: "Branding & Packaging Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 454,
        group: "agent",
        name: "UX/UI Designer for Retail Platforms",
        description: "UX/UI Designer for Retail Platforms",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 455,
        group: "agent",
        name: "E-commerce Fulfilment Service",
        description: "E-commerce Fulfilment Service",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 456,
        group: "agent",
        name: "Logistics & Last-Mile Delivery Partner",
        description: "Logistics & Last-Mile Delivery Partner",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 457,
        group: "agent",
        name: "Customer Support & Live Chat Outsourcing",
        description: "Customer Support & Live Chat Outsourcing",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 458,
        group: "agent",
        name: "Return Management & Reverse Logistics Specialist",
        description: "Return Management & Reverse Logistics Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 459,
        group: "agent",
        name: "Warehouse Setup & Automation Consultant",
        description: "Warehouse Setup & Automation Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 460,
        group: "agent",
        name: "Sales & Conversion Rate Optimization Expert",
        description: "Sales & Conversion Rate Optimization Expert",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 461,
        group: "agent",
        name: "Retail Data Analyst",
        description: "Retail Data Analyst",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 462,
        group: "agent",
        name: "Consumer Behaviour Consultant",
        description: "Consumer Behaviour Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 463,
        group: "agent",
        name: "Pricing Strategy Specialist",
        description: "Pricing Strategy Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 464,
        group: "agent",
        name: "Retail Loyalty Program Consultant",
        description: "Retail Loyalty Program Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Commercial Real Estate
      {
        id: 465,
        group: "agent",
        name: "Commercial Real Estate Broker",
        description: "Commercial Real Estate Broker",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 466,
        group: "agent",
        name: "Leasing Agent – Retail Spaces",
        description: "Leasing Agent – Retail Spaces",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 467,
        group: "agent",
        name: "Leasing Agent – Office Spaces",
        description: "Leasing Agent – Office Spaces",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 468,
        group: "agent",
        name: "Leasing Agent – Industrial & Warehouse",
        description: "Leasing Agent – Industrial & Warehouse",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 469,
        group: "agent",
        name: "Mixed-Use Property Agent",
        description: "Mixed-Use Property Agent",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 470,
        group: "agent",
        name: "Commercial Property Investment Consultant",
        description: "Commercial Property Investment Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 471,
        group: "agent",
        name: "Commercial Real Estate Developer",
        description: "Commercial Real Estate Developer",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 472,
        group: "agent",
        name: "Build-to-Suit Specialist",
        description: "Build-to-Suit Specialist",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 473,
        group: "agent",
        name: "Urban Planning & Zoning Consultant",
        description: "Urban Planning & Zoning Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 474,
        group: "agent",
        name: "Real Estate Market Analyst (Commercial Sector)",
        description: "Real Estate Market Analyst (Commercial Sector)",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 475,
        group: "agent",
        name: "Commercial Real Estate Legal Advisor",
        description: "Commercial Real Estate Legal Advisor",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 476,
        group: "agent",
        name: "Commercial Lease Negotiation Expert",
        description: "Commercial Lease Negotiation Expert",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 477,
        group: "agent",
        name: "Title & Land Registration Consultant",
        description: "Title & Land Registration Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 478,
        group: "agent",
        name: "Regulatory & Permit Consultant",
        description: "Regulatory & Permit Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 479,
        group: "agent",
        name: "Commercial Mortgage Consultant",
        description: "Commercial Mortgage Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 480,
        group: "agent",
        name: "Loan Structuring Advisor",
        description: "Loan Structuring Advisor",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 481,
        group: "agent",
        name: "Commercial Property Valuation Expert",
        description: "Commercial Property Valuation Expert",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 482,
        group: "agent",
        name: "Cost-Benefit Analyst",
        description: "Cost-Benefit Analyst",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 483,
        group: "agent",
        name: "Cap Rate & ROI Consultant",
        description: "Cap Rate & ROI Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 484,
        group: "agent",
        name: "Commercial Property Manager",
        description: "Commercial Property Manager",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 485,
        group: "agent",
        name: "Facilities Management Consultant",
        description: "Facilities Management Consultant",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 486,
        group: "agent",
        name: "Tenant & Occupancy Management Specialist",
        description: "Tenant & Occupancy Management Specialist",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 487,
        group: "agent",
        name: "Lease Administration Professional",
        description: "Lease Administration Professional",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 488,
        group: "agent",
        name: "Rent Collection & Financial Reporting Expert",
        description: "Rent Collection & Financial Reporting Expert",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 489,
        group: "agent",
        name: "Commercial Property Marketing Specialist",
        description: "Commercial Property Marketing Specialist",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 490,
        group: "agent",
        name: "Commercial Real Estate Photographer/Videographer",
        description: "Commercial Real Estate Photographer/Videographer",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 491,
        group: "agent",
        name: "3D Virtual Tour & Drone Specialist",
        description: "3D Virtual Tour & Drone Specialist",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 492,
        group: "agent",
        name: "Branding for Commercial Projects",
        description: "Branding for Commercial Projects",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Real Estate Investment
      {
        id: 493,
        group: "agent",
        name: "Real Estate Investment Advisor",
        description: "Real Estate Investment Advisor",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 494,
        group: "agent",
        name: "Property Investment Broker",
        description: "Property Investment Broker",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 495,
        group: "agent",
        name: "Buy-to-Let Investment Consultant",
        description: "Buy-to-Let Investment Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 496,
        group: "agent",
        name: "Luxury Property Investment Specialist",
        description: "Luxury Property Investment Specialist",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 497,
        group: "agent",
        name: "Commercial Property Investment Broker",
        description: "Commercial Property Investment Broker",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 498,
        group: "agent",
        name: "Off-Plan Investment Consultant",
        description: "Off-Plan Investment Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 499,
        group: "agent",
        name: "International Property Investment Agent",
        description: "International Property Investment Agent",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 500,
        group: "agent",
        name: "REIT (Real Estate Investment Trust) Manager",
        description: "REIT (Real Estate Investment Trust) Manager",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 501,
        group: "agent",
        name: "Real Estate Fund Advisor",
        description: "Real Estate Fund Advisor",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 502,
        group: "agent",
        name: "Private Equity Real Estate Consultant",
        description: "Private Equity Real Estate Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 503,
        group: "agent",
        name: "Asset & Portfolio Manager",
        description: "Asset & Portfolio Manager",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 504,
        group: "agent",
        name: "Institutional Property Investment Consultant",
        description: "Institutional Property Investment Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 505,
        group: "agent",
        name: "Real Estate Legal Advisor",
        description: "Real Estate Legal Advisor",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 506,
        group: "agent",
        name: "Due Diligence Consultant",
        description: "Due Diligence Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 507,
        group: "agent",
        name: "Property Title & Transfer Specialist",
        description: "Property Title & Transfer Specialist",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 508,
        group: "agent",
        name: "Regulatory Compliance Advisor",
        description: "Regulatory Compliance Advisor",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 509,
        group: "agent",
        name: "Real Estate Tax Planning Consultant",
        description: "Real Estate Tax Planning Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 510,
        group: "agent",
        name: "Mortgage Consultant (for Investment Properties)",
        description: "Mortgage Consultant (for Investment Properties)",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 511,
        group: "agent",
        name: "Real Estate Finance Advisor",
        description: "Real Estate Finance Advisor",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 512,
        group: "agent",
        name: "Property Valuation Expert",
        description: "Property Valuation Expert",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 513,
        group: "agent",
        name: "Return-on-Investment (ROI) Consultant",
        description: "Return-on-Investment (ROI) Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 514,
        group: "agent",
        name: "Loan Structuring Expert for Investors",
        description: "Loan Structuring Expert for Investors",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 515,
        group: "agent",
        name: "Real Estate Development Consultant",
        description: "Real Estate Development Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 516,
        group: "agent",
        name: "Feasibility Study Specialist",
        description: "Feasibility Study Specialist",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 517,
        group: "agent",
        name: "Land Acquisition & Zoning Consultant",
        description: "Land Acquisition & Zoning Consultant",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 518,
        group: "agent",
        name: "Urban Development Strategist",
        description: "Urban Development Strategist",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 519,
        group: "agent",
        name: "Build-to-Rent Project Advisor",
        description: "Build-to-Rent Project Advisor",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 520,
        group: "agent",
        name: "Property Management",
        description: "Property Management",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 521,
        group: "agent",
        name: "Rental Income Management Expert",
        description: "Rental Income Management Expert",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 522,
        group: "agent",
        name: "Tenant Relations Specialist",
        description: "Tenant Relations Specialist",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Health & Beauty
      {
        id: 523,
        group: "agent",
        name: "General Health & Wellness Consultant",
        description: "General Health & Wellness Consultant",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 524,
        group: "agent",
        name: "Nutritionist / Dietitian",
        description: "Nutritionist / Dietitian",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 525,
        group: "agent",
        name: "Fitness Trainer / Personal Coach",
        description: "Fitness Trainer / Personal Coach",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 526,
        group: "agent",
        name: "Physiotherapist",
        description: "Physiotherapist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 527,
        group: "agent",
        name: "Alternative Medicine Practitioner (Ayurveda, Homeopathy, etc.)",
        description:
          "Alternative Medicine Practitioner (Ayurveda, Homeopathy, etc.)",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 528,
        group: "agent",
        name: "Chiropractor",
        description: "Chiropractor",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 529,
        group: "agent",
        name: "Mental Health Counselor / Therapist",
        description: "Mental Health Counselor / Therapist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 530,
        group: "agent",
        name: "Wellness Retreat & Detox Consultant",
        description: "Wellness Retreat & Detox Consultant",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 531,
        group: "agent",
        name: "Dermatology Clinic",
        description: "Dermatology Clinic",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 532,
        group: "agent",
        name: "Aesthetic / Cosmetic Surgery Clinic",
        description: "Aesthetic / Cosmetic Surgery Clinic",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 533,
        group: "agent",
        name: "Laser Hair Removal Specialist",
        description: "Laser Hair Removal Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 534,
        group: "agent",
        name: "Skincare & Anti-Aging Clinic",
        description: "Skincare & Anti-Aging Clinic",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 535,
        group: "agent",
        name: "Hair Transplant Center",
        description: "Hair Transplant Center",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 536,
        group: "agent",
        name: "Dental Aesthetics & Whitening Specialist",
        description: "Dental Aesthetics & Whitening Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 537,
        group: "agent",
        name: "Medical Spa / MediSpa",
        description: "Medical Spa / MediSpa",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 538,
        group: "agent",
        name: "Beauty Salon Owner / Operator",
        description: "Beauty Salon Owner / Operator",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 539,
        group: "agent",
        name: "Hair Stylist / Barber",
        description: "Hair Stylist / Barber",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 540,
        group: "agent",
        name: "Makeup Artist",
        description: "Makeup Artist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 541,
        group: "agent",
        name: "Nail Technician / Salon",
        description: "Nail Technician / Salon",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 542,
        group: "agent",
        name: "Eyebrow & Lash Technician",
        description: "Eyebrow & Lash Technician",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 543,
        group: "agent",
        name: "Bridal & Event Beauty Specialist",
        description: "Bridal & Event Beauty Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 544,
        group: "agent",
        name: "Massage Therapist",
        description: "Massage Therapist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 545,
        group: "agent",
        name: "Luxury Spa Owner / Manager",
        description: "Luxury Spa Owner / Manager",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 546,
        group: "agent",
        name: "Aromatherapy Specialist",
        description: "Aromatherapy Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 547,
        group: "agent",
        name: "Thai / Swedish Massage Expert",
        description: "Thai / Swedish Massage Expert",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 548,
        group: "agent",
        name: "Organic Beauty Brand Founder",
        description: "Organic Beauty Brand Founder",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 549,
        group: "agent",
        name: "Cosmetic Product Distributor",
        description: "Cosmetic Product Distributor",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 550,
        group: "agent",
        name: "Beauty Product Retailer / Online Seller",
        description: "Beauty Product Retailer / Online Seller",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 551,
        group: "agent",
        name: "Skincare Consultant",
        description: "Skincare Consultant",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 552,
        group: "agent",
        name: "Makeup Product Specialist",
        description: "Makeup Product Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 553,
        group: "agent",
        name: "Health & Beauty Blogger / Influencer",
        description: "Health & Beauty Blogger / Influencer",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 554,
        group: "agent",
        name: "Beauty Course Instructor / Trainer",
        description: "Beauty Course Instructor / Trainer",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 555,
        group: "agent",
        name: "Cosmetology School Representative",
        description: "Cosmetology School Representative",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Training & Education
      {
        id: 556,
        group: "agent",
        name: "School Consultant / Placement Advisor",
        description: "School Consultant / Placement Advisor",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 557,
        group: "agent",
        name: "University Admission Consultant",
        description: "University Admission Consultant",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 558,
        group: "agent",
        name: "Home Tutor (Primary/Secondary)",
        description: "Home Tutor (Primary/Secondary)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 559,
        group: "agent",
        name: "Online Tutor / E-learning Specialist",
        description: "Online Tutor / E-learning Specialist",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 560,
        group: "agent",
        name: "Test Preparation Expert (SAT, IELTS, TOEFL, etc.)",
        description: "Test Preparation Expert (SAT, IELTS, TOEFL, etc.)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 561,
        group: "agent",
        name: "Curriculum Designer / Academic Content Developer",
        description: "Curriculum Designer / Academic Content Developer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 562,
        group: "agent",
        name: "Private School Recruiter / Liaison",
        description: "Private School Recruiter / Liaison",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 563,
        group: "agent",
        name: "Corporate Trainer / Coach",
        description: "Corporate Trainer / Coach",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 564,
        group: "agent",
        name: "Leadership & Management Trainer",
        description: "Leadership & Management Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 565,
        group: "agent",
        name: "Sales & Marketing Skills Trainer",
        description: "Sales & Marketing Skills Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 566,
        group: "agent",
        name: "Soft Skills & Communication Coach",
        description: "Soft Skills & Communication Coach",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 567,
        group: "agent",
        name: "Customer Service & Hospitality Trainer",
        description: "Customer Service & Hospitality Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 568,
        group: "agent",
        name: "HR & Employee Development Specialist",
        description: "HR & Employee Development Specialist",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 569,
        group: "agent",
        name: "Health & Safety (HSE) Trainer",
        description: "Health & Safety (HSE) Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 570,
        group: "agent",
        name: "IT & Software Trainer",
        description: "IT & Software Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 571,
        group: "agent",
        name: "Digital Marketing Instructor",
        description: "Digital Marketing Instructor",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 572,
        group: "agent",
        name: "Coding Bootcamp Instructor",
        description: "Coding Bootcamp Instructor",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 573,
        group: "agent",
        name: "Graphic Design / Creative Software Trainer",
        description: "Graphic Design / Creative Software Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 574,
        group: "agent",
        name: "Engineering & Technical Skills Trainer",
        description: "Engineering & Technical Skills Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 575,
        group: "agent",
        name: "Trade Skills Trainer (Plumbing, Electrical, HVAC, etc.)",
        description: "Trade Skills Trainer (Plumbing, Electrical, HVAC, etc.)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 576,
        group: "agent",
        name: "Language Instructor (Arabic, English, French, etc.)",
        description: "Language Instructor (Arabic, English, French, etc.)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 577,
        group: "agent",
        name: "ESL (English as Second Language) Teacher",
        description: "ESL (English as Second Language) Teacher",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 578,
        group: "agent",
        name: "Sign Language Instructor",
        description: "Sign Language Instructor",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 579,
        group: "agent",
        name: "Cross-Cultural Training Specialist",
        description: "Cross-Cultural Training Specialist",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 580,
        group: "agent",
        name: "Translation & Interpretation Trainer",
        description: "Translation & Interpretation Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 581,
        group: "agent",
        name: "Training Center / Institute Owner",
        description: "Training Center / Institute Owner",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 582,
        group: "agent",
        name: "E-learning Platform Operator",
        description: "E-learning Platform Operator",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 583,
        group: "agent",
        name: "EdTech Solution Provider",
        description: "EdTech Solution Provider",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 584,
        group: "agent",
        name: "Education Franchise Consultant",
        description: "Education Franchise Consultant",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 585,
        group: "agent",
        name: "School or University Representative",
        description: "School or University Representative",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 586,
        group: "agent",
        name: "Certified Professional Trainer (CPT)",
        description: "Certified Professional Trainer (CPT)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 587,
        group: "agent",
        name: "Project Management (PMP, PRINCE2) Instructor",
        description: "Project Management (PMP, PRINCE2) Instructor",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 588,
        group: "agent",
        name: "Finance & Accounting (CPA, CFA) Trainer",
        description: "Finance & Accounting (CPA, CFA) Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 589,
        group: "agent",
        name: "IT Certifications Trainer (AWS, Microsoft, Cisco, etc.)",
        description: "IT Certifications Trainer (AWS, Microsoft, Cisco, etc.)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 590,
        group: "agent",
        name: "Special Needs Education Specialist",
        description: "Special Needs Education Specialist",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 591,
        group: "agent",
        name: "Speech and Language Therapist (Educational Context)",
        description: "Speech and Language Therapist (Educational Context)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 592,
        group: "agent",
        name: "Autism / Learning Difficulties Consultant",
        description: "Autism / Learning Difficulties Consultant",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 593,
        group: "agent",
        name: "Inclusive Education Advocate / Trainer",
        description: "Inclusive Education Advocate / Trainer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Hajj & Umrah
      {
        id: 594,
        group: "agent",
        name: "Licensed Hajj Operator",
        description: "Licensed Hajj Operator",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 595,
        group: "agent",
        name: "Licensed Umrah Operator",
        description: "Licensed Umrah Operator",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 596,
        group: "agent",
        name: "Hajj & Umrah Travel Consultant",
        description: "Hajj & Umrah Travel Consultant",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 597,
        group: "agent",
        name: "Group Organizer for Pilgrims",
        description: "Group Organizer for Pilgrims",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 598,
        group: "agent",
        name: "Pilgrimage Visa Specialist",
        description: "Pilgrimage Visa Specialist",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 599,
        group: "agent",
        name: "Hajj & Umrah Transport Coordinator",
        description: "Hajj & Umrah Transport Coordinator",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 600,
        group: "agent",
        name: "Hotel Booking & Accommodation Agent",
        description: "Hotel Booking & Accommodation Agent",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 601,
        group: "agent",
        name: "Local Transport & Transfers Agent",
        description: "Local Transport & Transfers Agent",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 602,
        group: "agent",
        name: "Zamzam Water Distribution Coordinator",
        description: "Zamzam Water Distribution Coordinator",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 603,
        group: "agent",
        name: "Spiritual Guide / Scholar",
        description: "Spiritual Guide / Scholar",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 604,
        group: "agent",
        name: "Pilgrimage Training Specialist",
        description: "Pilgrimage Training Specialist",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 605,
        group: "agent",
        name: "Hajj Group Leader / Mualim",
        description: "Hajj Group Leader / Mualim",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 606,
        group: "agent",
        name: "Educational Material Distributor",
        description: "Educational Material Distributor",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 607,
        group: "agent",
        name: "Health & Safety Advisor",
        description: "Health & Safety Advisor",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 608,
        group: "agent",
        name: "Insurance Provider",
        description: "Insurance Provider",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 609,
        group: "agent",
        name: "Special Needs Agent",
        description: "Special Needs Agent",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 610,
        group: "agent",
        name: "Hajj & Umrah App Developer / Provider",
        description: "Hajj & Umrah App Developer / Provider",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 611,
        group: "agent",
        name: "Digital Tawaf / Hajj Tracker Tools Provider",
        description: "Digital Tawaf / Hajj Tracker Tools Provider",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 612,
        group: "agent",
        name: "Online Booking Platform for Hajj & Umrah",
        description: "Online Booking Platform for Hajj & Umrah",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Events & Entertainment
      {
        id: 613,
        group: "agent",
        name: "Event Planners / Coordinators",
        description: "Event Planners / Coordinators",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 614,
        group: "agent",
        name: "Event Management Agencies",
        description: "Event Management Agencies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 615,
        group: "agent",
        name: "Wedding Planners",
        description: "Wedding Planners",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 616,
        group: "agent",
        name: "Party Planners",
        description: "Party Planners",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 617,
        group: "agent",
        name: "Destination Event Consultants",
        description: "Destination Event Consultants",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 618,
        group: "agent",
        name: "Talent Agents",
        description: "Talent Agents",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 619,
        group: "agent",
        name: "Booking Managers",
        description: "Booking Managers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 620,
        group: "agent",
        name: "Live Music Coordinators",
        description: "Live Music Coordinators",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 621,
        group: "agent",
        name: "DJ Agents / Agencies",
        description: "DJ Agents / Agencies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 622,
        group: "agent",
        name: "Influencer Booking Agents",
        description: "Influencer Booking Agents",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 623,
        group: "agent",
        name: "AV Technicians / Sound Engineers",
        description: "AV Technicians / Sound Engineers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 624,
        group: "agent",
        name: "Stage Designers / Decor Experts",
        description: "Stage Designers / Decor Experts",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 625,
        group: "agent",
        name: "Event Production Agencies",
        description: "Event Production Agencies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 626,
        group: "agent",
        name: "Virtual Event Producers",
        description: "Virtual Event Producers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 627,
        group: "agent",
        name: "Catering Agents / Agencies",
        description: "Catering Agents / Agencies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 628,
        group: "agent",
        name: "Host/Hostess Staffing Agencies",
        description: "Host/Hostess Staffing Agencies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 629,
        group: "agent",
        name: "VIP Concierge Services",
        description: "VIP Concierge Services",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 630,
        group: "agent",
        name: "Event Logistics Coordinators",
        description: "Event Logistics Coordinators",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 631,
        group: "agent",
        name: "Event Stylists / Decorators",
        description: "Event Stylists / Decorators",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 632,
        group: "agent",
        name: "Florists & Floral Designers",
        description: "Florists & Floral Designers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 633,
        group: "agent",
        name: "Furniture & Props Rental Agents",
        description: "Furniture & Props Rental Agents",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 634,
        group: "agent",
        name: "Corporate Event Planners",
        description: "Corporate Event Planners",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 635,
        group: "agent",
        name: "Team Building Consultants",
        description: "Team Building Consultants",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 636,
        group: "agent",
        name: "Trade Show & Exhibition Agents",
        description: "Trade Show & Exhibition Agents",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 637,
        group: "agent",
        name: "Religious / Cultural Ceremony Coordinators",
        description: "Religious / Cultural Ceremony Coordinators",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 638,
        group: "agent",
        name: "Kids Party Agents",
        description: "Kids Party Agents",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 639,
        group: "agent",
        name: "Themed Event Consultants",
        description: "Themed Event Consultants",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 640,
        group: "agent",
        name: "Venue Booking Agents",
        description: "Venue Booking Agents",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 641,
        group: "agent",
        name: "Event Licensing Consultants",
        description: "Event Licensing Consultants",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 642,
        group: "agent",
        name: "Security Agents / Bouncers",
        description: "Security Agents / Bouncers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Creative Services
      {
        id: 643,
        group: "agent",
        name: "Content Writers",
        description: "Content Writers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 644,
        group: "agent",
        name: "Copywriters",
        description: "Copywriters",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 645,
        group: "agent",
        name: "Technical Writers",
        description: "Technical Writers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 646,
        group: "agent",
        name: "Scriptwriters / Screenwriters",
        description: "Scriptwriters / Screenwriters",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 647,
        group: "agent",
        name: "Translators / Localization Experts",
        description: "Translators / Localization Experts",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 648,
        group: "agent",
        name: "Editors / Proof-readers",
        description: "Editors / Proof-readers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 649,
        group: "agent",
        name: "Graphic Designers",
        description: "Graphic Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 650,
        group: "agent",
        name: "Brand Identity Consultants",
        description: "Brand Identity Consultants",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 651,
        group: "agent",
        name: "Illustrators",
        description: "Illustrators",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 652,
        group: "agent",
        name: "Packaging Designers",
        description: "Packaging Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 653,
        group: "agent",
        name: "Motion Designers",
        description: "Motion Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 654,
        group: "agent",
        name: "Video Editors",
        description: "Video Editors",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 655,
        group: "agent",
        name: "Cinematographers / Videographers",
        description: "Cinematographers / Videographers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 656,
        group: "agent",
        name: "Animators (2D/3D)",
        description: "Animators (2D/3D)",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 657,
        group: "agent",
        name: "Voice-over Artists",
        description: "Voice-over Artists",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 658,
        group: "agent",
        name: "Sound Designers / Audio Editors",
        description: "Sound Designers / Audio Editors",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 659,
        group: "agent",
        name: "UX Designers",
        description: "UX Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 660,
        group: "agent",
        name: "UI Designers",
        description: "UI Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 661,
        group: "agent",
        name: "Product Designers",
        description: "Product Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 662,
        group: "agent",
        name: "Prototyping Specialists",
        description: "Prototyping Specialists",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 663,
        group: "agent",
        name: "Design System Consultants",
        description: "Design System Consultants",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 664,
        group: "agent",
        name: "Web Designers",
        description: "Web Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 665,
        group: "agent",
        name: "Landing Page Specialists",
        description: "Landing Page Specialists",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 666,
        group: "agent",
        name: "Email Template Designers",
        description: "Email Template Designers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 667,
        group: "agent",
        name: "Canva / Template Creators",
        description: "Canva / Template Creators",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 668,
        group: "agent",
        name: "Creative Project Managers",
        description: "Creative Project Managers",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 669,
        group: "agent",
        name: "Client Acquisition Coaches",
        description: "Client Acquisition Coaches",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 670,
        group: "agent",
        name: "Freelance Platform Agents",
        description: "Freelance Platform Agents",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // AI Agents
      {
        id: 671,
        group: "agent",
        name: "AI Developer / Engineer",
        description: "AI Developer / Engineer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 672,
        group: "agent",
        name: "Machine Learning Specialist",
        description: "Machine Learning Specialist",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 673,
        group: "agent",
        name: "Natural Language Processing (NLP) Expert",
        description: "Natural Language Processing (NLP) Expert",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 674,
        group: "agent",
        name: "Computer Vision Specialist",
        description: "Computer Vision Specialist",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 675,
        group: "agent",
        name: "AI Algorithm Developer",
        description: "AI Algorithm Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 676,
        group: "agent",
        name: "AI Software Engineer / Integration Specialist",
        description: "AI Software Engineer / Integration Specialist",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 677,
        group: "agent",
        name: "AI Consultant / Business Strategist",
        description: "AI Consultant / Business Strategist",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 678,
        group: "agent",
        name: "AI Transformation Advisor",
        description: "AI Transformation Advisor",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 679,
        group: "agent",
        name: "Ethical AI & Compliance Consultant",
        description: "Ethical AI & Compliance Consultant",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 680,
        group: "agent",
        name: "AI Implementation Specialist (SMEs / Enterprises)",
        description: "AI Implementation Specialist (SMEs / Enterprises)",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 681,
        group: "agent",
        name: "AI Chatbot Developer",
        description: "AI Chatbot Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 682,
        group: "agent",
        name: "Virtual Assistant Solution Provider",
        description: "Virtual Assistant Solution Provider",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 683,
        group: "agent",
        name: "AI-powered CRM Consultant",
        description: "AI-powered CRM Consultant",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 684,
        group: "agent",
        name: "Voice AI Agent Developer",
        description: "Voice AI Agent Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 685,
        group: "agent",
        name: "AI Agent for Customer Service",
        description: "AI Agent for Customer Service",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 686,
        group: "agent",
        name: "AI Agents for Real Estate / Property Listing",
        description: "AI Agents for Real Estate / Property Listing",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 687,
        group: "agent",
        name: "AI Automation for E-commerce or Retail",
        description: "AI Automation for E-commerce or Retail",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 688,
        group: "agent",
        name: "Healthcare AI Agent Provider",
        description: "Healthcare AI Agent Provider",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 689,
        group: "agent",
        name: "Finance & Trading AI Specialist",
        description: "Finance & Trading AI Specialist",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 690,
        group: "agent",
        name: "LegalTech AI Consultant",
        description: "LegalTech AI Consultant",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 691,
        group: "agent",
        name: "EdTech AI Agent Developer",
        description: "EdTech AI Agent Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 692,
        group: "agent",
        name: "HR & Recruitment AI Platform Provider",
        description: "HR & Recruitment AI Platform Provider",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 693,
        group: "agent",
        name: "AI in Marketing & Advertising Automation",
        description: "AI in Marketing & Advertising Automation",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 694,
        group: "agent",
        name: "AI Agents for Travel & Hospitality",
        description: "AI Agents for Travel & Hospitality",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 695,
        group: "agent",
        name: "Data Scientist / AI Model Trainer",
        description: "Data Scientist / AI Model Trainer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 696,
        group: "agent",
        name: "Data Labelling & Annotation Services",
        description: "Data Labelling & Annotation Services",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 697,
        group: "agent",
        name: "Predictive Analytics Consultant",
        description: "Predictive Analytics Consultant",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 698,
        group: "agent",
        name: "AI & Big Data Analyst",
        description: "AI & Big Data Analyst",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 699,
        group: "agent",
        name: "AI Cloud Solutions Provider",
        description: "AI Cloud Solutions Provider",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 700,
        group: "agent",
        name: "Edge AI Developer",
        description: "Edge AI Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 701,
        group: "agent",
        name: "AI Testing & Deployment Expert",
        description: "AI Testing & Deployment Expert",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 702,
        group: "agent",
        name: "AI Model Optimization / Scaling Specialist",
        description: "AI Model Optimization / Scaling Specialist",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 703,
        group: "agent",
        name: "AI Trainer / Instructor",
        description: "AI Trainer / Instructor",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 704,
        group: "agent",
        name: "AI Curriculum Developer",
        description: "AI Curriculum Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 705,
        group: "agent",
        name: "AI Bootcamp Operator",
        description: "AI Bootcamp Operator",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 706,
        group: "agent",
        name: "Corporate AI Trainer",
        description: "Corporate AI Trainer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 707,
        group: "agent",
        name: "AI Prototype Developer",
        description: "AI Prototype Developer",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 708,
        group: "agent",
        name: "Custom AI Agent Development Agency",
        description: "Custom AI Agent Development Agency",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 709,
        group: "agent",
        name: "Freelance AI Agent Consultant",
        description: "Freelance AI Agent Consultant",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 710,
        group: "agent",
        name: "White-Label AI Agent Platform Provider",
        description: "White-Label AI Agent Platform Provider",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Personal Services
      {
        id: 711,
        group: "agent",
        name: "Personal Stylists / Fashion Consultants",
        description: "Personal Stylists / Fashion Consultants",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 712,
        group: "agent",
        name: "Personal Shoppers",
        description: "Personal Shoppers",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 713,
        group: "agent",
        name: "Makeup Artists / Hair Stylists",
        description: "Makeup Artists / Hair Stylists",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 714,
        group: "agent",
        name: "Image Consultants",
        description: "Image Consultants",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 715,
        group: "agent",
        name: "Wardrobe Managers",
        description: "Wardrobe Managers",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 716,
        group: "agent",
        name: "Personal Trainers / Fitness Coaches",
        description: "Personal Trainers / Fitness Coaches",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 717,
        group: "agent",
        name: "Yoga / Pilates Instructors",
        description: "Yoga / Pilates Instructors",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 718,
        group: "agent",
        name: "Nutritionists / Dietitians",
        description: "Nutritionists / Dietitians",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 719,
        group: "agent",
        name: "Wellness Coaches",
        description: "Wellness Coaches",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 720,
        group: "agent",
        name: "Massage Therapists",
        description: "Massage Therapists",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 721,
        group: "agent",
        name: "Life Coaches",
        description: "Life Coaches",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 722,
        group: "agent",
        name: "Career Coaches",
        description: "Career Coaches",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 723,
        group: "agent",
        name: "Therapists / Counsellors (Private Practice)",
        description: "Therapists / Counsellors (Private Practice)",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 724,
        group: "agent",
        name: "Relationship Coaches",
        description: "Relationship Coaches",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 725,
        group: "agent",
        name: "Parenting Consultants",
        description: "Parenting Consultants",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 726,
        group: "agent",
        name: "Personal Concierges",
        description: "Personal Concierges",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 727,
        group: "agent",
        name: "Travel Concierges",
        description: "Travel Concierges",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 728,
        group: "agent",
        name: "Event Assistants / Party Planners",
        description: "Event Assistants / Party Planners",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 729,
        group: "agent",
        name: "Elder Care Agents",
        description: "Elder Care Agents",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 730,
        group: "agent",
        name: "Nannies / Babysitters",
        description: "Nannies / Babysitters",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 731,
        group: "agent",
        name: "Housekeepers / Domestic Help Agencies",
        description: "Housekeepers / Domestic Help Agencies",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 732,
        group: "agent",
        name: "Pet Sitters / Dog Walkers",
        description: "Pet Sitters / Dog Walkers",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 733,
        group: "agent",
        name: "Personal Chefs",
        description: "Personal Chefs",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 734,
        group: "agent",
        name: "Bodyguards / Private Security Agents",
        description: "Bodyguards / Private Security Agents",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 735,
        group: "agent",
        name: "Driver Services / Chauffeurs",
        description: "Driver Services / Chauffeurs",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 736,
        group: "agent",
        name: "Tattoo / Piercing Consultants",
        description: "Tattoo / Piercing Consultants",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 737,
        group: "agent",
        name: "Personal Tech Assistants",
        description: "Personal Tech Assistants",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 738,
        group: "agent",
        name: "Social Media Managers",
        description: "Social Media Managers",
        parentId: 23,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Emerging & Niche Categories
      {
        id: 739,
        group: "agent",
        name: "Green Energy Consultant",
        description: "Green Energy Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 740,
        group: "agent",
        name: "Environmental Compliance Agent",
        description: "Environmental Compliance Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 741,
        group: "agent",
        name: "ESG (Environmental, Social, Governance) Advisor",
        description: "ESG (Environmental, Social, Governance) Advisor",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 742,
        group: "agent",
        name: "Carbon Offset / Emission Trading Consultant",
        description: "Carbon Offset / Emission Trading Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 743,
        group: "agent",
        name: "Wedding Planner",
        description: "Wedding Planner",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 744,
        group: "agent",
        name: "Corporate Event Organizer",
        description: "Corporate Event Organizer",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 745,
        group: "agent",
        name: "Entertainment Booking Agent",
        description: "Entertainment Booking Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 746,
        group: "agent",
        name: "Venue Coordinator",
        description: "Venue Coordinator",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 747,
        group: "agent",
        name: "Event Logistics Agent",
        description: "Event Logistics Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 748,
        group: "agent",
        name: "Blockchain Consultant",
        description: "Blockchain Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 749,
        group: "agent",
        name: "Metaverse Consultant",
        description: "Metaverse Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 750,
        group: "agent",
        name: "IoT Solution Provider",
        description: "IoT Solution Provider",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 751,
        group: "agent",
        name: "Cybersecurity Agent",
        description: "Cybersecurity Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 752,
        group: "agent",
        name: "Software Licensing Agent",
        description: "Software Licensing Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 753,
        group: "agent",
        name: "Graphic Design & Branding Consultant",
        description: "Graphic Design & Branding Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 754,
        group: "agent",
        name: "UI/UX Designer",
        description: "UI/UX Designer",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 755,
        group: "agent",
        name: "Interior Designer",
        description: "Interior Designer",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 756,
        group: "agent",
        name: "Creative Director / Art Agent",
        description: "Creative Director / Art Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 757,
        group: "agent",
        name: "Fashion Stylist / Consultant",
        description: "Fashion Stylist / Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 758,
        group: "agent",
        name: "Influencer / Talent Agent",
        description: "Influencer / Talent Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 759,
        group: "agent",
        name: "Video Production Agency",
        description: "Video Production Agency",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 760,
        group: "agent",
        name: "Content Creator Manager",
        description: "Content Creator Manager",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 761,
        group: "agent",
        name: "Podcasting Consultant",
        description: "Podcasting Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 762,
        group: "agent",
        name: "Digital PR Agent",
        description: "Digital PR Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 763,
        group: "agent",
        name: "Life Coach",
        description: "Life Coach",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 764,
        group: "agent",
        name: "Career Coach",
        description: "Career Coach",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 765,
        group: "agent",
        name: "Personal Stylist",
        description: "Personal Stylist",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 766,
        group: "agent",
        name: "Fitness & Wellness Consultant",
        description: "Fitness & Wellness Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 767,
        group: "agent",
        name: "Personal Chef / Nutritionist",
        description: "Personal Chef / Nutritionist",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 768,
        group: "agent",
        name: "Franchise Consultant",
        description: "Franchise Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 769,
        group: "agent",
        name: "PRO Services Agent",
        description: "PRO Services Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 770,
        group: "agent",
        name: "Business Setup Agent (Mainland/Freezone)",
        description: "Business Setup Agent (Mainland/Freezone)",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 771,
        group: "agent",
        name: "Trademark & IP Consultant",
        description: "Trademark & IP Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 772,
        group: "agent",
        name: "Construction Project Manager",
        description: "Construction Project Manager",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 773,
        group: "agent",
        name: "Architect / Civil Engineer Agent",
        description: "Architect / Civil Engineer Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 774,
        group: "agent",
        name: "Quantity Surveyor",
        description: "Quantity Surveyor",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 775,
        group: "agent",
        name: "Permits & Approval Agent",
        description: "Permits & Approval Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 776,
        group: "agent",
        name: "Smart Building Consultant",
        description: "Smart Building Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 777,
        group: "agent",
        name: "Senior Care Consultant",
        description: "Senior Care Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 778,
        group: "agent",
        name: "Disability Services Coordinator",
        description: "Disability Services Coordinator",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 779,
        group: "agent",
        name: "Mobility Equipment Agent",
        description: "Mobility Equipment Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 780,
        group: "agent",
        name: "Home Nursing Agent",
        description: "Home Nursing Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 781,
        group: "agent",
        name: "AgriTech Consultant",
        description: "AgriTech Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 782,
        group: "agent",
        name: "Food Supply Chain Advisor",
        description: "Food Supply Chain Advisor",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 783,
        group: "agent",
        name: "Farm Equipment Agent",
        description: "Farm Equipment Agent",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 784,
        group: "agent",
        name: "Organic Certification Consultant",
        description: "Organic Certification Consultant",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // ********************************* Sub-Industries for Agency *********************************

      // Real State
      {
        id: 785,
        group: "agency",
        name: "Real Estate Brokerage",
        description: "Real Estate Brokerage",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 786,
        group: "agency",
        name: "Real Estate Developer",
        description: "Real Estate Developer",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 787,
        group: "agency",
        name: "Real Estate Agencies",
        description: "Real Estate Agencies",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 788,
        group: "agency",
        name: "Real Estate Developers & Builders",
        description: "Real Estate Developers & Builders",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 789,
        group: "agency",
        name: "Property Management Companies",
        description: "Property Management Companies",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 790,
        group: "agency",
        name: "Valuation & Appraisal Firms",
        description: "Valuation & Appraisal Firms",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 791,
        group: "agency",
        name: "Real Estate Investment Firms",
        description: "Real Estate Investment Firms",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 792,
        group: "agency",
        name: "Real Estate Consultancy Firms",
        description: "Real Estate Consultancy Firms",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 793,
        group: "agency",
        name: "Commercial Property Management",
        description: "Commercial Property Management",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 794,
        group: "agency",
        name: "Tenant Management Companies",
        description: "Tenant Management Companies",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 795,
        group: "agency",
        name: "Land Development Companies",
        description: "Land Development Companies",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 796,
        group: "agency",
        name: "Facilities Management Firms",
        description: "Facilities Management Firms",
        parentId: 1,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Accounting & Tax
      {
        id: 797,
        group: "agency",
        name: "Accounting Firms",
        description: "Accounting Firms",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 798,
        group: "agency",
        name: "Tax Advisory Firms",
        description: "Tax Advisory Firms",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 799,
        group: "agency",
        name: "Bookkeeping Service Providers",
        description: "Bookkeeping Service Providers",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 800,
        group: "agency",
        name: "Audit Firms",
        description: "Audit Firms",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 801,
        group: "agency",
        name: "Financial Compliance Agencies",
        description: "Financial Compliance Agencies",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 802,
        group: "agency",
        name: "VAT & Zakat Consultancy Firms",
        description: "VAT & Zakat Consultancy Firms",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 803,
        group: "agency",
        name: "Auditing Agencies",
        description: "Auditing Agencies",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 804,
        group: "agency",
        name: "Payroll Service Providers",
        description: "Payroll Service Providers",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 805,
        group: "agency",
        name: "Tax Consultancy Firm",
        description: "Tax Consultancy Firm",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 806,
        group: "agency",
        name: "VAT Registration & Filing Services",
        description: "VAT Registration & Filing Services",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 807,
        group: "agency",
        name: "Audit & Assurance Firm",
        description: "Audit & Assurance Firm",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 808,
        group: "agency",
        name: "Corporate Finance Advisory",
        description: "Corporate Finance Advisory",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 809,
        group: "agency",
        name: "Accounting Software Consultants",
        description: "Accounting Software Consultants",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 810,
        group: "agency",
        name: "Outsourced CFO Services",
        description: "Outsourced CFO Services",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 811,
        group: "agency",
        name: "Startup Accounting & Compliance Experts",
        description: "Startup Accounting & Compliance Experts",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 812,
        group: "agency",
        name: "Business Setup & Financial Structuring",
        description: "Business Setup & Financial Structuring",
        parentId: 2,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // IT & Tech
      {
        id: 813,
        group: "agency",
        name: "Software Development Companies",
        description: "Software Development Companies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 814,
        group: "agency",
        name: "IT Support & Maintenance Firms",
        description: "IT Support & Maintenance Firms",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 815,
        group: "agency",
        name: "Cloud Solutions Providers",
        description: "Cloud Solutions Providers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 816,
        group: "agency",
        name: "System Integration Firms",
        description: "System Integration Firms",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 817,
        group: "agency",
        name: "Managed IT Services Companies",
        description: "Managed IT Services Companies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 818,
        group: "agency",
        name: "Networking & Infrastructure Providers",
        description: "Networking & Infrastructure Providers",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 819,
        group: "agency",
        name: "ERP/CRM Implementation Firms",
        description: "ERP/CRM Implementation Firms",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 820,
        group: "agency",
        name: "Web & App Development Agencies",
        description: "Web & App Development Agencies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 821,
        group: "agency",
        name: "Cybersecurity Consultancies",
        description: "Cybersecurity Consultancies",
        parentId: 3,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Mortgage & Finance
      {
        id: 822,
        group: "agency",
        name: "Mortgage Brokerage Companies",
        description: "Mortgage Brokerage Companies",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 823,
        group: "agency",
        name: "Investment Advisory Firms",
        description: "Investment Advisory Firms",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 824,
        group: "agency",
        name: "Financial Planning & Wealth Management Companies",
        description: "Financial Planning & Wealth Management Companies",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 825,
        group: "agency",
        name: "Islamic Finance Institutions",
        description: "Islamic Finance Institutions",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 826,
        group: "agency",
        name: "Loan Agencies",
        description: "Loan Agencies",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 827,
        group: "agency",
        name: "Credit Rating & Risk Assessment Firms",
        description: "Credit Rating & Risk Assessment Firms",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 828,
        group: "agency",
        name: "Debt Recovery Agencies",
        description: "Debt Recovery Agencies",
        parentId: 4,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Visa & Immigration
      {
        id: 829,
        group: "agency",
        name: "Visa Application Agencies",
        description: "Visa Application Agencies",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 830,
        group: "agency",
        name: "Immigration Law Firms",
        description: "Immigration Law Firms",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 831,
        group: "agency",
        name: "Golden Visa Consultants",
        description: "Golden Visa Consultants",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 832,
        group: "agency",
        name: "Company Formation & Business Setup Firms",
        description: "Company Formation & Business Setup Firms",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 833,
        group: "agency",
        name: "PRO & Government Liaison Companies",
        description: "PRO & Government Liaison Companies",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 834,
        group: "agency",
        name: "Corporate Immigration Consultancies",
        description: "Corporate Immigration Consultancies",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 835,
        group: "agency",
        name: "Government Visa Application Services",
        description: "Government Visa Application Services",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 836,
        group: "agency",
        name: "Global Mobility & Relocation Firms",
        description: "Global Mobility & Relocation Firms",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 837,
        group: "agency",
        name: "Citizenship & Residency by Investment Programs",
        description: "Citizenship & Residency by Investment Programs",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 838,
        group: "agency",
        name: "Authorized Typing Center Agent/Manager",
        description: "Authorized Typing Center Agent/Manager",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 839,
        group: "agency",
        name: "Amer Center (Dubai) Agent/Manager",
        description: "Amer Center (Dubai) Agent/Manager",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 840,
        group: "agency",
        name: "Tasjeel/Tasheel Center Agent/Manager",
        description: "Tasjeel/Tasheel Center Agent/Manager",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 841,
        group: "agency",
        name: "Tawjeeh Center Agent/Manager",
        description: "Tawjeeh Center Agent/Manager",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 842,
        group: "agency",
        name: "Visa Application Processing Center Agent/Manager",
        description: "Visa Application Processing Center Agent/Manager",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 843,
        group: "agency",
        name: "Investor Visa Services",
        description: "Investor Visa Services",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 844,
        group: "agency",
        name: "Remote Work / Nomad Visa Consultant",
        description: "Remote Work / Nomad Visa Consultant",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 845,
        group: "agency",
        name: "Dependent Visa Specialist",
        description: "Dependent Visa Specialist",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 846,
        group: "agency",
        name: "Exit/Re-entry Visa Expert",
        description: "Exit/Re-entry Visa Expert",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 847,
        group: "agency",
        name: "Overstay Fines & Legal Clearance Agency",
        description: "Overstay Fines & Legal Clearance Agency",
        parentId: 5,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Insurance Providers
      {
        id: 848,
        group: "agency",
        name: "Insurance Provider",
        description: "Insurance Provider",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 849,
        group: "agency",
        name: "Insurance Brokers",
        description: "Insurance Brokers",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 850,
        group: "agency",
        name: "Insurance Company",
        description: "Insurance Company",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 851,
        group: "agency",
        name: "Health Insurance Company",
        description: "Health Insurance Company",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 852,
        group: "agency",
        name: "Property & Home Insurance Providers",
        description: "Property & Home Insurance Providers",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 853,
        group: "agency",
        name: "Auto Insurance Firms",
        description: "Auto Insurance Firms",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 854,
        group: "agency",
        name: "Life Insurance Companies",
        description: "Life Insurance Companies",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 855,
        group: "agency",
        name: "Travel Insurance Providers",
        description: "Travel Insurance Providers",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 856,
        group: "agency",
        name: "Business & Liability Insurance Firms",
        description: "Business & Liability Insurance Firms",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 857,
        group: "agency",
        name: "Reinsurance Companies",
        description: "Reinsurance Companies",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 858,
        group: "agency",
        name: "Insurance Underwriting Companies",
        description: "Insurance Underwriting Companies",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 859,
        group: "agency",
        name: "Risk Management & Insurance Consulting Firms",
        description: "Risk Management & Insurance Consulting Firms",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 860,
        group: "agency",
        name: "Takaful (Islamic Insurance) Providers",
        description: "Takaful (Islamic Insurance) Providers",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 861,
        group: "agency",
        name: "Insurance Claims Management Companies",
        description: "Insurance Claims Management Companies",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 862,
        group: "agency",
        name: "Insurance Technology (InsurTech) Firms",
        description: "Insurance Technology (InsurTech) Firms",
        parentId: 6,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Marketing & Advertising
      {
        id: 863,
        group: "agency",
        name: "Full-Service Marketing Agency",
        description: "Full-Service Marketing Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 864,
        group: "agency",
        name: "Marketing Agencies",
        description: "Marketing Agencies",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 865,
        group: "agency",
        name: "Digital Advertising Firms",
        description: "Digital Advertising Firms",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 866,
        group: "agency",
        name: "Digital Marketing Agency",
        description: "Digital Marketing Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 867,
        group: "agency",
        name: "SEO & SEM Consultancies",
        description: "SEO & SEM Consultancies",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 868,
        group: "agency",
        name: "Creative Agencies",
        description: "Creative Agencies",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 869,
        group: "agency",
        name: "Branding Firms",
        description: "Branding Firms",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 870,
        group: "agency",
        name: "Media Buying Companies",
        description: "Media Buying Companies",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 871,
        group: "agency",
        name: "PR (Public Relations) Agencies",
        description: "PR (Public Relations) Agencies",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 872,
        group: "agency",
        name: "Social Media Management Companies",
        description: "Social Media Management Companies",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 873,
        group: "agency",
        name: "Experiential Marketing Firms",
        description: "Experiential Marketing Firms",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 874,
        group: "agency",
        name: "Performance Marketing Agency",
        description: "Performance Marketing Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 875,
        group: "agency",
        name: "Creative Advertising Agency",
        description: "Creative Advertising Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 876,
        group: "agency",
        name: "Media Buying & Planning Agency",
        description: "Media Buying & Planning Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 877,
        group: "agency",
        name: "Branding & Identity Studio",
        description: "Branding & Identity Studio",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 878,
        group: "agency",
        name: "PR & Communications Agency",
        description: "PR & Communications Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 879,
        group: "agency",
        name: "Content Marketing Agency",
        description: "Content Marketing Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 880,
        group: "agency",
        name: "Influencer & Talent Management Agency",
        description: "Influencer & Talent Management Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 881,
        group: "agency",
        name: "Billboard Advertising Provider",
        description: "Billboard Advertising Provider",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 882,
        group: "agency",
        name: "Radio & TV Advertising Consultant",
        description: "Radio & TV Advertising Consultant",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 883,
        group: "agency",
        name: "Print & Magazine Advertising Firm",
        description: "Print & Magazine Advertising Firm",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 884,
        group: "agency",
        name: "Event Marketing Agency",
        description: "Event Marketing Agency",
        parentId: 7,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Legal & Compliance
      {
        id: 885,
        group: "agency",
        name: "Law Firms",
        description: "Law Firms",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 886,
        group: "agency",
        name: "Full-Service Law Firm",
        description: "Full-Service Law Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 887,
        group: "agency",
        name: "Legal Advisory Companies",
        description: "Legal Advisory Companies",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 888,
        group: "agency",
        name: "Corporate Legal Departments",
        description: "Corporate Legal Departments",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 889,
        group: "agency",
        name: "Compliance Consultancy Firms",
        description: "Compliance Consultancy Firms",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 890,
        group: "agency",
        name: "Notary Offices",
        description: "Notary Offices",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 891,
        group: "agency",
        name: "Risk & Regulatory Services Companies",
        description: "Risk & Regulatory Services Companies",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 892,
        group: "agency",
        name: "Arbitration & Mediation Centers",
        description: "Arbitration & Mediation Centers",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 893,
        group: "agency",
        name: "Intellectual Property Firms",
        description: "Intellectual Property Firms",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 894,
        group: "agency",
        name: "Corporate & Commercial Law Firm",
        description: "Corporate & Commercial Law Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 895,
        group: "agency",
        name: "Boutique Law Firm (Specialized Legal Services)",
        description: "Boutique Law Firm (Specialized Legal Services)",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 896,
        group: "agency",
        name: "Real Estate & Property Law Firm",
        description: "Real Estate & Property Law Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 897,
        group: "agency",
        name: "Employment Law Firm",
        description: "Employment Law Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 898,
        group: "agency",
        name: "Family Law Firm",
        description: "Family Law Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 899,
        group: "agency",
        name: "Litigation & Arbitration Firm",
        description: "Litigation & Arbitration Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 900,
        group: "agency",
        name: "IP & Trademark Law Firm",
        description: "IP & Trademark Law Firm",
        parentId: 8,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Maintenance & Repair
      {
        id: 901,
        group: "agency",
        name: "Facility Management Companies",
        description: "Facility Management Companies",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 902,
        group: "agency",
        name: "Building Maintenance Contractors",
        description: "Building Maintenance Contractors",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 903,
        group: "agency",
        name: "HVAC & Plumbing Companies",
        description: "HVAC & Plumbing Companies",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 904,
        group: "agency",
        name: "Electrical Repair Agencies",
        description: "Electrical Repair Agencies",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 905,
        group: "agency",
        name: "Pest Control Companies",
        description: "Pest Control Companies",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 906,
        group: "agency",
        name: "General Maintenance Firms",
        description: "General Maintenance Firms",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 907,
        group: "agency",
        name: "Elevator & Escalator Services",
        description: "Elevator & Escalator Services",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 908,
        group: "agency",
        name: "Fire Safety & Alarm System Companies",
        description: "Fire Safety & Alarm System Companies",
        parentId: 9,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Logistics & Transportation
      {
        id: 909,
        group: "agency",
        name: "Freight Forwarders",
        description: "Freight Forwarders",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 910,
        group: "agency",
        name: "Supply Chain Management Firms",
        description: "Supply Chain Management Firms",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 911,
        group: "agency",
        name: "Cargo & Shipping Companies",
        description: "Cargo & Shipping Companies",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 912,
        group: "agency",
        name: "Courier & Delivery Agencies",
        description: "Courier & Delivery Agencies",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 913,
        group: "agency",
        name: "Moving & Packing Companies",
        description: "Moving & Packing Companies",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 914,
        group: "agency",
        name: "Warehouse & Storage Providers",
        description: "Warehouse & Storage Providers",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 915,
        group: "agency",
        name: "Fleet Management Companies",
        description: "Fleet Management Companies",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 916,
        group: "agency",
        name: "Car Leasing & Rental Firms",
        description: "Car Leasing & Rental Firms",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 917,
        group: "agency",
        name: "Cross-border Logistics Providers",
        description: "Cross-border Logistics Providers",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 918,
        group: "agency",
        name: "Freight Forwarder",
        description: "Freight Forwarder",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 919,
        group: "agency",
        name: "International Shipping",
        description: "International Shipping",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 920,
        group: "agency",
        name: "Freight Brokerage Company",
        description: "Freight Brokerage Company",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 921,
        group: "agency",
        name: "Air Cargo Agency",
        description: "Air Cargo Agency",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 922,
        group: "agency",
        name: "Sea Cargo Agency",
        description: "Sea Cargo Agency",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 923,
        group: "agency",
        name: "Customs Clearance Agency",
        description: "Customs Clearance Agency",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 924,
        group: "agency",
        name: "3PL (Third-Party Logistics) Provider",
        description: "3PL (Third-Party Logistics) Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 925,
        group: "agency",
        name: "Cold Chain Logistics Specialist",
        description: "Cold Chain Logistics Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 926,
        group: "agency",
        name: "Last Mile Delivery Provider",
        description: "Last Mile Delivery Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 927,
        group: "agency",
        name: "Warehousing & Storage Solutions",
        description: "Warehousing & Storage Solutions",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 928,
        group: "agency",
        name: "Domestic Courier Service",
        description: "Domestic Courier Service",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 929,
        group: "agency",
        name: "E-Commerce Delivery Company",
        description: "E-Commerce Delivery Company",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 930,
        group: "agency",
        name: "Same-Day Delivery Service",
        description: "Same-Day Delivery Service",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 931,
        group: "agency",
        name: "Express Delivery Agent",
        description: "Express Delivery Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 932,
        group: "agency",
        name: "Parcel Pickup & Drop Specialist",
        description: "Parcel Pickup & Drop Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 933,
        group: "agency",
        name: "Passenger Transport Company",
        description: "Passenger Transport Company",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 934,
        group: "agency",
        name: "School Transportation Services",
        description: "School Transportation Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 935,
        group: "agency",
        name: "Employee Transportation Provider",
        description: "Employee Transportation Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 936,
        group: "agency",
        name: "Tour Bus Operator",
        description: "Tour Bus Operator",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 937,
        group: "agency",
        name: "Luxury Vehicle Transport Services",
        description: "Luxury Vehicle Transport Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 938,
        group: "agency",
        name: "Car Rental Company",
        description: "Car Rental Company",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 939,
        group: "agency",
        name: "Commercial Vehicle Leasing",
        description: "Commercial Vehicle Leasing",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 940,
        group: "agency",
        name: "Truck & Trailer Rental Services",
        description: "Truck & Trailer Rental Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 941,
        group: "agency",
        name: "Chauffeur & Driver Service Agency",
        description: "Chauffeur & Driver Service Agency",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 942,
        group: "agency",
        name: "Household Movers",
        description: "Household Movers",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 943,
        group: "agency",
        name: "Office Relocation Specialists",
        description: "Office Relocation Specialists",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 944,
        group: "agency",
        name: "International Moving Company",
        description: "International Moving Company",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 945,
        group: "agency",
        name: "Packing & Unpacking Services",
        description: "Packing & Unpacking Services",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 946,
        group: "agency",
        name: "Heavy Equipment Transportation",
        description: "Heavy Equipment Transportation",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 947,
        group: "agency",
        name: "Vehicle Shipping Specialist",
        description: "Vehicle Shipping Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 948,
        group: "agency",
        name: "Medical & Pharmaceutical Logistics",
        description: "Medical & Pharmaceutical Logistics",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 949,
        group: "agency",
        name: "Event Logistics Provider",
        description: "Event Logistics Provider",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 950,
        group: "agency",
        name: "Customs Documentation Specialist",
        description: "Customs Documentation Specialist",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 951,
        group: "agency",
        name: "Import/Export Compliance Agent",
        description: "Import/Export Compliance Agent",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 952,
        group: "agency",
        name: "Free Zone Logistics Facilitator",
        description: "Free Zone Logistics Facilitator",
        parentId: 10,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Travel & Hospitality
      {
        id: 953,
        group: "agency",
        name: "Travel Agencies",
        description: "Travel Agencies",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 954,
        group: "agency",
        name: "Tour Operators",
        description: "Tour Operators",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 955,
        group: "agency",
        name: "Destination Management Companies (DMCs)",
        description: "Destination Management Companies (DMCs)",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 956,
        group: "agency",
        name: "Corporate Travel Services Firms",
        description: "Corporate Travel Services Firms",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 957,
        group: "agency",
        name: "Hotel Booking Agencies",
        description: "Hotel Booking Agencies",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 958,
        group: "agency",
        name: "Cruise & Umrah Package Providers",
        description: "Cruise & Umrah Package Providers",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 959,
        group: "agency",
        name: "Pilgrimage Management Firms",
        description: "Pilgrimage Management Firms",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 960,
        group: "agency",
        name: "Travel Consultancy Firm",
        description: "Travel Consultancy Firm",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 961,
        group: "agency",
        name: "Hajj & Umrah Travel Organizer",
        description: "Hajj & Umrah Travel Organizer",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 962,
        group: "agency",
        name: "Luxury Travel Advisor",
        description: "Luxury Travel Advisor",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 963,
        group: "agency",
        name: "Group Tour Operator",
        description: "Group Tour Operator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 964,
        group: "agency",
        name: "Cruise Company",
        description: "Cruise Company",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 965,
        group: "agency",
        name: "Adventure & Safari Tour Company",
        description: "Adventure & Safari Tour Company",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 966,
        group: "agency",
        name: "Destination Management Company (DMC)",
        description: "Destination Management Company (DMC)",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 967,
        group: "agency",
        name: "Travel Insurance Provider",
        description: "Travel Insurance Provider",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 968,
        group: "agency",
        name: "Serviced Apartments Provider",
        description: "Serviced Apartments Provider",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 969,
        group: "agency",
        name: "Airport Pickup & Drop Service",
        description: "Airport Pickup & Drop Service",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 970,
        group: "agency",
        name: "Hotel Shuttle Service",
        description: "Hotel Shuttle Service",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 971,
        group: "agency",
        name: "Chauffeur & Limousine Service",
        description: "Chauffeur & Limousine Service",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 972,
        group: "agency",
        name: "Tour Bus Operator",
        description: "Tour Bus Operator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 973,
        group: "agency",
        name: "Yacht & Boat Rental Agent",
        description: "Yacht & Boat Rental Agent",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 974,
        group: "agency",
        name: "MICE Organizer",
        description: "MICE Organizer",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 975,
        group: "agency",
        name: "Destination Wedding Planner",
        description: "Destination Wedding Planner",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 976,
        group: "agency",
        name: "Medical Tourism Facilitator",
        description: "Medical Tourism Facilitator",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 977,
        group: "agency",
        name: "Cultural & Heritage Tour Guide",
        description: "Cultural & Heritage Tour Guide",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 978,
        group: "agency",
        name: "Eco-Tourism Specialist",
        description: "Eco-Tourism Specialist",
        parentId: 11,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Post-Purchase / Rental
      {
        id: 979,
        group: "agency",
        name: "Fit-Out & Interior Design Firms",
        description: "Fit-Out & Interior Design Firms",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 980,
        group: "agency",
        name: "Home Staging Companies",
        description: "Home Staging Companies",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 981,
        group: "agency",
        name: "Property Inspection Services",
        description: "Property Inspection Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 982,
        group: "agency",
        name: "Move-In/Move-Out Agencies",
        description: "Move-In/Move-Out Agencies",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 983,
        group: "agency",
        name: "Utility Setup & Management Services",
        description: "Utility Setup & Management Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 984,
        group: "agency",
        name: "Furniture Rental Companies",
        description: "Furniture Rental Companies",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 985,
        group: "agency",
        name: "Smart Home Installation Providers",
        description: "Smart Home Installation Providers",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 986,
        group: "agency",
        name: "House Moving Services",
        description: "House Moving Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 987,
        group: "agency",
        name: "Packing & Unpacking Services",
        description: "Packing & Unpacking Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 988,
        group: "agency",
        name: "Furniture Assembly Expert",
        description: "Furniture Assembly Expert",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 989,
        group: "agency",
        name: "Temporary Storage Solutions",
        description: "Temporary Storage Solutions",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 990,
        group: "agency",
        name: "Internet & Telecom Provider",
        description: "Internet & Telecom Provider",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 991,
        group: "agency",
        name: "Utility Connection Support (Electricity, Water, Gas)",
        description: "Utility Connection Support (Electricity, Water, Gas)",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 992,
        group: "agency",
        name: "Satellite TV Installer",
        description: "Satellite TV Installer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 993,
        group: "agency",
        name: "Smart Home Setup Specialist",
        description: "Smart Home Setup Specialist",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 994,
        group: "agency",
        name: "Home Security System Installer",
        description: "Home Security System Installer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 995,
        group: "agency",
        name: "Deep Cleaning Services",
        description: "Deep Cleaning Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 996,
        group: "agency",
        name: "Post-Construction Cleaning",
        description: "Post-Construction Cleaning",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 997,
        group: "agency",
        name: "Pest Control Services",
        description: "Pest Control Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 998,
        group: "agency",
        name: "Sanitization & Disinfection Services",
        description: "Sanitization & Disinfection Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 999,
        group: "agency",
        name: "Water Tank Cleaning Services",
        description: "Water Tank Cleaning Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1000,
        group: "agency",
        name: "Interior Designer",
        description: "Interior Designer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1001,
        group: "agency",
        name: "Curtain & Blinds Installer",
        description: "Curtain & Blinds Installer",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1002,
        group: "agency",
        name: "Landscaping & Gardening Services",
        description: "Landscaping & Gardening Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1003,
        group: "agency",
        name: "Swimming Pool Maintenance",
        description: "Swimming Pool Maintenance",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1004,
        group: "agency",
        name: "Painting & Wall Treatment Services",
        description: "Painting & Wall Treatment Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1005,
        group: "agency",
        name: "AC Servicing & Repair",
        description: "AC Servicing & Repair",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1006,
        group: "agency",
        name: "Plumbing Services",
        description: "Plumbing Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1007,
        group: "agency",
        name: "Electrical Services",
        description: "Electrical Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1008,
        group: "agency",
        name: "Handyman Services",
        description: "Handyman Services",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1009,
        group: "agency",
        name: "Appliance Installation & Repair",
        description: "Appliance Installation & Repair",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1010,
        group: "agency",
        name: "Waste Management & Recycling",
        description: "Waste Management & Recycling",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1011,
        group: "agency",
        name: "Storage Shed/Container Provider",
        description: "Storage Shed/Container Provider",
        parentId: 12,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Human Resources & Recruitment
      {
        id: 1012,
        group: "agency",
        name: "Recruitment Agencies",
        description: "Recruitment Agencies",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1013,
        group: "agency",
        name: "Staffing & Talent Acquisition Firms",
        description: "Staffing & Talent Acquisition Firms",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1014,
        group: "agency",
        name: "Executive Search Firms",
        description: "Executive Search Firms",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1015,
        group: "agency",
        name: "Payroll Management Companies",
        description: "Payroll Management Companies",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1016,
        group: "agency",
        name: "HR Technology Providers",
        description: "HR Technology Providers",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1017,
        group: "agency",
        name: "Employment Verification Agencies",
        description: "Employment Verification Agencies",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1018,
        group: "agency",
        name: "Workforce Outsourcing Companies",
        description: "Workforce Outsourcing Companies",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1019,
        group: "agency",
        name: "General Recruitment Agency",
        description: "General Recruitment Agency",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1020,
        group: "agency",
        name: "Executive Search Consultant",
        description: "Executive Search Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1021,
        group: "agency",
        name: "Temporary Staffing Provider",
        description: "Temporary Staffing Provider",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1022,
        group: "agency",
        name: "Blue-Collar Recruitment Specialist",
        description: "Blue-Collar Recruitment Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1023,
        group: "agency",
        name: "Freelance & Gig Economy Recruiter",
        description: "Freelance & Gig Economy Recruiter",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1024,
        group: "agency",
        name: "Overseas Recruitment Consultant",
        description: "Overseas Recruitment Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1025,
        group: "agency",
        name: "Government Sector Recruitment Advisor",
        description: "Government Sector Recruitment Advisor",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1026,
        group: "agency",
        name: "Emiratization Recruitment Specialist",
        description: "Emiratization Recruitment Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1027,
        group: "agency",
        name: "HR Policy & Strategy Consultant",
        description: "HR Policy & Strategy Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1028,
        group: "agency",
        name: "Labour Law Compliance Consultant",
        description: "Labour Law Compliance Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1029,
        group: "agency",
        name: "HR Auditing Services",
        description: "HR Auditing Services",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1030,
        group: "agency",
        name: "Workforce Planning Consultant",
        description: "Workforce Planning Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1031,
        group: "agency",
        name: "Performance Management Consultant",
        description: "Performance Management Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1032,
        group: "agency",
        name: "Compensation & Benefits Advisor",
        description: "Compensation & Benefits Advisor",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1033,
        group: "agency",
        name: "Corporate Training Provider",
        description: "Corporate Training Provider",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1034,
        group: "agency",
        name: "Soft Skills Trainer",
        description: "Soft Skills Trainer",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1035,
        group: "agency",
        name: "Technical Skills Trainer",
        description: "Technical Skills Trainer",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1036,
        group: "agency",
        name: "Leadership & Executive Coach",
        description: "Leadership & Executive Coach",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1037,
        group: "agency",
        name: "Team Building Specialist",
        description: "Team Building Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1038,
        group: "agency",
        name: "Diversity & Inclusion Consultant",
        description: "Diversity & Inclusion Consultant",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1039,
        group: "agency",
        name: "HR Outsourcing Firm (HRO)",
        description: "HR Outsourcing Firm (HRO)",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1040,
        group: "agency",
        name: "Payroll Management Services",
        description: "Payroll Management Services",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1041,
        group: "agency",
        name: "Employee Onboarding & Exit Management",
        description: "Employee Onboarding & Exit Management",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1042,
        group: "agency",
        name: "Time & Attendance Management Provider",
        description: "Time & Attendance Management Provider",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1043,
        group: "agency",
        name: "HR Software & Systems Integrator",
        description: "HR Software & Systems Integrator",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1044,
        group: "agency",
        name: "Healthcare Recruitment Specialist",
        description: "Healthcare Recruitment Specialist",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1045,
        group: "agency",
        name: "IT & Tech Talent Recruiter",
        description: "IT & Tech Talent Recruiter",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1046,
        group: "agency",
        name: "Hospitality & Tourism Staffing",
        description: "Hospitality & Tourism Staffing",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1047,
        group: "agency",
        name: "Construction & Engineering Recruitment",
        description: "Construction & Engineering Recruitment",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1048,
        group: "agency",
        name: "Education & Academic Recruitment",
        description: "Education & Academic Recruitment",
        parentId: 13,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Retail & E-Commerce
      {
        id: 1049,
        group: "agency",
        name: "E-Commerce Platform Management Firms",
        description: "E-Commerce Platform Management Firms",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1050,
        group: "agency",
        name: "Fulfillment & Delivery Service Companies",
        description: "Fulfillment & Delivery Service Companies",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1051,
        group: "agency",
        name: "Online Store Setup Consultancies",
        description: "Online Store Setup Consultancies",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1052,
        group: "agency",
        name: "Inventory Management Providers",
        description: "Inventory Management Providers",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1053,
        group: "agency",
        name: "Customer Experience Consultancies",
        description: "Customer Experience Consultancies",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1054,
        group: "agency",
        name: "Marketplace Optimization Agencies",
        description: "Marketplace Optimization Agencies",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1055,
        group: "agency",
        name: "Retail Business Consultant",
        description: "Retail Business Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1056,
        group: "agency",
        name: "Store Design & Merchandising Expert",
        description: "Store Design & Merchandising Expert",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1057,
        group: "agency",
        name: "POS (Point-of-Sale) Systems Provider",
        description: "POS (Point-of-Sale) Systems Provider",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1058,
        group: "agency",
        name: "Inventory Management Consultant",
        description: "Inventory Management Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1059,
        group: "agency",
        name: "Franchise Consultant",
        description: "Franchise Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1060,
        group: "agency",
        name: "E-commerce Platform Developer (Shopify, Magento, WooCommerce, etc.)",
        description:
          "E-commerce Platform Developer (Shopify, Magento, WooCommerce, etc.)",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1061,
        group: "agency",
        name: "Website & Mobile App Developer",
        description: "Website & Mobile App Developer",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1062,
        group: "agency",
        name: "Marketplace Setup Expert (Amazon, Noon, etc.)",
        description: "Marketplace Setup Expert (Amazon, Noon, etc.)",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1063,
        group: "agency",
        name: "Drop shipping Consultant",
        description: "Drop shipping Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1064,
        group: "agency",
        name: "Payment Gateway Integration Specialist",
        description: "Payment Gateway Integration Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1065,
        group: "agency",
        name: "E-commerce Marketing Specialist",
        description: "E-commerce Marketing Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1066,
        group: "agency",
        name: "SEO/SEM Expert for Online Stores",
        description: "SEO/SEM Expert for Online Stores",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1067,
        group: "agency",
        name: "Social Media Marketing for Retail",
        description: "Social Media Marketing for Retail",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1068,
        group: "agency",
        name: "Email Marketing & Automation Expert",
        description: "Email Marketing & Automation Expert",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1069,
        group: "agency",
        name: "Influencer & Affiliate Marketing",
        description: "Influencer & Affiliate Marketing",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1070,
        group: "agency",
        name: "Product & Visual Services",
        description: "Product & Visual Services",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1071,
        group: "agency",
        name: "Product Photographer/Videographer",
        description: "Product Photographer/Videographer",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1072,
        group: "agency",
        name: "Graphic Designer for Online Stores",
        description: "Graphic Designer for Online Stores",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1073,
        group: "agency",
        name: "Branding & Packaging Consultant",
        description: "Branding & Packaging Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1074,
        group: "agency",
        name: "UX/UI Designer for Retail Platforms",
        description: "UX/UI Designer for Retail Platforms",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1075,
        group: "agency",
        name: "E-commerce Fulfillment Service",
        description: "E-commerce Fulfillment Service",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1076,
        group: "agency",
        name: "Logistics & Last-Mile Delivery Partner",
        description: "Logistics & Last-Mile Delivery Partner",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1077,
        group: "agency",
        name: "Customer Support & Live Chat Outsourcing",
        description: "Customer Support & Live Chat Outsourcing",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1078,
        group: "agency",
        name: "Return Management & Reverse Logistics Specialist",
        description: "Return Management & Reverse Logistics Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1079,
        group: "agency",
        name: "Warehouse Setup & Automation Consultant",
        description: "Warehouse Setup & Automation Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1080,
        group: "agency",
        name: "Sales & Conversion Rate Optimization Expert",
        description: "Sales & Conversion Rate Optimization Expert",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1081,
        group: "agency",
        name: "Retail Data Analyst",
        description: "Retail Data Analyst",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1082,
        group: "agency",
        name: "Consumer Behavior Consultant",
        description: "Consumer Behavior Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1083,
        group: "agency",
        name: "Pricing Strategy Specialist",
        description: "Pricing Strategy Specialist",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1084,
        group: "agency",
        name: "Retail Loyalty Program Consultant",
        description: "Retail Loyalty Program Consultant",
        parentId: 14,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Commercial Real Estate
      {
        id: 1085,
        group: "agency",
        name: "Commercial Brokerage Agencies",
        description: "Commercial Brokerage Agencies",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1086,
        group: "agency",
        name: "Business Center & Co-working Space Operators",
        description: "Business Center & Co-working Space Operators",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1087,
        group: "agency",
        name: "Retail Leasing Companies",
        description: "Retail Leasing Companies",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1088,
        group: "agency",
        name: "Industrial Property Developers",
        description: "Industrial Property Developers",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1089,
        group: "agency",
        name: "Business Parks & SEZ Developers",
        description: "Business Parks & SEZ Developers",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1090,
        group: "agency",
        name: "Commercial Real Estate Brokerage",
        description: "Commercial Real Estate Brokerage",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1091,
        group: "agency",
        name: "Leasing Agency– Retail Spaces",
        description: "Leasing Agency– Retail Spaces",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1092,
        group: "agency",
        name: "Leasing Agency – Office Spaces",
        description: "Leasing Agency – Office Spaces",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1093,
        group: "agency",
        name: "Leasing Agency– Industrial & Warehouse",
        description: "Leasing Agency– Industrial & Warehouse",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1094,
        group: "agency",
        name: "Mixed-Use Property agency",
        description: "Mixed-Use Property agency",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1095,
        group: "agency",
        name: "Commercial Property Investment Consultancy",
        description: "Commercial Property Investment Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1096,
        group: "agency",
        name: "Commercial Real Estate Developer",
        description: "Commercial Real Estate Developer",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1097,
        group: "agency",
        name: "Build-to-Suit Specialist",
        description: "Build-to-Suit Specialist",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1098,
        group: "agency",
        name: "Commercial Real Estate Legal Advisory",
        description: "Commercial Real Estate Legal Advisory",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1099,
        group: "agency",
        name: "Title & Land Registration Consultancy",
        description: "Title & Land Registration Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1100,
        group: "agency",
        name: "Regulatory & Permit Consultancy",
        description: "Regulatory & Permit Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1101,
        group: "agency",
        name: "Commercial Mortgage Consultancy",
        description: "Commercial Mortgage Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1102,
        group: "agency",
        name: "Loan Structuring Advisory",
        description: "Loan Structuring Advisory",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1103,
        group: "agency",
        name: "Commercial Property Valuation Consultancy",
        description: "Commercial Property Valuation Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1104,
        group: "agency",
        name: "Cap Rate & ROI Consultancy",
        description: "Cap Rate & ROI Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1105,
        group: "agency",
        name: "Commercial Property Management Company",
        description: "Commercial Property Management Company",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1106,
        group: "agency",
        name: "Facilities Management Consultancy",
        description: "Facilities Management Consultancy",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1107,
        group: "agency",
        name: "Tenant & Occupancy Management",
        description: "Tenant & Occupancy Management",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1108,
        group: "agency",
        name: "Commercial Real Estate Photographer/Videographer",
        description: "Commercial Real Estate Photographer/Videographer",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1109,
        group: "agency",
        name: "3D Virtual Tour & Drone Specialist",
        description: "3D Virtual Tour & Drone Specialist",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1110,
        group: "agency",
        name: "Branding for Commercial Projects",
        description: "Branding for Commercial Projects",
        parentId: 15,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Real Estate Investment
      {
        id: 1111,
        group: "agency",
        name: "REIT Management Companies",
        description: "REIT Management Companies",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1112,
        group: "agency",
        name: "International Property Investment Firms",
        description: "International Property Investment Firms",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1113,
        group: "agency",
        name: "Syndicated Investment Firms",
        description: "Syndicated Investment Firms",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1114,
        group: "agency",
        name: "High Net-Worth Investment Consultants",
        description: "High Net-Worth Investment Consultants",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1115,
        group: "agency",
        name: "Crowdfunded Real Estate Platforms",
        description: "Crowdfunded Real Estate Platforms",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1116,
        group: "agency",
        name: "Property Management Company",
        description: "Property Management Company",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1117,
        group: "agency",
        name: "Rental Income Management",
        description: "Rental Income Management",
        parentId: 16,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Health & Beauty
      {
        id: 1118,
        group: "agency",
        name: "Wellness Center Chains",
        description: "Wellness Center Chains",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1119,
        group: "agency",
        name: "Aesthetic Clinics",
        description: "Aesthetic Clinics",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1120,
        group: "agency",
        name: "Spa Franchises",
        description: "Spa Franchises",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1121,
        group: "agency",
        name: "Cosmetic Product Companies",
        description: "Cosmetic Product Companies",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1122,
        group: "agency",
        name: "Health Supplement Retailers",
        description: "Health Supplement Retailers",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1123,
        group: "agency",
        name: "Medical Tourism Facilitators",
        description: "Medical Tourism Facilitators",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1124,
        group: "agency",
        name: "General Health & Wellness Consultancy",
        description: "General Health & Wellness Consultancy",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1125,
        group: "agency",
        name: "Nutritionist / Dietitian",
        description: "Nutritionist / Dietitian",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1126,
        group: "agency",
        name: "Chiropractor",
        description: "Chiropractor",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1127,
        group: "agency",
        name: "Mental Health Counselor / Therapist",
        description: "Mental Health Counselor / Therapist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1128,
        group: "agency",
        name: "Wellness Retreat & Detox Consultancy",
        description: "Wellness Retreat & Detox Consultancy",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1129,
        group: "agency",
        name: "Dermatology Clinic",
        description: "Dermatology Clinic",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1130,
        group: "agency",
        name: "Aesthetic / Cosmetic Surgery Clinic",
        description: "Aesthetic / Cosmetic Surgery Clinic",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1131,
        group: "agency",
        name: "Laser Hair Removal Specialist",
        description: "Laser Hair Removal Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1132,
        group: "agency",
        name: "Skincare & Anti-Aging Clinic",
        description: "Skincare & Anti-Aging Clinic",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1133,
        group: "agency",
        name: "Hair Transplant Center",
        description: "Hair Transplant Center",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1134,
        group: "agency",
        name: "Dental Aesthetics & Whitening Specialist",
        description: "Dental Aesthetics & Whitening Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1135,
        group: "agency",
        name: "Medical Spa / MediSpa",
        description: "Medical Spa / MediSpa",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1136,
        group: "agency",
        name: "Beauty Salon Owner / Operator",
        description: "Beauty Salon Owner / Operator",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1137,
        group: "agency",
        name: "Hair Stylist / Barber",
        description: "Hair Stylist / Barber",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1138,
        group: "agency",
        name: "Makeup Artist",
        description: "Makeup Artist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1139,
        group: "agency",
        name: "Nail Technician / Salon",
        description: "Nail Technician / Salon",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1140,
        group: "agency",
        name: "Eyebrow & Lash Technician",
        description: "Eyebrow & Lash Technician",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1141,
        group: "agency",
        name: "Bridal & Event Beauty Specialist",
        description: "Bridal & Event Beauty Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1142,
        group: "agency",
        name: "Massage Therapist",
        description: "Massage Therapist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1143,
        group: "agency",
        name: "Luxury Spa",
        description: "Luxury Spa",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1144,
        group: "agency",
        name: "Aromatherapy",
        description: "Aromatherapy",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1145,
        group: "agency",
        name: "Thai / Swedish Massage",
        description: "Thai / Swedish Massage",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1146,
        group: "agency",
        name: "Cosmetics & Skincare Products",
        description: "Cosmetics & Skincare Products",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1147,
        group: "agency",
        name: "Organic Beauty Brand",
        description: "Organic Beauty Brand",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1148,
        group: "agency",
        name: "Cosmetic Product Distributor",
        description: "Cosmetic Product Distributor",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1149,
        group: "agency",
        name: "Beauty Product Retailer / Online Seller",
        description: "Beauty Product Retailer / Online Seller",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1150,
        group: "agency",
        name: "Skincare Consultancy",
        description: "Skincare Consultancy",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1151,
        group: "agency",
        name: "Makeup Product Specialist",
        description: "Makeup Product Specialist",
        parentId: 17,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Training & Education
      {
        id: 1152,
        group: "agency",
        name: "Training Centers",
        description: "Training Centers",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1153,
        group: "agency",
        name: "Certification Bodies",
        description: "Certification Bodies",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1154,
        group: "agency",
        name: "Vocational Institutions",
        description: "Vocational Institutions",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1155,
        group: "agency",
        name: "E-Learning Companies",
        description: "E-Learning Companies",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1156,
        group: "agency",
        name: "Corporate L&D Providers",
        description: "Corporate L&D Providers",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1157,
        group: "agency",
        name: "Professional Development Agencies",
        description: "Professional Development Agencies",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1158,
        group: "agency",
        name: "Language Training Organizations",
        description: "Language Training Organizations",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1159,
        group: "agency",
        name: "Home Tutor (Primary/Secondary)",
        description: "Home Tutor (Primary/Secondary)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1160,
        group: "agency",
        name: "Online Tutor / E-learning Specialist",
        description: "Online Tutor / E-learning Specialist",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1161,
        group: "agency",
        name: "Test Preparation Expert (SAT, IELTS, TOEFL, etc.)",
        description: "Test Preparation Expert (SAT, IELTS, TOEFL, etc.)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1162,
        group: "agency",
        name: "Curriculum Designer / Academic Content Developer",
        description: "Curriculum Designer / Academic Content Developer",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1163,
        group: "agency",
        name: "Private School Recruiter / Liaison",
        description: "Private School Recruiter / Liaison",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1164,
        group: "agency",
        name: "Corporate Trainer / Coach",
        description: "Corporate Trainer / Coach",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1165,
        group: "agency",
        name: "Leadership & Management Training",
        description: "Leadership & Management Training",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1166,
        group: "agency",
        name: "Sales & Marketing Skills Training",
        description: "Sales & Marketing Skills Training",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1167,
        group: "agency",
        name: "Soft Skills & Communication Coach",
        description: "Soft Skills & Communication Coach",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1168,
        group: "agency",
        name: "Customer Service & Hospitality Training",
        description: "Customer Service & Hospitality Training",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1169,
        group: "agency",
        name: "HR & Employee Development Specialist",
        description: "HR & Employee Development Specialist",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1170,
        group: "agency",
        name: "Health & Safety (HSE) Training",
        description: "Health & Safety (HSE) Training",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1171,
        group: "agency",
        name: "IT & Software Training",
        description: "IT & Software Training",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1172,
        group: "agency",
        name: "Trade Skills Trainer (Plumbing, Electrical, HVAC, etc.)",
        description: "Trade Skills Trainer (Plumbing, Electrical, HVAC, etc.)",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1173,
        group: "agency",
        name: "Language Centre",
        description: "Language Centre",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1174,
        group: "agency",
        name: "ESL (English as Second Language) Training",
        description: "ESL (English as Second Language) Training",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1175,
        group: "agency",
        name: "Translation & Interpretation",
        description: "Translation & Interpretation",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1176,
        group: "agency",
        name: "Training Center / Institute Owner",
        description: "Training Center / Institute Owner",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1177,
        group: "agency",
        name: "E-learning Platform Operator",
        description: "E-learning Platform Operator",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1178,
        group: "agency",
        name: "EdTech Solution Provider",
        description: "EdTech Solution Provider",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1179,
        group: "agency",
        name: "Education Franchise Consultant",
        description: "Education Franchise Consultant",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1180,
        group: "agency",
        name: "School or University Representative",
        description: "School or University Representative",
        parentId: 18,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Hajj & Umrah
      {
        id: 1181,
        group: "agency",
        name: "Hajj & Umrah Travel Agencies",
        description: "Hajj & Umrah Travel Agencies",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1182,
        group: "agency",
        name: "Pilgrimage Tour Operators",
        description: "Pilgrimage Tour Operators",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1183,
        group: "agency",
        name: "Group Travel Organizers",
        description: "Group Travel Organizers",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1184,
        group: "agency",
        name: "Visa Support & Documentation Firms",
        description: "Visa Support & Documentation Firms",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1185,
        group: "agency",
        name: "Accommodation & Logistics Providers",
        description: "Accommodation & Logistics Providers",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1186,
        group: "agency",
        name: "Hajj & Umrah App Developer / Provider",
        description: "Hajj & Umrah App Developer / Provider",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1187,
        group: "agency",
        name: "Digital Tawaf / Hajj Tracker Tools Provider",
        description: "Digital Tawaf / Hajj Tracker Tools Provider",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1188,
        group: "agency",
        name: "Online Booking Platform for Hajj & Umrah",
        description: "Online Booking Platform for Hajj & Umrah",
        parentId: 19,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Events & Entertainment
      {
        id: 1189,
        group: "agency",
        name: "Event Management Companies",
        description: "Event Management Companies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1190,
        group: "agency",
        name: "Stage & Audio-Visual Setup Providers",
        description: "Stage & Audio-Visual Setup Providers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1191,
        group: "agency",
        name: "Conference Organizers",
        description: "Conference Organizers",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1192,
        group: "agency",
        name: "Wedding & Social Event Planners",
        description: "Wedding & Social Event Planners",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1193,
        group: "agency",
        name: "Entertainment Booking Agencies",
        description: "Entertainment Booking Agencies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1194,
        group: "agency",
        name: "Exhibition Management Firms",
        description: "Exhibition Management Firms",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1195,
        group: "agency",
        name: "Festival Production Companies",
        description: "Festival Production Companies",
        parentId: 20,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Creative Services
      {
        id: 1196,
        group: "agency",
        name: "Design Studios",
        description: "Design Studios",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1197,
        group: "agency",
        name: "Animation & Motion Graphics Firms",
        description: "Animation & Motion Graphics Firms",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1198,
        group: "agency",
        name: "Video Production Houses",
        description: "Video Production Houses",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1199,
        group: "agency",
        name: "Content Creation Agencies",
        description: "Content Creation Agencies",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1200,
        group: "agency",
        name: "3D Visualization Companies",
        description: "3D Visualization Companies",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1201,
        group: "agency",
        name: "Photography & Videography Studios",
        description: "Photography & Videography Studios",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1202,
        group: "agency",
        name: "Storyboarding & Concept Design Firms",
        description: "Storyboarding & Concept Design Firms",
        parentId: 21,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // AI Agents
      {
        id: 1203,
        group: "agency",
        name: "AI Development Companies",
        description: "AI Development Companies",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1204,
        group: "agency",
        name: "AI Strategy Consulting Firms",
        description: "AI Strategy Consulting Firms",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1205,
        group: "agency",
        name: "Conversational AI Providers",
        description: "Conversational AI Providers",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1206,
        group: "agency",
        name: "NLP/Voice AI Solution Firms",
        description: "NLP/Voice AI Solution Firms",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1207,
        group: "agency",
        name: "Intelligent Automation Providers",
        description: "Intelligent Automation Providers",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1208,
        group: "agency",
        name: "Predictive Analytics Companies",
        description: "Predictive Analytics Companies",
        parentId: 22,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },

      // Personal Services

      // Emerging & Niche Categories
      {
        id: 1209,
        group: "agency",
        name: "Sustainability & ESG Consulting Firms",
        description: "Sustainability & ESG Consulting Firms",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1210,
        group: "agency",
        name: "Smart City Solutions Providers",
        description: "Smart City Solutions Providers",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1211,
        group: "agency",
        name: "Blockchain & Web3 Agencies",
        description: "Blockchain & Web3 Agencies",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1212,
        group: "agency",
        name: "IoT Solution Firms",
        description: "IoT Solution Firms",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1213,
        group: "agency",
        name: "Metaverse Development Companies",
        description: "Metaverse Development Companies",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1214,
        group: "agency",
        name: "Robotics Solution Providers",
        description: "Robotics Solution Providers",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1215,
        group: "agency",
        name: "Digital Twin Services",
        description: "Digital Twin Services",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1216,
        group: "agency",
        name: "Green Tech Agencies",
        description: "Green Tech Agencies",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1217,
        group: "agency",
        name: "Biometric & Identity Management Companies",
        description: "Biometric & Identity Management Companies",
        parentId: 24,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
      {
        id: 1217,
        group: null,
        name: "Consultancy",
        description: "Consultancy",
        parentId: null,
        typeId: 2,
        statusId: 1,
        createdBy: 1,
      },
    ])
    .onConflict("id") 
    .ignore();

  await knex.raw(`
    SELECT setval(
      pg_get_serial_sequence('"list"."services"', 'id'),
      (SELECT MAX(id) FROM "list"."services")
    )
  `);
}
