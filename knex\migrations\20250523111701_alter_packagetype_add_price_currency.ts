import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').alterTable('packagetype', (table) => {
    table.decimal('price', 12, 2).notNullable().defaultTo(0.00);
    table.string('currency', 10).notNullable().defaultTo('AED');
    table.string('userType', 20).notNullable();
    table.string('colorTheme', 20).notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').alterTable('packagetype', (table) => {
    table.dropColumn('price');
    table.dropColumn('currency');
    table.dropColumn('userType');
    table.dropColumn('colorTheme');
  });
}