import type { Knex } from "knex"; 

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.specificType('employmentProof', 'text[]').nullable().defaultTo(null); 
        table.specificType('emiratesId', 'text[]').nullable().defaultTo(null); 
        table.specificType('visa', 'text[]').nullable().defaultTo(null); 
 
  });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.dropColumn('employmentProof');
        table.dropColumn('emiratesId');
        table.dropColumn('visa');
        
    });
 
}



