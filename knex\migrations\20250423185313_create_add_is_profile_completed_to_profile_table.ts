import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.profile', function (table) {
        table.boolean('isProfileCompleted').defaultTo(true);
    });
}


export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.profile', function (table) {
        table.dropColumn('isProfileCompleted');
    });
}

