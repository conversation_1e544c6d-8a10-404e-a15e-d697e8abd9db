import express from "express";
import {
  getSettings,
  upsertSettings,
} from "../../../controller/admin/settings.controller";
import { storageData } from "../../../utils/services/multer";
const router = express.Router();

const upload = storageData("documents");

router.get("/", getSettings);
router.post(
  "/",
  upload.fields([
    { name: "logo", maxCount: 1 },
    { name: "favicon", maxCount: 1 },
  ]),
  upsertSettings
);

export default router;
