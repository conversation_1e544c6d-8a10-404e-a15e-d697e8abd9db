import { NextFunction, Request, Response } from "express";
import { errorCatchResponse, errorResponse, response } from "../utils/response";
import db from "../config/database";
import { verifyToken } from "../utils/services/jwt";
import { AUTH } from "../utils/database/queries/auth";
import { JwtPayload } from "jsonwebtoken";
import async<PERSON><PERSON><PERSON> from "./trycatch";

// Extend Express Request to include user object
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

export const adminAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
   
    const token = req.cookies?.adminAuthToken;

    if (!token) {
      response(res, 401, "Unauthorized access");
      return;
    }

    // Verify token
    const decoded = verifyToken(token) as { id: string };
    if (!decoded || !decoded.id) {
      response(res, 401, "Invalid token");
      return;
    }

    const userId = decoded.id;

    // Fetch user details
    const result = await db.query(
      AUTH.SELECT_PROFILE_WITH_LOGIN_AND_LOGIN_ROLE,
      [userId]
    );

    const user = result.rows[0];

    const userRole = await db.query(AUTH.SELECT_LOGIN_ROLE_BY_ID, [
      user.loginId,
    ]);

    if (userRole.rows.length > 0) {
      if (userRole.rows[0].name != "admin" && userRole.rows[0].name != "superAdmin") {
        errorResponse(res, "You are not authorized to access this resource.");
        return;
      }
      user.role = userRole.rows[0].name;
    } else {
      errorResponse(res, "You are not authorized to access this resource.");
      return;
    }

    if (!user) {
      response(res, 404, "Account not found");
      return;
    }

    // Check user verification and status
    if (!user.isActivated) {
      response(
        res,
        403,
        "Your account has been disabled by the administrator."
      );
      return;
    }
    
    req.user = user;
    next();
  } catch (error: any) {
    if (error.name === "JsonWebTokenError") {
      response(res, 401, "Unauthorized access!");
      return;
    }

    if (error.name === "TokenExpiredError") {
      response(res, 401, "Session expired, please sign in again!");

      return;
    }

    response(res, 500, "Internal server error!");
    return;
  }
};

export const verifyUserToken = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      let { id } = req.params;

      if (!id || isNaN(Number(id))) {
        return errorResponse(res, "User ID must be a valid number");
      }

      const userId = Number(id);
      if (!userId) {
        return errorResponse(res, "User ID parameter is required");
      }
      const token = req.cookies?.authToken;

      if (!token) {
        return errorResponse(res, "Invalid or expired token");
      }

      let decodedToken;
      try {
        decodedToken = verifyToken(token) as JwtPayload;
      } catch (err) {
        return errorResponse(res, "Invalid or expired token");
      }

      const loginId = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [userId]);

      const userRoleResult = await db.query(AUTH.SELECT_LOGIN_ROLE_BY_ID, [
        loginId.rows[0].id,
      ]);
      const userRoleData = userRoleResult.rows[0];
      // Fix: Logical condition should use AND (&&), not OR (||)
      if (userRoleData && userRoleData.name !== "superAdmin" && userRoleData.name !== "admin") {
        return response(res, 401, "Unauthorized access. Only admins are allowed.");
      }

      const isMatch = decodedToken.id === loginId.rows[0].id;

      if (!isMatch) {
        return errorResponse(res, "Invalid or expired token");
      }

      return response(res, 200, "Token verified successfully");
    } catch (error) {
      console.error("Error during token verification:", error);
      return errorCatchResponse(res, "Internal Server Error");
    }
  }
);