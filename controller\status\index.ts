import { Request, Response } from "express";
import { errorCatchResponse, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import db from "../../config/database";
import { STATUS } from "../../utils/database/queries/status";

export const getAllStatus = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const result = await db.query(STATUS.GET_ALL_STATUS);
      return responseData(res, 200, "Status fetched successfully", result.rows);
    } catch (error) {
      console.error("Error fetching Status:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const searchAgencies = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { search } = req.query;

    // Default to empty string if search is not provided
    const searchTerm = search ? search.toString() : '';

    const result = await db.query(STATUS.SEARCH_AGENCIES, [searchTerm]);
    return responseData(res, 200, "Agencies fetched successfully", result.rows);
  } catch (error) {
    console.error("Error fetching agencies:", error);
    return errorCatchResponse(res, "Something went wrong");
  }
});