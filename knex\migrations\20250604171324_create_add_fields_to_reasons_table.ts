import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('prf.reasons', (table) => {
    table.integer('oldStatusId').nullable();
    table.integer('newStatusId').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('prf.reasons', (table) => {
    table.dropColumn('oldStatusId');
    table.dropColumn('newStatusId');
  });
}
