import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
    ALTER TABLE look.packagefeaturesmeta
    DROP CONSTRAINT IF EXISTS packagefeaturesmeta_featurename_unique,
    DROP CONSTRAINT IF EXISTS packagefeaturesmeta_displayorder_unique;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("look.packagefeaturesmeta", (table) => {
    table.unique(["featureName"]);
    table.unique(["displayOrder"]);
  });
}
