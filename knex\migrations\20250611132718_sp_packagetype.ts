import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
        CREATE OR REPLACE FUNCTION look.sp_packagetype (
        p_fnid          integer,
        p_id            integer DEFAULT NULL::integer,
        p_name          text    DEFAULT NULL::text,
        p_description   text    DEFAULT NULL::text,
        p_statusid      integer DEFAULT NULL::integer,
        p_createdby     integer DEFAULT NULL::integer,
        p_price         numeric DEFAULT NULL::numeric,
        p_currency      text    DEFAULT NULL::text,
        p_usertype      text    DEFAULT NULL::text,
        p_colortheme    text    DEFAULT NULL::text,
        p_sortby        text    DEFAULT 'id'::text,
        -- NEW parameters (keep them last so old positional calls still work)
        p_stripeplanid  text    DEFAULT NULL::text,
        p_paypalplanid  text    DEFAULT NULL::text,
        p_interval      text    DEFAULT NULL::text,
        p_discountid    integer DEFAULT NULL::integer
        ) RETURNS jsonb
        LANGUAGE plpgsql
        AS $function$
        DECLARE
            v_data    jsonb;
            v_title   text;
            v_result  int;
            v_type    text;
            v_message text;
        BEGIN
            -- 0 ► single record
            IF p_fnid = 0 THEN
                SELECT to_jsonb(t) INTO v_data
                FROM   look.packagetype t
                WHERE  t.id = p_id;

                v_title   := 'Package Type - Single Record';
                v_result  := 1;
                v_type    := 'success';
                v_message := 'Record selected successfully.';

            -- 1 ► list
            ELSIF p_fnid = 1 THEN
                WITH sorted_rows AS (
                    SELECT to_jsonb(t) AS row_json
                    FROM   look.packagetype t
                    WHERE  (p_id IS NULL OR t.id = p_id)
                    ORDER  BY CASE
                                WHEN p_sortby = 'name' THEN t.name
                                ELSE t.id::text
                            END
                )
                SELECT jsonb_agg(row_json) INTO v_data FROM sorted_rows;

                v_title   := 'Package Type - List Records';
                v_result  := 1;
                v_type    := 'success';
                v_message := 'Records selected successfully.';

            -- 2 ► insert / update
            ELSIF p_fnid = 2 THEN

                -- INSERT
                IF p_id IS NULL THEN
                    INSERT INTO look.packagetype (
                        name, description, "statusId", "createdBy", "createdOn",
                        price, currency, "userType", "colorTheme",
                        "stripePlanId", "payPalPlanId", "interval", "discountId"
                    ) VALUES (
                        p_name, p_description, p_statusid, p_createdby, NOW(),
                        p_price, p_currency, p_usertype, p_colortheme,
                        p_stripeplanid, p_paypalplanid, p_interval, p_discountid
                    )
                    RETURNING to_jsonb(look.packagetype.*) INTO v_data;

                    v_title   := 'Package Type - Insert';
                    v_result  := 1;
                    v_type    := 'success';
                    v_message := 'Record inserted successfully.';

                -- UPDATE
                ELSE
                    UPDATE look.packagetype
                    SET    name           = p_name,
                        description    = p_description,
                        "statusId"     = p_statusid,
                        "modifiedOn"   = NOW(),
                        price          = p_price,
                        currency       = p_currency,
                        "userType"     = p_usertype,
                        "colorTheme"   = p_colortheme,
                        "stripePlanId" = p_stripeplanid,
                        "payPalPlanId" = p_paypalplanid,
                        "interval"     = p_interval,
                            "discountId"   = p_discountid
                    WHERE  id = p_id
                    RETURNING to_jsonb(look.packagetype.*) INTO v_data;

                    v_title   := 'Package Type - Update';
                    v_result  := 2;
                    v_type    := 'success';
                    v_message := 'Record updated successfully.';
                END IF;

            -- 3 ► delete
            ELSIF p_fnid = 3 THEN
                DELETE FROM look.packagetype
                WHERE  id = p_id
                RETURNING to_jsonb(look.packagetype.*) INTO v_data;

                v_title   := 'Package Type - Delete';
                v_result  := 3;
                v_type    := 'success';
                v_message := 'Record deleted successfully.';

            -- 4 ► status change
            ELSIF p_fnid = 4 THEN
                UPDATE look.packagetype
                SET    "statusId"   = p_statusid,
                    "modifiedOn" = NOW()
                WHERE  id = p_id
                RETURNING to_jsonb(look.packagetype.*) INTO v_data;

                v_title   := 'Package Type - Status Update';
                v_result  := 2;
                v_type    := 'success';
                v_message := 'Status updated successfully.';

            -- invalid fnid
            ELSE
                v_title   := 'Package Type - Error';
                v_result  := -1;
                v_type    := 'error';
                v_message := 'Invalid Function ID.';
                v_data    := NULL;
            END IF;

            RETURN jsonb_build_object(
                'title',   v_title,
                'result',  v_result,
                'type',    v_type,
                'message', v_message,
                'data',    COALESCE(v_data, 'null'::jsonb)
            );
        END;
        $function$;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`
        DROP FUNCTION IF EXISTS look.sp_packagetype (
        integer,  -- p_fnid
        integer,  -- p_id
        text,     -- p_name
        text,     -- p_description
        integer,  -- p_statusid
        integer,  -- p_createdby
        numeric,  -- p_price
        text,     -- p_currency
        text,     -- p_usertype
        text,     -- p_colortheme
        text,     -- p_sortby
        text,     -- p_stripeplanid
        text,     -- p_paypalplanid
        text      -- p_interval,
        integer   -- p_discountid
        );
  `);
}
