import express from "express";
import * as authController from "../../../controller/auth";
import { storageData, uploadError<PERSON><PERSON><PERSON> } from "../../../utils/services/multer";
import { authMiddleware } from "../../../middleware/authMiddleware";
import { agentLogin } from "../../../controller/agents";

const router = express.Router();

const upload = storageData("users");

router.post("/register", upload.none(), authController.registerAgentAccount);
router.post("/account-verify", upload.none(), authController.accounrtVerifyOTP);
router.post("/verify-otp", upload.none(), authController.verifyOTP);
router.post("/login", upload.none(), agentLogin);
router.post(
  "/forgot-password",
  upload.none(),
  authController.forgotPasswordOTP
);
router.post("/resend-otp", upload.none(), authController.resendOTP);
router.post(
  "/reset-password",
  upload.none(),
  authController.updatePasswordWithOTP
);
router.post("/validate-email", upload.none(), authController.validateEmail);
router.post(
  "/validate-username",
  upload.none(),
  authController.validateUserName
);
router.post(
  "/change-password",
  upload.none(),
  authMiddleware,
  authController.changeOrResetPassword
);

router.get("/logout", authMiddleware, authController.logout);

router.get("/get-user-profile", authMiddleware, authController.getProfile);
router.post(
  "/update-profile",
  authMiddleware,
  upload.single("image"),
  uploadErrorHandler,
  authController.updateProfile
);

router.get(
  "/account-status",
  authMiddleware,
  authController.accountActivatedDeactivated
);
router.put("/password", authMiddleware, authController.updatePassword);

router.get("/session/:id", authController.verifyUserToken);
router.get("/get-user-type/:id", authController.fetchUserAccountType);
router.get("/resubmit/:id", authController.fetchUserAccountTypeForResubmit);

export default router;
