/*  FINAL VERSION – 2025-07-05
    • adds p_search_text search
    • fixes placeholder counts
    • fixes boolean literal in v_conv_filter
    • keeps all original CRUD & notes logic                            */

CREATE OR REPLACE FUNCTION look.sp_leads_notes (
    -- ───────────── inputs ─────────────
    p_fnid           INTEGER,
    p_lead_id        INTEGER,
    p_full_name      TEXT,
    p_email          TEXT,
    p_phone          TEXT,
    p_license_number TEXT,
    p_company        TEXT,
    p_lead_type      TEXT,
    p_status_id      INTEGER,
    p_source         TEXT,
    p_is_converted   BOOLEAN,
    p_note_id        INTEGER,
    p_entity_type    TEXT,
    p_entity_id      INTEGER,
    p_note           TEXT,
    p_sortby         TEXT,
    p_filter_column  TEXT,
    p_filter_value   TEXT,
    -- ───────── pagination ─────────
    p_page_no        INTEGER DEFAULT 1,
    p_page_size      INTEGER DEFAULT 10,
    -- ───────── free-text search ─────────
    p_search_text    TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $BODY$
DECLARE
    /* generic result */
    v_data        JSONB;
    v_title       TEXT;
    v_result      INT;
    v_type        TEXT;
    v_message     TEXT;

    /* pagination helpers */
    v_limit       INT := GREATEST(COALESCE(p_page_size,10),1);
    v_offset      INT := (GREATEST(COALESCE(p_page_no,1),1)-1) * v_limit;
    v_total       INT := 0;
    v_returned    INT := 0;
    v_pagination  JSONB := NULL;

    /* helpers for optional filters */
    v_conv_filter   TEXT := '';
    v_search_filter TEXT := '';
BEGIN
    /* ───── converted flag filter ───── */
    IF p_is_converted IS NOT NULL THEN
        v_conv_filter := format(
          ' AND t."isConverted" = %s',
          CASE WHEN p_is_converted THEN 'true' ELSE 'false' END
        );
    END IF;

    /* ───── free-text search filter ───── */
    IF p_search_text IS NOT NULL AND length(trim(p_search_text)) > 0 THEN
        v_search_filter := format(
          ' AND ( t."fullName" ILIKE %L
                  OR t."email"      ILIKE %L
                  OR t."phone"      ILIKE %L
                  OR t."company"    ILIKE %L
                  OR t."source"     ILIKE %L )',
          '%'||p_search_text||'%',
          '%'||p_search_text||'%',
          '%'||p_search_text||'%',
          '%'||p_search_text||'%',
          '%'||p_search_text||'%'
        );
    END IF;

    -------------------------------------------------------------------
    -- 0.  Single lead
    -------------------------------------------------------------------
    IF p_fnid = 0 THEN
        SELECT to_jsonb(r) INTO v_data
        FROM (
            SELECT t.*, s.name AS "statusName"
            FROM   look.leads t
            LEFT   JOIN look.status s ON t."statusId" = s.id
            WHERE  t.id = p_lead_id
        ) r;

        v_title   := 'Lead - Single Record';
        v_result  := 1;
        v_type    := 'success';
        v_message := 'Record fetched successfully.';

    -------------------------------------------------------------------
    -- 1.  Leads list (paginated, with total)
    -------------------------------------------------------------------
    ELSIF p_fnid = 1 THEN
        /* helper for optional status filter */
        DECLARE
            v_status_filter TEXT := '';
            v_filter_pattern TEXT;
        BEGIN
            IF p_status_id IS NOT NULL THEN
                v_status_filter := format(' AND t."statusId" = %s', p_status_id);
            END IF;

            /* ─── branch A : column/value filter ─── */
            IF p_filter_column IS NOT NULL AND p_filter_value IS NOT NULL THEN
                v_filter_pattern := '%'||p_filter_value||'%';

                -- total
                EXECUTE format(
                  'SELECT COUNT(*)
                     FROM   look.leads t
                     LEFT   JOIN look.status s ON t."statusId" = s.id
                    WHERE  t.%I::text ILIKE %L%s%s%s',
                  p_filter_column,
                  v_filter_pattern,
                  v_status_filter,
                  v_conv_filter,
                  v_search_filter
                )
                INTO v_total;

                -- slice
                EXECUTE format(
                  $q$
                  SELECT jsonb_agg(to_jsonb(r))
                  FROM (
                      SELECT t.*, s.name AS "statusName"
                      FROM   look.leads t
                      LEFT   JOIN look.status s ON t."statusId" = s.id
                      WHERE  t.%I::text ILIKE %L%s%s%s
                      ORDER  BY t."created_at" DESC
                      LIMIT  %s OFFSET %s
                  ) r
                  $q$,
                  p_filter_column,
                  v_filter_pattern,
                  v_status_filter,
                  v_conv_filter,
                  v_search_filter,
                  v_limit,
                  v_offset
                ) INTO v_data;

                v_title :=
                  CASE WHEN p_status_id IS NOT NULL
                       THEN format('Lead - Filtered by %s + statusId (page %s)',
                                   p_filter_column, p_page_no)
                       ELSE format('Lead - Filtered by %s (page %s)',
                                   p_filter_column, p_page_no)
                  END;

            /* ─── branch B : status-only filter ─── */
            ELSIF p_status_id IS NOT NULL THEN
                EXECUTE format(
                  'SELECT COUNT(*) FROM look.leads t
                    WHERE t."statusId" = %s%s%s',
                  p_status_id,
                  v_conv_filter,
                  v_search_filter
                )
                INTO v_total;

                EXECUTE format(
                  $q$
                  SELECT jsonb_agg(to_jsonb(r))
                  FROM (
                      SELECT t.*, s.name AS "statusName"
                      FROM   look.leads  t
                      LEFT   JOIN look.status s ON t."statusId" = s.id
                      WHERE  t."statusId" = %s%s%s
                      ORDER  BY t."created_at" DESC
                      LIMIT  %s OFFSET %s
                  ) r
                  $q$,
                  p_status_id,
                  v_conv_filter,
                  v_search_filter,
                  v_limit,
                  v_offset
                ) INTO v_data;

                v_title := format('Lead - statusId filter (page %s)', p_page_no);

            /* ─── branch C : no explicit filters ─── */
            ELSE
                EXECUTE format(
                  'SELECT COUNT(*) FROM look.leads t WHERE TRUE%s%s',
                  v_conv_filter,
                  v_search_filter
                )
                INTO v_total;

                EXECUTE format(
                  $q$
                  SELECT jsonb_agg(to_jsonb(r))
                  FROM (
                      SELECT t.*, s.name AS "statusName"
                      FROM   look.leads  t
                      LEFT   JOIN look.status s ON t."statusId" = s.id
                      WHERE  TRUE%s%s
                      ORDER  BY t."created_at" DESC
                      LIMIT  %s OFFSET %s
                  ) r
                  $q$,
                  v_conv_filter,
                  v_search_filter,
                  v_limit,
                  v_offset
                ) INTO v_data;

                v_title := format('Lead - All (page %s)', p_page_no);
            END IF;
        END;

        /* pagination JSON */
        v_returned  := COALESCE(jsonb_array_length(v_data),0);
        v_pagination := jsonb_build_object(
            'page',      p_page_no,
            'pageSize',  v_limit,
            'returned',  v_returned,
            'total',     v_total
        );

        v_result  := 1;
        v_type    := 'success';
        v_message := 'Records fetched successfully.';

    -------------------------------------------------------------------
    -- 2.  Insert / update lead
    -------------------------------------------------------------------
    ELSIF p_fnid = 2 THEN
        IF p_lead_id IS NULL THEN
            INSERT INTO look.leads (
              "fullName", "email", "phone", "licenseNumber",
              "company",  "leadType", "statusId", "source",
              "isConverted", "created_at", "updated_at"
            )
            VALUES (
              p_full_name, p_email, p_phone, p_license_number,
              p_company,   p_lead_type, p_status_id, p_source,
              COALESCE(p_is_converted,FALSE), NOW(), NOW()
            )
            RETURNING to_jsonb(look.leads.*) INTO v_data;

            v_title   := 'Lead - Inserted';
            v_result  := 1;
            v_type    := 'success';
            v_message := 'Lead created successfully.';
        ELSE
            UPDATE look.leads
            SET
              "fullName"      = p_full_name,
              "email"         = p_email,
              "phone"         = p_phone,
              "licenseNumber" = p_license_number,
              "company"       = p_company,
              "leadType"      = p_lead_type,
              "statusId"      = p_status_id,
              "source"        = p_source,
              "isConverted"   = p_is_converted,
              "updated_at"    = NOW()
            WHERE id = p_lead_id
            RETURNING to_jsonb(look.leads.*) INTO v_data;

            v_title   := 'Lead - Updated';
            v_result  := 2;
            v_type    := 'success';
            v_message := 'Lead updated successfully.';
        END IF;

    -------------------------------------------------------------------
    -- 3.  Delete lead
    -------------------------------------------------------------------
    ELSIF p_fnid = 3 THEN
        DELETE FROM look.leads
        WHERE id = p_lead_id
        RETURNING to_jsonb(look.leads.*) INTO v_data;

        v_title   := 'Lead - Deleted';
        v_result  := 3;
        v_type    := 'success';
        v_message := 'Lead deleted successfully.';

    -------------------------------------------------------------------
    -- 10. Add note
    -------------------------------------------------------------------
    ELSIF p_fnid = 10 THEN
        INSERT INTO look.notes (
          "entityType", "entityId", "note", "created_at", "updated_at"
        )
        VALUES (
          p_entity_type, p_entity_id, p_note, NOW(), NOW()
        )
        RETURNING to_jsonb(look.notes.*) INTO v_data;

        v_title   := 'Note - Inserted';
        v_result  := 1;
        v_type    := 'success';
        v_message := 'Note added successfully.';

    -------------------------------------------------------------------
    -- 11. Get notes
    -------------------------------------------------------------------
    ELSIF p_fnid = 11 THEN
        IF p_entity_type IS NOT NULL AND p_entity_id IS NOT NULL THEN
            SELECT jsonb_agg(to_jsonb(t)) INTO v_data
            FROM   look.notes t
            WHERE  t."entityType" = p_entity_type
              AND  t."entityId"   = p_entity_id
            ORDER  BY t."created_at" DESC;
            v_title := 'Notes - By Entity';
        ELSE
            SELECT jsonb_agg(to_jsonb(t)) INTO v_data
            FROM   look.notes t
            ORDER  BY t."created_at" DESC;
            v_title := 'Notes - All';
        END IF;

        v_result  := 1;
        v_type    := 'success';
        v_message := 'Notes fetched successfully.';

    -------------------------------------------------------------------
    -- invalid fnid
    -------------------------------------------------------------------
    ELSE
        v_title   := 'Lead - Error';
        v_result  := -1;
        v_type    := 'error';
        v_message := 'Invalid Function ID.';
        v_data    := NULL;
    END IF;

    /* ───── final JSON to caller ───── */
    RETURN jsonb_build_object(
        'title',      v_title,
        'result',     v_result,
        'type',       v_type,
        'message',    v_message,
        'data',       COALESCE(v_data,'null'::jsonb),
        'pagination', COALESCE(v_pagination,'null'::jsonb)
    );
END;
$BODY$;
