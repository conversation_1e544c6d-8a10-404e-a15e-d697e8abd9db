import { TABLE } from "../table";

export const LEADS = {
  GET_LEADS_COUNTS: `
    SELECT
      ld."statusId",
      st."name" AS "statusName",
      COUNT(*) AS "leadsCount"
    FROM look.leads ld
    LEFT JOIN ${TABLE.STATUSES} st ON st.id = ld."statusId"
    GROUP BY ld."statusId", st."name";`,

  GET_LEADS_COUNTS_WITH_STATUS: `
  SELECT
      ld."statusId",
      st."name" AS "statusName",
      COUNT(*) AS "leadsCount"
    FROM look.leads ld
    LEFT JOIN ${TABLE.STATUSES} st ON st.id = ld."statusId"
       WHERE ld."statusId" = $1
    GROUP BY ld."statusId", st."name";`,
};
