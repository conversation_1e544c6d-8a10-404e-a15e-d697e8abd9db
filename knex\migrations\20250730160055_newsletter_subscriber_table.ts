import type { Knex } from "knex"; 
export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("newsletters", (table) => {
    table.increments("id").primary(); 
    table.string("email").nullable();
    table.integer("statusId").nullable();
    table.string("source").nullable(); 
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("newsletters");
}


