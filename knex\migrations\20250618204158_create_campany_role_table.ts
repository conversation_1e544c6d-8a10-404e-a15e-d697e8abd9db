import type { Knex } from "knex";
  
export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('companyrole', function(table) {
    table.increments('id').primary(); // Primary key
    table.integer('companyId').notNullable(); // Required
    table.integer('roleId').notNullable(); // Required
    table.string('roleName').nullable();
    table.specificType('rolesList', 'text[]').nullable(); // Array of strings
    table.timestamp('timestamp').defaultTo(knex.fn.now()); // Optional created timestamp
  });
};

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('companyrole');
};


