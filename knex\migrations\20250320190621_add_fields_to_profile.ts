import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("prf.profile", (table) => {
    table.string("designation", 256).nullable();
    table.text("shortDescription").nullable();
    table.text("description").nullable();
    table.text("specialization").nullable();
    table.integer("experience").nullable();
    table.text("languages").nullable();
    table.string("industry", 256).nullable();
    table.boolean("certified").notNullable().defaultTo(false);
    table.string("certificateNumber", 256).nullable();
    table.date("expiryDate").nullable();
    table.string("contactNumber", 256).nullable();
    table.string("whatsappContact", 256).nullable();
    table.string("contactEmail", 256).nullable();
    table.string("cardHolderName", 256).nullable();
    table.string("cardType", 256).nullable();
    table.bigInteger("cardNumber").nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("prf.profile", (table) => {
    table.dropColumn("designation");
    table.dropColumn("shortDescription");
    table.dropColumn("description");
    table.dropColumn("specialization");
    table.dropColumn("experience");
    table.dropColumn("languages");
    table.dropColumn("industry");
    table.dropColumn("certified");
    table.dropColumn("issuedBy");
    table.dropColumn("certificateNumber");
    table.dropColumn("expiryDate");
    table.dropColumn("contactNumber");
    table.dropColumn("whatsappContact");
    table.dropColumn("contactEmail");
    table.dropColumn("cardHolderName");
    table.dropColumn("cardType");
    table.dropColumn("cardNumber");
  });
}
