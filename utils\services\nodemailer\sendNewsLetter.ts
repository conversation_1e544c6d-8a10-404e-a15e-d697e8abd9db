import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

 
export const sendNewsLetterEmail = async (
    subject: string,
    message: string, 
    to: string[],
    res: Response,
    
): Promise<void> => { 
    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string, 
        to,
        subject,
        html: await bulkEmailHTMLTemplate(message), 
        
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log("Bulk email sent successfully!");
        response(res, 200, "Bulk email sent successfully");
    } catch (error) {
        console.error("Error sending bulk email:", error);
        response(res, 500, "Failed to send bulk email");
    }
};

 

const bulkEmailHTMLTemplate = async (message: string): Promise<string> => `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FindAnyAgent Message</title>
  </head>
  <body style="margin:0;padding:0;background:#f9f9f9;">
    <!-- ===== Outer wrapper (100% width) ===== -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f9f9f9;">
      <tr>
        <td align="center" style="padding:40px 10px;">
          <!-- ===== Centered card (max 600px) ===== -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0"
                 style="max-width:600px;background:#ffffff;border-radius:8px;
                        box-shadow:0 0 10px rgba(0,0,0,0.1);">
            <tr>
              <td style="padding:24px 32px;font-family:Arial,sans-serif;color:#333333;line-height:1.6;">
                <!-- Header -->
                <h1 style="margin:0 0 20px 0;font-size:22px;color:#5e9b6d;">
                  FindAnyAgent Message
                </h1>

                <!-- Dynamic content -->
                <div style="font-size:16px;">${message}</div>

                <!-- Footer -->
                <p style="margin:32px 0 0 0;font-size:14px;color:#777777;">
                  Best regards,<br />
                  <strong>FindAnyAgent Team</strong>
                </p>
              </td>
            </tr>
          </table>
          <!-- ===== End card ===== -->
        </td>
      </tr>
    </table>
    <!-- ===== End wrapper ===== -->
  </body>
</html>`;
 