import { UpdateUserProfile, UserProfile } from "../../types/user";
import { TABLE } from "../table";

export const USERDETAIL = {
  GET_ALL_USER_DETAILS: `SELECT id, "userId", designation, "shortDescription", description, specialization, experience, languages, industry, "accountType", association, certified, "issuedBy", "certificateNumber", "expiryDate", "contactNumber", "whatsappContact", "contactEmail", "cardHolderName", "cardType", "cardNumber" FROM "${TABLE.USERDETAIL}" LIMIT $1 OFFSET $2`,
  USER_DETAIL_BY_ID: `SELECT id, "userId", designation, "shortDescription", description, specialization, experience, languages, industry, "accountType", association, certified, "issuedBy", "certificateNumber", "expiryDate", "contactNumber", "whatsappContact", "contactEmail", "cardHolderName", "cardType", "cardNumber" FROM "${TABLE.USERDETAIL}" WHERE id = $1`,
  GET_TOTAL_USER_DETAILS: `SELECT COUNT(*) AS total_user_details FROM "${TABLE.USERDETAIL}"`,
  USER_DETAIL_BY_USERID: `SELECT id, "userId", designation, "shortDescription", description, specialization, experience, languages, industry, "accountType", association, certified, "issuedBy", "certificateNumber", "expiryDate", "contactNumber", "whatsappContact", "contactEmail", "cardHolderName", "cardType", "cardNumber" FROM "${TABLE.USERDETAIL}" WHERE "userId" = $1`,

  CREATE_USER_DETAIL: (fields: UserProfile) => {
    const columns = Object.keys(fields).map((col) => `"${col}"`).join(", ");
    const values = Object.keys(fields)
      .map((_, index) => `$${index + 1}`)
      .join(", ");

    return `INSERT INTO "${TABLE.USERDETAIL}" (${columns}) VALUES (${values}) RETURNING *;`;
  },

  UPDATE_USER_DETAIL: (fields: UpdateUserProfile) => {
    const setClause = Object.keys(fields)
      .filter((key) => key !== "id")
      .map((key, index) => `"${key}" = $${index + 1}`)
      .join(", ");

    return `UPDATE "${TABLE.USERDETAIL}" SET ${setClause} WHERE id = $${Object.keys(fields).length} RETURNING *;`;
  },
  DELETE_USER_DETAIL: `DELETE FROM "${TABLE.USERDETAIL}" WHERE id = $1`,
};