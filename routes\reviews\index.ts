import express from "express";
import { authMiddleware } from "../../middleware/authMiddleware";
import {
  validateCreateReview,
  validateGetReviewsQuery,
} from "../../middleware/reviewsValidation";
import {
  createReview,
  getProfileReviews,
  getProfileRatingStats,
  getUserReviews,
} from "../../controller/reviews";
import { storageData } from "../../utils/services/multer";

const router = express.Router();
const upload = storageData("reviews");

// Public routes (no authentication required)
router.get("/profile/:profileId", validateGetReviewsQuery, getProfileReviews); // Get reviews for a specific profile
router.get("/profile/:profileId/stats", getProfileRatingStats); // Get rating stats for a profile

// Protected routes (authentication required)
router.use(authMiddleware); // Apply authentication middleware

router.post("/", upload.none(), validateCreateReview, createReview); // Create a new review
router.get("/my-reviews", validateGetReviewsQuery, getUserReviews); // Get user's own reviews

export default router;
