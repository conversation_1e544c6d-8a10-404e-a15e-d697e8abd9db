import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').createTable('packagefeaturevalues', (table) => {
    table.increments('packageFeatureId').primary();

    table.integer('packageTypeId').notNullable().unsigned();
    table.foreign('packageTypeId').references('id').inTable('look.packagetype').onDelete('CASCADE');

    table.integer('featureId').notNullable().unsigned();
    table.foreign('featureId').references('featureId').inTable('look.packagefeaturesmeta').onDelete('CASCADE');

    table.string('featureValue', 255).nullable();

    table.timestamps(true, true); // Automatically adds created_at and updated_at
    
    table.unique(['packageTypeId', 'featureId']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').dropTableIfExists('packagefeaturevalues');
}