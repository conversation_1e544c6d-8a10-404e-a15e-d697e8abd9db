import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";

export const sendForgotPasswordOTP = async (
  username: string,
  email: string,
  res: Response,
  otp: string
): Promise<void> => {
  // Validate the email before proceeding
  if (!email || typeof email !== "string" || email.trim() === "") {
    console.error("Error: Invalid email address provided.");
    res.status(400).json({ message: "Invalid email address." });
  }

  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email.trim(),
    subject: "Reset Your Password - FindAnyAgent",
    html: await forgotPasswordOTPTemplate(otp, username),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Forgot Password OTP sent successfully to ${email}`);

    response(
      res,
      200,
      "An OTP has been sent to your email for password reset."
    );
  } catch (error) {
    console.error("Error sending Forgot Password OTP:", error);
    res.status(500).json({
      message: "Unable to send OTP for password reset. Please try again later.",
    });
  }
};

export const forgotPasswordOTPTemplate = async (otp: string, username: string) => {
  return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .header {
            font-size: 22px;
            font-weight: bold;
            color: #e74c3c;
        }
        .content {
            margin: 20px 0;
            font-size: 16px;
        }
        .otp {
            display: inline-block;
            font-size: 28px;
            font-weight: bold;
            color: #e74c3c;
            background-color: #fdecea;
            padding: 12px 24px;
            border-radius: 6px;
            letter-spacing: 3px;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
        }
        .note {
            font-size: 14px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Reset Your Password</div>
        <div class="content">
            <p>Hi ${username},</p>
            <p>We received a request to reset your password. Use the OTP below to proceed:</p>
            <p class="otp">${otp}</p>
            <p class="note">This OTP is valid for 10 minutes. If you did not request this, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>FindAnyAgent Team</strong></p>
        </div>
    </div>
</body>
</html>`;
};
