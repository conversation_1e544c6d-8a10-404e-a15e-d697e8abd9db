CREATE SCHEMA IF NOT EXISTS look;
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

DROP TABLE IF EXISTS "look"."status";
CREATE TABLE "look"."status" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "parentId" int4,
    "description" text,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_status_parent" FOREIGN KEY ("parentId") REFERENCES "look"."status"("id")
);

-- Indices
CREATE UNIQUE INDEX idx_16491_pk__status__3214ec077aef2ee3 ON look.status USING btree (id);

DROP TABLE IF EXISTS "look"."group";
CREATE TABLE "look"."group" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "parentId" int4,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_group_parent" FOREIGN KEY ("parentId") REFERENCES "look"."group"("id"),
    CONSTRAINT "fk_group_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16477_pk__group__3214ec073a6ac58e ON look."group" USING btree (id);

DROP TABLE IF EXISTS "look"."packagetype";
CREATE TABLE "look"."packagetype" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "description" text,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "createdBy" int4 NOT NULL,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_packagetype_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16484_pk__packagetype__3214ec079bec2a46 ON look."packagetype" USING btree (id);



DROP TABLE IF EXISTS "look"."type";
CREATE TABLE "look"."type" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "parentId" int4,
    "description" text,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_type_parent" FOREIGN KEY ("parentId") REFERENCES "look"."type"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16497_pk__type__3214ec079350d772 ON look.type USING btree (id);