// migrations/20250619_create_notes.ts
import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("look.notes", (table) => {
    table.increments("id").primary();
    table.string("entityType").notNullable();
    table.integer("entityId").unsigned().notNullable();
    table.integer('statusId').nullable();
    table.text("note").notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("look.notes");
}
