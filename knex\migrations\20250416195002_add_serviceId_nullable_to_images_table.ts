import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.withSchema("prf").alterTable("images", (table) => {
    table.integer("serviceId").nullable().alter();
    table.integer("typeId").nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.withSchema("prf").alterTable("images", (table) => {
    table.integer("serviceId").notNullable().alter();
    table.integer("typeId").notNullable().alter();
  });
}
