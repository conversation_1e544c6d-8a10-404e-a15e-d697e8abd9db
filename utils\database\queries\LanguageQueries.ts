import { DatabaseSchema } from "../DatabaseSchema";
import { TABLE } from "../table";

export const UserQueries = {
    QUERY_FIND_ALL: `SELECT * FROM ${DatabaseSchema.LIST}.${TABLE.LANGUAGE} ORDER BY name ASC;`,
    QUERY_FIND_BY_ID: `SELECT * FROM ${DatabaseSchema.LIST}.${TABLE.LANGUAGE} WHERE id = $1`,
    QUERY_UPDATE_NAME: `UPDATE ${DatabaseSchema.LIST}.${TABLE.LANGUAGE} SET name = $1 WHERE id = $2`,
};