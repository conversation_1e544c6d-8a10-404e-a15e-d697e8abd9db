import express from "express";
import {
  getAllTickets,
  getTicketById,
  createTicket,
  updateTicket,
  deleteTicket,
  getResponsesByTicketId,
  createResponse,
  getTicketSummary,
  getTicketMeta,
} from "../../../controller/admin/tickets";

const router = express.Router();

router.get("/", getAllTickets);
router.get("/summary", getTicketSummary);
router.get("/meta", getTicketMeta);
router.get("/:id", getTicketById);
router.post("/", createTicket);
router.put("/:id", updateTicket);
router.delete("/:id", deleteTicket);
router.get("/:id/responses", getResponsesByTicketId);
router.post("/:id/responses", createResponse);

export default router; 