import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    await knex.schema.withSchema('agn').createTable('userreferrals', (table) => {
        // Auto-incrementing primary key
        table.increments('id').primary();

        // Foreign key to profile table in 'prf' schema
        table.integer('userId').notNullable();
        table.foreign('userId').references('id').inTable('prf.profile').onDelete('CASCADE');

        // Foreign key to referrals table in 'agn' schema
        table.string('referralId').notNullable();
        table.foreign('referralId').references('referralId').inTable('agn.referrals').onDelete('CASCADE');

        // Explicitly defined created_at and updated_at columns
        table.timestamp('createdOn').notNullable().defaultTo(knex.fn.now());
        table.timestamp('updatedOn').notNullable().defaultTo(knex.fn.now());
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.withSchema('agn').dropTableIfExists('userreferrals');
}