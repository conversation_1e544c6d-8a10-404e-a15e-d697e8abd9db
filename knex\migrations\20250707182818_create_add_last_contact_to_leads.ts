import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("look.leads", (table) => {
    table
      .timestamp("last_contact")
      .nullable()
      .comment("Last date/time the lead was contacted");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("look.leads", (table) => {
    table.dropColumn("last_contact");
  });
}
