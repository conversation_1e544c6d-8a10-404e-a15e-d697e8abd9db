import { differenceInCalendarDays, isToday } from 'date-fns';
import { sendExpiredEmail } from '../../services/nodemailer/sendExpiredEmail';
import { sendExpiryReminderEmail } from '../../services/nodemailer/sendExpiryReminderEmail';
import { AUTH } from '../../database/queries/auth';
import db from '../../../config/database';

export const checkExpiryAndSendEmails = async (profiles: any[]) => {

    for (const user of profiles) {
        const email = user.email;
        const fullName = `${user.firstName ?? ''} ${user.lastName ?? ''}`.trim();

        // List of keys on the user object that we want to check
        const documentsToCheck = [
            {
                name: 'License Document',
                expiryDate: user.licenseExpiredDate,
                mediaArray: user.licenseDocs,
            },
            {
                name: 'Freelancer Permit',
                expiryDate: user.freelancerLicenseNumberExpiryDate,
                mediaArray: user.freelancePermitDocs,
            },
            {
                name: 'Trade License',
                expiryDate: user.licenseExpiredDate,
                mediaArray: user.tradeLicenseDocs,
            },
            {
                name: 'Emirates ID',
                expiryDate: user.emiratesIdExpiry,
                mediaArray: user.emiratesId,
            },
            {
                name: 'Visa',
                expiryDate: user.visaExpiry,
                mediaArray: user.visa,
            },
            {
                name: 'Passport',
                expiryDate: user.passportExpiry,
                mediaArray: user.passport,
            },
            {
                name: 'Passport Document',
                expiryDate: user.passportDocExpiry,
                mediaArray: user.passportDoc,
            },
            {
                name: 'Visa Document',
                expiryDate: user.visaDocExpiry,
                mediaArray: user.visaDoc,
            },
            {
                name: 'Supporting Additional Document',
                expiryDate: user.supportingDocsDocExpiry,
                mediaArray: user.supportingDocsDoc,
            }
        ];

        for (const doc of documentsToCheck) {
            if (!doc.expiryDate) continue;

            const expiryDate = new Date(doc.expiryDate);
            if (isNaN(expiryDate.getTime())) continue;

            const daysDiff = differenceInCalendarDays(expiryDate, new Date());

            if (daysDiff === 5 || daysDiff === 1) {

                const statusNames = ["Expired Soon"];

                const expiredStatus = await db.query(
                    AUTH.SELECT_ACCOUNT_STATUS(statusNames),
                    statusNames
                );

                // Update the status of media
                if (Array.isArray(doc.mediaArray)) {
                    for (const media of doc.mediaArray) {
                        if (!media) continue; // Skip null/undefined/empty media

                        const mediaName = media.replace(/^https?:\/\/[^/]+\//, '');
                        const profileId = user?.profile_Id;

                        if (mediaName && profileId) {
                            try {
                                await updateImageStatus(mediaName, profileId, expiredStatus?.rows?.[0]?.id);
                            } catch (err) {
                                console.error(`Error updating status for media: ${media}`, err);
                            }
                        }
                    }
                }

                await sendExpiryReminderEmail(email, fullName, doc.name, expiryDate);
            } else if (isToday(expiryDate)) {
                const statusNames = ["Expired"];

                const expiredStatus = await db.query(
                    AUTH.SELECT_ACCOUNT_STATUS(statusNames),
                    statusNames
                );

                // Update the status of media
                if (Array.isArray(doc.mediaArray)) {
                    for (const media of doc.mediaArray) {
                        if (!media) continue; // Skip null/undefined/empty media

                        const mediaName = media.replace(/^https?:\/\/[^/]+\//, '');
                        const profileId = user?.profile_Id;

                        if (mediaName && profileId) {
                            try {
                                await updateImageStatus(mediaName, profileId, expiredStatus?.rows?.[0]?.id);
                            } catch (err) {
                                console.error(`Error updating status for media: ${media}`, err);
                            }
                        }
                    }
                }
                await sendExpiredEmail(email, fullName, doc.name);
            }
        }
    }
};

export const updateImageStatus = async (
    mediaName: string,
    profileId: string,
    statusId: number
) => {
    if (!mediaName || !profileId || statusId == null) {
        console.warn('Invalid input to updateImageStatus:', { mediaName, profileId, statusId });
        return;
    }

    try {
        const query = `
            UPDATE prf.images
            SET "statusId" = $1
            WHERE url = $2 AND "profileId" = $3
        `;
        await db.query(query, [statusId, mediaName, profileId]);
        console.log(`Updated status for media: ${mediaName}, profileId: ${profileId}`);
    } catch (error) {
        console.error(`Failed to update status for media: ${mediaName}, profileId: ${profileId}`, error);
    }
};