import express from "express";
import agentsRoutes from "./agent/agent.Route";
import subscriptionRoutes from "./globalConfig/subscription.Route";
import manageDocumentsRoutes from "./documents/index";
import { adminLogin, verifyAdminUserToken } from "../../controller/admin/auth";
import { adminAuthMiddleware } from "../../middleware/adminAuthMiddleware";
import { getDashboardData } from "../../controller/admin/dashboard";
import userManagementRoutes from "./userManagement/userManagement.Route";
import leadManagementRoutes from "./lead.routes";
import salesPersonsManagementRoutes from "./salesperson.routes";
import blogRoutes from "../blogs/index"; 
import blogsubscriberRoutes from "../blog-subscriber/index"; 
import settingsRoutes from "./settings/settings.routes";
import ticketsRoutes from "./tickets";
import propertiesRoutes from "./properties.routes/index";
import discountsRoutes from "./discount.routes/index";

const router = express.Router();

router.use("/agents", adminAuthMiddleware, agentsRoutes);
router.use("/manage-documents", adminAuthMiddleware, manageDocumentsRoutes);
router.get("/dashboard", adminAuthMiddleware, getDashboardData);
router.post("/login", adminLogin);
router.get("/session/:id", verifyAdminUserToken);
router.use("/subscription", adminAuthMiddleware, subscriptionRoutes);
router.use("/leads", adminAuthMiddleware, leadManagementRoutes);
router.use("/salespersons", adminAuthMiddleware, salesPersonsManagementRoutes);
router.use("/user-management", adminAuthMiddleware, userManagementRoutes);
router.use("/blogs", adminAuthMiddleware, blogRoutes);
router.use("/newsletter", adminAuthMiddleware, blogsubscriberRoutes);
router.use("/settings", adminAuthMiddleware, settingsRoutes);
router.use("/tickets", adminAuthMiddleware, ticketsRoutes);
router.use("/properties", adminAuthMiddleware, propertiesRoutes);
router.use("/discounts", adminAuthMiddleware, discountsRoutes);
 
export default router;
