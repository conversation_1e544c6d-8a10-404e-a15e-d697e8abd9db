import express from "express";
import { storageData } from "../../../utils/services/multer";
import { deleteAgentDocumentById, getAgentAccountDocumentsById, getAgentAccountsDocuments, getDocumentReasons, sendAgentAccountDocumentDetails, updateAgentAccountDocument } from "../../../controller/admin/documents-managemment";

const router = express.Router();

const upload = storageData("users");

router.get("/", getAgentAccountsDocuments);

router.get("/:id", getAgentAccountDocumentsById);

router.put("/:docId/sendToAgent", upload.none(), sendAgentAccountDocumentDetails);

router.put("/:docId/status", upload.none(), updateAgentAccountDocument);

router.get("/:docId/getReasons", getDocumentReasons);

router.delete("/:id", deleteAgentDocumentById);

export default router;
