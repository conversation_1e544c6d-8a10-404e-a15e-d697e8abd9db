import { Request, Response } from "express";

import { errorResponse, response, responseData } from "../../utils/response";
import { subscriptionCreation } from "../../utils/helperFunctions/webhook.function";
import db from "../../config/database";

export const saveWebhook = async (payload: unknown) => {
  try {
    await db.query(
      `
        INSERT INTO test_webhooks (data)
        VALUES ($1)
        RETURNING id
      `,
      [payload]
    );
  } catch (error) {
    console.error("Error saving webhook payload:", error);
  }
};

export const webhookStripeCatchSubscriptionEvent = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    let event = req.body;

    // Convert raw body to JSON for easy access
    const data = event.data.object as any;

    // Handle events
    switch (event.type) {
      case "customer.subscription.created":
        await saveWebhook(data);
        await subscriptionCreation(data);
        break;
      case "customer.subscription.updated":
        await saveWebhook(data);
        // await subscriptionCreation(data);
        break;
      case "customer.subscription.deleted":
        await saveWebhook(data);
        // await subscriptionDelete(data);
        break;
      case "invoice.payment_succeeded":
        await saveWebhook(data);
        // await subscriptionDelete(data);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
    return;
  } catch (error: any) {
    console.log(error);
    errorResponse(res, error.message);
    return;
  }
};
