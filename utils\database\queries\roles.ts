export const ROLE_QUERIES = {
  CREATE: `INSERT INTO sec.roles (name, descriptions, "createdBy") VALUES ($1, $2, $3) RETURNING *`,
  GET_BY_ID: `SELECT * FROM sec.roles WHERE id = $1`,
  GET_FIRST_ROLE: `SELECT * FROM sec.roles WHERE name = $1 ORDER BY id ASC LIMIT 1`,
  GET_ALL: `SELECT "id", "name", "descriptions" FROM sec.roles ORDER BY name ASC`,
  UPDATE: `UPDATE sec.roles SET name = $1, descriptions = $2, modifiedBy = $3, modifiedOn = CURRENT_TIMESTAMP WHERE id = $4 RETURNING *`,
  DELETE: `DELETE FROM sec.roles WHERE id = $1 RETURNING *`,
};
