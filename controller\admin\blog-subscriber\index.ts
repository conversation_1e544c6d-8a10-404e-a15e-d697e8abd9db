import { Request, Response } from "express"; 
import async<PERSON>and<PERSON> from "../../../middleware/trycatch";
import db from "../../../config/database";
import { responseData    } from "../../../utils/response";
import { validateEmail } from '../../../utils/validators'; 
import { TABLE } from "../../../utils/database/table";
import { sendNewsLetterEmail } from "../../../utils/services/nodemailer/sendNewsLetter";


export const createNewsLetterSubscriber = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { email, type } = req.body;

    if (!email) {
      return responseData(res, 400, "Email is required");
    }

    if (!validateEmail(email)) {
      return responseData(res, 400, "Invalid email format");
    }

    const client = await db.connect();

    // Check if email already exists
    const checkQuery = `SELECT * FROM ${TABLE.NEWS_LETTERS} WHERE email = $1`;
    const checkResult = await client.query(checkQuery, [email]);

    if (checkResult.rows.length > 0) {
      client.release();
      return responseData(res, 200, "You have already subscribed.");
    }

    // Insert new subscriber
    const insertQuery = `INSERT INTO ${TABLE.NEWS_LETTERS} (email , "statusId" , source) VALUES ($1 , $2 , $3) RETURNING *`;
    const insertResult = await client.query(insertQuery, [email , 1, type]);
    
    client.release();

    return responseData(res, 200, "You have successfully subscribed.");
  } catch (error) {
    console.error("Error in create News Letter Email:", error);
    return responseData(res, 500, "Failed to create News Letter Email");
  }
});


export const getAllNewsLetterSubscribers = asyncHandler(async (req: Request, res: Response) => {
  try {
    const {
      status= "",
      source,
      page = "1",
      pageSize = "10",
      search,
    } = req.query;

    const currentPage = parseInt(page as string, 10);
    const currentPageSize = parseInt(pageSize as string, 10);
    const statusIDS = parseInt(status as string, 10);

    const client = await db.connect();

    let statusId: number | null = null;
    if (status) {
      const { rows: statusRows } = await client.query(
        `SELECT id FROM ${TABLE.STATUSES} WHERE id = $1`,
        [statusIDS]
      );
      statusId = statusRows[0]?.id || null;
    }

    let query = `
      SELECT n.*, s.name AS status
      FROM ${TABLE.NEWS_LETTERS} n
      LEFT JOIN ${TABLE.STATUSES} s ON n."statusId" = s.id
      WHERE 1=1
    `;

    const queryParams: any[] = [];

    if (statusId) {
      queryParams.push(statusId);
      query += ` AND n."statusId" = $${queryParams.length}`;
    }

    if (source) {
      queryParams.push(source);
      query += ` AND n.source = $${queryParams.length}`;
    }

    if (search) {
      queryParams.push(`%${search}%`);
      query += ` AND n.email ILIKE $${queryParams.length}`;
    }

    const offset = (currentPage - 1) * currentPageSize;
    queryParams.push(offset);
    queryParams.push(currentPageSize);
    query += ` ORDER BY n.created_at DESC OFFSET $${queryParams.length - 1} LIMIT $${queryParams.length}`;

    const result = await client.query(query, queryParams);

    // Count query
    let countQuery = `
      SELECT COUNT(*) FROM ${TABLE.NEWS_LETTERS} n
      WHERE 1=1
    `;
    const countParams: any[] = [];

    if (statusId) {
      countParams.push(statusId);
      countQuery += ` AND n."statusId" = $${countParams.length}`;
    }

    if (source) {
      countParams.push(source);
      countQuery += ` AND n.source = $${countParams.length}`;
    }

    if (search) {
      countParams.push(`%${search}%`);
      countQuery += ` AND n.email ILIKE $${countParams.length}`;
    }

    const countResult = await client.query(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count, 10);

    client.release();

    return responseData(res, 200, "Subscribers fetched successfully", {
      leads: result.rows,
      leadsCounts: null,
      pagination: {
        page: currentPage,
        pageSize: currentPageSize,
        total: totalCount,
        returned: result.rows.length,
      },
    });

  } catch (error) {
    console.error("Error in getAllNewsLetterSubscribers:", error);
    return responseData(res, 500, "Failed to get subscribers");
  }
});



export const deleteNewsLetterSubscriber = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params; 
    const client = await db.connect();
 
    const checkQuery = `DELETE FROM ${TABLE.NEWS_LETTERS} WHERE id = $1`;
    const checkResult = await client.query(checkQuery, [id]); 
    client.release();
    return responseData(res, 200, "Subscriber Deleted Successfully",checkResult.rows);

  } catch (error) {
    console.error("Error in delete News Letter Email:", error);
    return responseData(res, 500, "Failed to  delete News Letter Email");
  }
});

export const updateNewsLetterSubscriber = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id ,statusId} = req.body; 
    const client = await db.connect();
 
    const checkQuery = `UPDATE ${TABLE.NEWS_LETTERS} SET "statusId" = $2 WHERE id = $1`;
    const checkResult = await client.query(checkQuery, [id,statusId]); 
    client.release();
    return responseData(res, 200, "Subscriber Updated Successfully",checkResult.rows); 

  } catch (error) {
    console.error("Error in update News Letter Email:", error);
    return responseData(res, 500, "Failed to update News Letter Email");
  }
});
 
export const sendBulkEmailHandler = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { subject, message, isLead } = req.body;
    const to = JSON.parse(req.body?.to || []);  
    // Validation
    if (!subject || !message) {
      return responseData(res, 400, "Subject and message are required");
    } 
    // Send email to all recipients in BCC
    await sendNewsLetterEmail(subject, message,  to, res); 
 
  } catch (error) {
    console.error("Error in sending News Letter Email:", error);
    return responseData(res, 500, "Failed to send News Letter Email");
  }
});
export const getAllSubscribersCount = asyncHandler(async (req: Request, res: Response) => {
  try {
    const client = await db.connect();

    const query = `
      SELECT 
        COUNT(*) FILTER (WHERE s.id = 1) AS subscribedCount,
        COUNT(*) FILTER (WHERE s.id =  29) AS unsubscribedCount,
        COUNT(*) FILTER (WHERE s.id = 28) AS otherCount,
        COUNT(*) AS totalCount
      FROM ${TABLE.NEWS_LETTERS} n
      LEFT JOIN ${TABLE.STATUSES} s ON n."statusId" = s.id
    `;

    const result = await client.query(query);
    client.release();

    return responseData(res, 200, "Subscriber counts by status fetched successfully", result.rows[0]);
  } catch (error) {
    console.error("Error in getAllSubscribersCount:", error);
    return responseData(res, 500, "Failed to get subscriber counts");
  }
});


