import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
    CREATE OR REPLACE FUNCTION look.sp_salesperson (
      p_fnid             integer,
      p_id               integer DEFAULT NULL,
      p_full_name        text DEFAULT NULL,
      p_email            text DEFAULT NULL,
      p_phone            text DEFAULT NULL,
      p_referral_id      text DEFAULT NULL,
      p_commission_rate  numeric DEFAULT NULL,
      p_statusid         integer DEFAULT NULL,
      p_notes            text DEFAULT NULL,
      p_sortby           text DEFAULT 'id',
      p_filter_column    text DEFAULT NULL,
      p_filter_value     text DEFAULT NULL,
      p_search           text DEFAULT NULL,
      p_page_no          integer DEFAULT 1,
      p_page_size        integer DEFAULT 10
    ) RETURNS jsonb
    LANGUAGE plpgsql
    AS $function$
    DECLARE
        v_data        jsonb;
        v_title       text;
        v_result      int;
        v_type        text;
        v_message     text;
        v_start_date  date;
        v_end_date    date;
    BEGIN
        -- 0 ► single record by ID
        IF p_fnid = 0 THEN
            SELECT to_jsonb(row) INTO v_data
            FROM (
              SELECT 
                t.*, 
                s.name AS status_name
              FROM look.salespersons t
              LEFT JOIN look.status s ON s.id = t."statusId"
              WHERE t.id = p_id
            ) row;

            v_title   := 'Salesperson - Single Record';
            v_result  := 1;
            v_type    := 'success';
            v_message := 'Salesperson selected successfully.';

        -- 1 ► list / filtered / paginated
        ELSIF p_fnid = 1 THEN
            IF p_filter_column = 'created_at' AND p_filter_value IS NOT NULL AND LENGTH(TRIM(p_filter_value)) > 0 THEN
                v_start_date := TO_DATE(p_filter_value || '-01', 'YYYY-MM-DD');
                v_end_date := (v_start_date + INTERVAL '1 MONTH - 1 DAY')::DATE;
            END IF;

            WITH filtered AS (
                SELECT 
                  t.*, 
                  s.name AS status_name
                FROM look.salespersons t
                LEFT JOIN look.status s ON s.id = t."statusId"
                WHERE 
                  (
                    -- Filter by created_at
                    (p_filter_column = 'created_at' AND (
                      p_filter_value IS NULL OR TRIM(p_filter_value) = ''
                      OR (t.created_at::DATE BETWEEN v_start_date AND v_end_date)
                    ))
                    OR
                    -- Filter by other dynamic columns
                    (
                      p_filter_column IS NOT NULL AND p_filter_column <> 'created_at' AND
                      (
                        CASE 
                          WHEN p_filter_column = 'full_name' THEN t.full_name
                          WHEN p_filter_column = 'email' THEN t.email
                          WHEN p_filter_column = 'referral_id' THEN t.referral_id
                          WHEN p_filter_column = 'statusId' THEN t."statusId"::text
                          ELSE NULL
                        END
                      ) ILIKE '%' || p_filter_value || '%'
                    )
                    OR p_filter_column IS NULL
                  )
                  AND (
                    -- Apply additional filter by statusId if provided
                    p_statusid IS NULL OR t."statusId" = p_statusid
                  )
                  AND (
                    -- Search
                    p_search IS NULL OR (
                      t.full_name ILIKE '%' || p_search || '%' OR
                      t.email ILIKE '%' || p_search || '%' OR
                      t.referral_id ILIKE '%' || p_search || '%'
                    )
                  )
            ),
            sorted AS (
                SELECT * FROM filtered
                ORDER BY 
                CASE 
                    WHEN p_sortby = 'full_name' THEN full_name
                    WHEN p_sortby = 'email' THEN email
                    WHEN p_sortby = 'referral_id' THEN referral_id
                    WHEN p_sortby = 'created_at' THEN created_at::text
                    ELSE id::text
                END
            ),
            counted AS (
                SELECT COUNT(*) AS total_count FROM sorted
            ),
            paged AS (
                SELECT * FROM sorted
                OFFSET ((p_page_no - 1) * p_page_size)
                LIMIT p_page_size
            ),
            final_data AS (
                SELECT 
                  jsonb_agg(to_jsonb(p)) AS records,
                  (SELECT total_count FROM counted) AS total_count,
                  COUNT(*) AS returned_count
                FROM paged p
            )
            SELECT jsonb_build_object(
                'records', COALESCE(records, '[]'::jsonb),
                'pagination', jsonb_build_object(
                    'page', p_page_no,
                    'pageSize', p_page_size,
                    'returned', returned_count,
                    'total', total_count
                )
            ) INTO v_data
            FROM final_data;

            v_title   := 'Salesperson - Filtered List';
            v_result  := 1;
            v_type    := 'success';
            v_message := 'Filtered salespersons retrieved successfully.';

        -- 2 ► insert / update
        ELSIF p_fnid = 2 THEN
            IF p_id IS NULL THEN
                INSERT INTO look.salespersons (
                    full_name, email, phone, referral_id,
                    commission_rate, "statusId", notes, created_at, updated_at
                ) VALUES (
                    p_full_name, p_email, p_phone, p_referral_id,
                    p_commission_rate, p_statusid, p_notes, NOW(), NOW()
                )
                RETURNING to_jsonb(look.salespersons.*) INTO v_data;

                v_title   := 'Salesperson - Insert';
                v_result  := 1;
                v_type    := 'success';
                v_message := 'Salesperson inserted successfully.';
            ELSE
                UPDATE look.salespersons
                SET
                    full_name = p_full_name,
                    email = p_email,
                    phone = p_phone,
                    referral_id = p_referral_id,
                    commission_rate = p_commission_rate,
                    "statusId" = p_statusid,
                    notes = p_notes,
                    updated_at = NOW()
                WHERE  id = p_id
                RETURNING to_jsonb(look.salespersons.*) INTO v_data;

                v_title   := 'Salesperson - Update';
                v_result  := 2;
                v_type    := 'success';
                v_message := 'Salesperson updated successfully.';
            END IF;

        -- 3 ► delete
        ELSIF p_fnid = 3 THEN
            DELETE FROM look.salespersons
            WHERE  id = p_id
            RETURNING to_jsonb(look.salespersons.*) INTO v_data;

            v_title   := 'Salesperson - Delete';
            v_result  := 3;
            v_type    := 'success';
            v_message := 'Salesperson deleted successfully.';

        -- 4 ► status update
        ELSIF p_fnid = 4 THEN
            UPDATE look.salespersons
            SET    "statusId" = p_statusid,
                   updated_at = NOW()
            WHERE  id = p_id
            RETURNING to_jsonb(look.salespersons.*) INTO v_data;

            v_title   := 'Salesperson - Status Update';
            v_result  := 2;
            v_type    := 'success';
            v_message := 'Salesperson status updated successfully.';

        -- invalid
        ELSE
            v_title   := 'Salesperson - Error';
            v_result  := -1;
            v_type    := 'error';
            v_message := 'Invalid Function ID.';
            v_data    := NULL;
        END IF;

        RETURN jsonb_build_object(
            'title',   v_title,
            'result',  v_result,
            'type',    v_type,
            'message', v_message,
            'data',    COALESCE(v_data, 'null'::jsonb)
        );
    END;
    $function$;
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`
    DROP FUNCTION IF EXISTS look.sp_salesperson (
      integer,
      integer,
      text,
      text,
      text,
      text,
      numeric,
      integer,
      text,
      text,
      text,
      text,
      text,
      integer,
      integer
    );
  `);
}
