import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  await knex("list.currency")
    .insert([
      { code: "USD", name: "US Dollar", symbol: "$" },
      { code: "EUR", name: "Euro", symbol: "€" },
      { code: "GBP", name: "British Pound", symbol: "£" },
      { code: "JPY", name: "Japanese Yen", symbol: "¥" },
      { code: "INR", name: "Indian Rupee", symbol: "₹" },
      { code: "AED", name: "UAE Dirham", symbol: "د.إ" },
      { code: "CAD", name: "Canadian Dollar", symbol: "C$" },
      { code: "AUD", name: "Australian Dollar", symbol: "A$" },
      { code: "CHF", name: "Swiss Franc", symbol: "CHF" },
      { code: "CNY", name: "Chinese Yuan", symbol: "¥" },
      { code: "SAR", name: "Saudi Riyal", symbol: "﷼" },
      { code: "ZAR", name: "South African Rand", symbol: "R" },
    ])
    .onConflict("code")
    .ignore(); // Skips existing codes
}
