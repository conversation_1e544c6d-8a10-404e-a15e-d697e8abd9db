import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import { createDiscount, deleteDiscount, getAllDiscounts, getDiscountById, getFilteredDiscounts, updateDiscount, updateDiscountStatus } from "../../../controller/admin/discount.controller";

const router = Router();
const upload = storageData("documents");

// GET all properties with pagination and filters
router.get("/", getFilteredDiscounts);

router.get("/all", getAllDiscounts);

// GET property by ID
router.get("/:id", getDiscountById);

// PUT update property status
router.put("/:id/status", upload.none(), updateDiscountStatus);

// DELETE property
router.delete("/:id", deleteDiscount);

// create a note for a property
router.post("/", upload.none(), createDiscount);

// Update a discount
router.put("/:id", upload.none(), updateDiscount);

export default router;
