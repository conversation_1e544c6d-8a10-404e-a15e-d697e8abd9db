import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').alterTable('leads', (table) => {
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at', { useTz: true }).nullable();
    table.integer('deleted_by').nullable();
    
    // Add indexes for better performance on soft delete queries
    table.index('is_deleted', 'idx_leads_is_deleted');
    table.index('deleted_at', 'idx_leads_deleted_at');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').alterTable('leads', (table) => {
    table.dropIndex('deleted_at', 'idx_leads_deleted_at');
    table.dropIndex('is_deleted', 'idx_leads_is_deleted');
    
    table.dropColumn('deleted_by');
    table.dropColumn('deleted_at');
    table.dropColumn('is_deleted');
  });
} 