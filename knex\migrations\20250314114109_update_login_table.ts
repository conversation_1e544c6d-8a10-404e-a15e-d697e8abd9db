import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("sec.login", (table) => {
    table.string("accountType").nullable();
    table.integer("otp").nullable();
    table.timestamp("expireOn").nullable();
    table.integer("createdBy").nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("sec.login", (table) => {
    table.dropColumn("accountType");
    table.dropColumn("otp");
    table.dropColumn("expireOn");
    table.integer("createdBy").notNullable().alter();
  });
}
