import cron from 'node-cron';
import db from '../../config/database';
import { AGENTS } from '../../utils/database/queries/agentDetails';
import { checkExpiryAndSendEmails } from '../../utils/helperFunctions/check.documents.expiry';

/**
 * Starts a cron job to check for document expiry and send notifications.
 * 
 * The cron job is scheduled to run daily at 12:00 PM (noon).
 * 
 * It performs the following tasks:
 * - Logs the start of the document expiry check.
 * - Queries the database to retrieve document details for agents.
 * - Checks for expired documents and sends email notifications as needed.
 * - Logs the completion of the document expiry notifications.
 */
export const startDocumentExpiryCron = () => {
  cron.schedule('0 12 * * *', async () => {
    // cron.schedule('*/5 * * * * *', async () => {
    console.log('[CRON] Running document expiry check...');

    const { rows } = await db.query(AGENTS.GET_DOCUMENT_DETAILS_BY_PROFILE_ID);
    await checkExpiryAndSendEmails(rows);
    console.log('[CRON] Completed document expiry notifications.');
  });
};
