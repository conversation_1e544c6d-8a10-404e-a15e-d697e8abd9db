import { Request, Response } from "express";
import as<PERSON><PERSON><PERSON><PERSON> from "../../middleware/trycatch";
import { errorResponse, responseData } from "../../utils/response";
import { AUTH } from "../../utils/database/queries/auth";
import db from "../../config/database";
import { REVIEWS } from "../../utils/database/queries/reviews";

// Create a new review
export const createReview = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      console.log("Create review request body:", req.body);
      console.log("User from middleware:", req.user);
      
      const { revieweeId, reviewText, rating } = req.body;
      const reviewerId = req.user?.id;

      // Check if user is authenticated
      if (!reviewerId) {
        return errorResponse(res, "Authentication required to create a review");
      }

      // Validation
      if (!revieweeId || !reviewText || !rating) {
        return errorResponse(res, "Reviewee ID, review text, and rating are required");
      }

      if (rating < 1 || rating > 5) {
        return errorResponse(res, "Rating must be between 1 and 5");
      }

      if (reviewerId === revieweeId) {
        return errorResponse(res, "You cannot review yourself");
      }

      // Check if reviewee exists
      const revieweeExists = await db.query(AUTH.SELECT_BY_ID, [revieweeId]);
      if (!revieweeExists.rows.length) {
        return errorResponse(res, "Reviewee not found");
      }

      const reviewee = revieweeExists.rows[0];
      
      // Check if reviewee is an agent or agency
      if (!['Individual', 'Company/Agency/PropertyDeveloper'].includes(reviewee.accountType)) {
        return errorResponse(res, "You can only review agents or agencies");
      }

      // Check if user already reviewed this profile
      const existingReview = await db.query(REVIEWS.CHECK_EXISTING_REVIEW, [
        reviewerId,
        revieweeId,
      ]);

      if (existingReview.rows.length > 0) {
        return errorResponse(res, "You have already reviewed this profile");
      }

      // Get reviewer's login ID for createdBy
      const reviewerLogin = await db.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [reviewerId]
      );

      if (!reviewerLogin.rows.length) {
        return errorResponse(res, "Invalid reviewer");
      }

      // Get pending status ID from database
      const statusNames = ["Pending"];
      const pendingStatus = await db.query(AUTH.SELECT_ACCOUNT_STATUS(statusNames), statusNames);
      
      if (!pendingStatus.rows.length) {
        return errorResponse(res, "Pending status not found in database");
      }

      const pendingStatusId = pendingStatus.rows[0].id;

      // Create the review with pending status from database
      const result = await db.query(REVIEWS.CREATE_REVIEW, [
        reviewerId,
        revieweeId,
        reviewText,
        rating,
        pendingStatusId, // Dynamic status ID from database
        reviewerLogin.rows[0].id,
      ]);

      return responseData(
        res,
        201,
        "Review submitted successfully. It will be visible after admin approval.",
        result.rows[0]
      );
    } catch (error) {
      console.error("Error creating review:", error);
      return errorResponse(res, "Failed to create review");
    }
  }
);

// Get approved reviews for a specific profile
export const getProfileReviews = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { profileId } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Check if profile exists
      const profileExists = await db.query(AUTH.SELECT_BY_ID, [profileId]);
      if (!profileExists.rows.length) {
        return errorResponse(res, "Profile not found");
      }

      // Get approved reviews
      const reviews = await db.query(REVIEWS.GET_APPROVED_REVIEWS_FOR_PROFILE, [
        profileId,
        limit,
        offset,
      ]);

      // Get rating statistics
      const stats = await db.query(REVIEWS.GET_PROFILE_RATING_STATS, [profileId]);

      return responseData(res, 200, "Reviews retrieved successfully", {
        reviews: reviews.rows,
        stats: stats.rows[0],
        pagination: {
          page,
          limit,
          total: reviews.rows.length,
        },
      });
    } catch (error) {
      console.error("Error getting profile reviews:", error);
      return errorResponse(res, "Failed to get reviews");
    }
  }
);

// Get profile rating statistics
export const getProfileRatingStats = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { profileId } = req.params;

      // Check if profile exists
      const profileExists = await db.query(AUTH.SELECT_BY_ID, [profileId]);
      if (!profileExists.rows.length) {
        return errorResponse(res, "Profile not found");
      }

      // Get rating statistics
      const stats = await db.query(REVIEWS.GET_PROFILE_RATING_STATS, [profileId]);

      return responseData(res, 200, "Rating statistics retrieved successfully", {
        stats: stats.rows[0],
      });
    } catch (error) {
      console.error("Error getting rating stats:", error);
      return errorResponse(res, "Failed to get rating statistics");
    }
  }
);

// Get user's own reviews (reviews they have written)
export const getUserReviews = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      const reviews = await db.query(REVIEWS.GET_USER_REVIEWS, [userId, limit, offset]);

      return responseData(res, 200, "User reviews retrieved successfully", {
        reviews: reviews.rows,
        pagination: {
          page,
          limit,
          total: reviews.rows.length,
        },
      });
    } catch (error) {
      console.error("Error getting user reviews:", error);
      return errorResponse(res, "Failed to get user reviews");
    }
  }
);
