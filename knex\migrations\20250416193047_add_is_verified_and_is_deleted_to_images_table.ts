import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable("prf.images", (table) => {
    table.boolean("isVerified").defaultTo(false);
    table.boolean("isDeleted").defaultTo(false);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable("prf.images", (table) => {
    table.dropColumn("isVerified");
    table.dropColumn("isDeleted");
  });
}
