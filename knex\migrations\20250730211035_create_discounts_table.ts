import type { Knex } from "knex";

exports.up = function (knex: Knex) {
  return knex.schema.createTable("web.discounts", function (table) {
    table.increments("id").primary(); // Primary key
    table.string("name").notNullable(); // Discount name
    table.text("description"); // Discount description
    table.string("type").notNullable().defaultTo('percentage'); // e.g. 'percentage' or 'fixed'
    table.decimal("value", 10, 2).notNullable(); // Discount value
    table.string("code").unique().nullable(); // Unique discount code
    table.string("stripeDiscountId").nullable(); // Start date of the discount
    table.integer("statusId").notNullable();
    table.timestamps(true, true);
  });
};

exports.down = function (knex: Knex) {
  return knex.schema.dropTable("web.discounts");
};
