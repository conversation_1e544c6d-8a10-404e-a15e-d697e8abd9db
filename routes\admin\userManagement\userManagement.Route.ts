import express from "express";
import {
  getUserDetails,
  downloadUsersCSV,
  addNewUser,
  sendBulkEmailHandler,
  sendIndividualEmailHandler,
  upload,
  deactivateUser,
  resetUserPassword,
  addUserNote,
  getUserNotes,
  activateUser,
} from "../../../controller/admin/user-management";

const router = express.Router();

router.get("/users", getUserDetails);
router.get("/users/download-csv", downloadUsersCSV);
router.post("/users/add-user", addNewUser);
router.post(
  "/users/bulk-email",
  upload.array("attachments", 10),
  sendBulkEmailHandler
);
router.post(
  "/users/individual-email",
  upload.array("attachments", 10),
  sendIndividualEmailHandler
);
router.post("/users/:id/notes", addUserNote);
router.get("/users/:profileId/notes", getUserNotes);
router.patch("/users/:id/deactivate", deactivateUser);
router.patch("/users/:id/activate", activateUser);
router.patch("/users/:id/reset-password", resetUserPassword);

export default router;
