import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("blogs", (table) => {
    table.increments("id").primary();
    table.bigInteger('profileId').nullable();
    table.string("author").nullable();
    table.text("title").notNullable();
    table.text("content").notNullable();
    table.string("category").nullable();
    table.text("excerpt").nullable();  
    table.string("featuredImage").nullable();
    table.integer("statusId").nullable();
    table.timestamp("publishDate").nullable();  
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("blogs");
}
