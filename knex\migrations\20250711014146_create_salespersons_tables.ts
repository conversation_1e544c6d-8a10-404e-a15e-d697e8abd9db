import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("look.salespersons", (table) => {
    table.increments("id").primary();
    table.string("full_name").notNullable();
    table.string("email").notNullable().unique();
    table.string("phone").nullable();
    table.string("referral_id").notNullable().unique(); // e.g., REF001
    table.decimal("commission_rate", 5, 2).notNullable(); // % rate
    table.integer("statusId").nullable();
    table.text("notes").nullable();
    table.timestamps(true, true); // created_at, updated_at
  });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.dropTableIfExists("look.salespersons");
}
