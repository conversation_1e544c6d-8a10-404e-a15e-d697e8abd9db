import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.string("positionOther").nullable();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.dropColumn("positionOther");
    });
}