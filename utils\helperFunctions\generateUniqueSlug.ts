import slugify from "slugify";
import db from "../../config/database";

/**
 * Generate a unique slug based on value and ensure it's unique in a given table.
 *
 * @param value The text to generate the slug from (e.g., name/title).
 * @param table The database table to check for uniqueness.
 * @param column The column name to check for duplicates (defaults to "slug").
 * @param excludeId Optional ID to exclude from duplicate check (for updates).
 * @returns Unique slug string.
 */
export const generateUniqueSlug = async (
  value: string,
  table: string,
  column: string = "slug",
  excludeId?: number
): Promise<string> => {
  const baseSlug = slugify(value, { lower: true, strict: true });
  let slug = baseSlug;
  let suffix = 1;

  // Loop until a unique slug is found
  while (true) {
    const query = excludeId
      ? `SELECT 1 FROM ${table} WHERE "${column}" = $1 AND id != $2 LIMIT 1`
      : `SELECT 1 FROM ${table} WHERE "${column}" = $1 LIMIT 1`;

    const values = excludeId ? [slug, excludeId] : [slug];
    const { rows } = await db.query(query, values);

    if (rows.length === 0) break;
    slug = `${baseSlug}-${suffix++}`;
  }

  return slug;
};
