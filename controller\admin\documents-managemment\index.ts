import { Request, Response } from "express";
import { AUTH } from "../../../utils/database/queries/auth";
import db from "../../../config/database";
import {
  errorCatchResponse,
  errorResponse,
  response,
  responseData,
} from "../../../utils/response";
import asyncHandler from "../../../middleware/trycatch";
import { AGENTS } from "../../../utils/database/queries/agentDetails";
import { DOCUMENTS } from "../../../utils/database/queries/documents";
import { sendDocumentsStatusUpdatedEmail } from "../../../utils/services/nodemailer/sendDocumentsStatusUpdatedEmail";
import { TABLE } from "../../../utils/database/table";
import { sendDocumentTypeStatusEmail } from "../../../utils/services/nodemailer/sendDocumentTypeStatusEmail";
import { deleteFileFromS3 } from "../../../utils/services/s3-bucket";

export const getAgentAccountsDocuments = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { status } = req.query;

      // Convert to string[]
      const statusNames: string[] = Array.isArray(status)
        ? status.filter((s): s is string => typeof s === "string") // filters out undefined and ParsedQs
        : typeof status === "string"
        ? [status]
        : [];

      const selectedStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      let rows, docuCountsStatusWise;

      if (selectedStatus.rows.length > 0) {
        const statusId = selectedStatus.rows[0].id;
        const result = await db.query(
          DOCUMENTS.GET_AGENTS_AND_AGENCIES_DOCUMENTS_WITH_STATUS,
          [statusId]
        );
        rows = result.rows;
        const { rows: rowDocuCountsStatusWise } = await db.query(
          DOCUMENTS.GET_DOCUMENTS_COUNTS_WITH_STATUS,
          [statusId]
        );

        docuCountsStatusWise = rowDocuCountsStatusWise;
      } else {
        const result = await db.query(
          DOCUMENTS.GET_AGENTS_AND_AGENCIES_DOCUMENTS
        );
        rows = result.rows;
        const { rows: rowDocuCountsStatusWise } = await db.query(
          DOCUMENTS.GET_DOCUMENTS_COUNTS
        );
        docuCountsStatusWise = rowDocuCountsStatusWise;
      }

      const enhancedRows = rows.map((user) => {
        const final_images = Array.isArray(user.final_images)
          ? user.final_images.map((img: any) => {
              const fullUrl = `https://fmafiles-main.s3.me-central-1.amazonaws.com/${img.url}`;

              const documentsToCheck = [
                {
                  expiryDate: user.licenseExpiredDate,
                  mediaArray: user.licenseDocs,
                },
                {
                  expiryDate: user.freelancerLicenseNumberExpiryDate,
                  mediaArray: user.freelancePermitDocs,
                },
                {
                  expiryDate: user.licenseExpiredDate,
                  mediaArray: user.tradeLicenseDocs,
                },
                {
                  expiryDate: user.emiratesIdExpiry,
                  mediaArray: user.emiratesId,
                },
                {
                  expiryDate: user.visaExpiry,
                  mediaArray: user.visa,
                },
                {
                  expiryDate: user.passportExpiry,
                  mediaArray: user.passport,
                },
                {
                  expiryDate: user.emiratesIdExpiry,
                  mediaArray: user.emiratesId,
                },
                {
                  expiryDate: user.passportDocExpiry,
                  mediaArray: user.passportDoc,
                },
                {
                  expiryDate: user.visaDocExpiry,
                  mediaArray: user.visaDoc,
                },
                {
                  expiryDate: user.supportingDocsDocExpiry,
                  mediaArray: user.supportingDocsDoc,
                },
              ];

              const matched = documentsToCheck.find(
                (doc) =>
                  Array.isArray(doc.mediaArray) &&
                  doc.mediaArray.includes(fullUrl)
              );

              return {
                ...img,
                expiryDate: matched?.expiryDate || null,
              };
            })
          : [];

        return {
          ...user,
          final_images,
        };
      });

      return responseData(res, 200, "Data retrieved successfully", {
        docuCountsStatusWise,
        documents: enhancedRows,
      });
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getAgentAccountDocumentsById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const { rows } = await db.query(AUTH.SELECT_BY_ID, [id]);

      if (!rows.length) {
        return errorResponse(res, "No record found");
      }

      if (rows[0].accountType === "Individual") {
        const individualAgentData = await db.query(
          AGENTS.GET_INDIVIDUAL_AGENYT_DETAILS_BY_ID,
          [id]
        );

        if (!individualAgentData.rows.length) {
          return errorResponse(res, "No record found");
        }

        const individualAgent = individualAgentData.rows[0];

        // Append base URL to files
        // individualAgent.profilePhotos = appendBaseUrlToFiles(individualAgent.profilePhotos);
        // individualAgent.emiratesId = appendBaseUrlToFiles(individualAgent.emiratesId);
        // individualAgent.passport = appendBaseUrlToFiles(individualAgent.passport);
        // individualAgent.employmentProof = appendBaseUrlToFiles(individualAgent.employmentProof);
        // individualAgent.freelancePermitDocs = appendBaseUrlToFiles(individualAgent.freelancePermitDocs);
        // individualAgent.licenseDoc = appendBaseUrlToFiles(individualAgent.licenseDoc);
        // individualAgent.licenseDocs = appendBaseUrlToFiles(individualAgent.licenseDocs);
        // individualAgent.visaDoc = appendBaseUrlToFiles(individualAgent.visaDoc);
        // individualAgent.visa = appendBaseUrlToFiles(individualAgent.visa);

        delete individualAgent.passwordHash;

        return responseData(
          res,
          200,
          "Data retrieved successfully",
          individualAgent
        );
      } else if (rows[0].accountType === "Company/Agency/PropertyDeveloper") {
        const companyAgentData = await db.query(
          AGENTS.GET_COMPANY_AGENT_DETAILS_BY_ID,
          [id]
        );

        if (!companyAgentData.rows.length) {
          return errorResponse(res, "No record found");
        }

        const companyAgent = companyAgentData.rows[0];

        // Append base URL to files
        // companyAgent.profilePhotos = appendBaseUrlToFiles(companyAgent.profilePhotos);
        // companyAgent.tradeLicenseDocs = appendBaseUrlToFiles(companyAgent.tradeLicenseDocs);
        // companyAgent.personalIdDoc = appendBaseUrlToFiles(companyAgent.personalIdDoc);
        // companyAgent.passportDoc = appendBaseUrlToFiles(companyAgent.passportDoc);
        // companyAgent.visaDoc = appendBaseUrlToFiles(companyAgent.visaDoc);
        // companyAgent.supportingDocsDoc = appendBaseUrlToFiles(companyAgent.supportingDocsDoc);
        // companyAgent.licenseDocs = appendBaseUrlToFiles(companyAgent.licenseDocs);
        // companyAgent.emiratesId = appendBaseUrlToFiles(companyAgent.emiratesId);

        delete companyAgent.passwordHash;

        return responseData(
          res,
          200,
          "Data retrieved successfully",
          companyAgent
        );
      }
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const updateAgentAccountDocument = asyncHandler(
  async (req: Request, res: Response) => {
    const { docId } = req.params;
    const { status, rejectionReason, isPrivate } = req.body;

    if (!docId) {
      return errorResponse(res, "Missing Document ID or status.");
    }

    const client = await db.connect();

    try {
      await client.query("BEGIN");

      // Step 1: Fetch current statusId of the document
      const currentStatusQuery = `
        SELECT "statusId", "profileId"
        FROM prf.images
        WHERE id = $1;
      `;

      const currentStatusResult = await client.query(currentStatusQuery, [
        docId,
      ]);

      if (currentStatusResult.rows.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent document not found.");
      }

      const oldStatusId = currentStatusResult.rows[0].statusId;
      const profileId = currentStatusResult.rows[0].profileId;

      let newStatusId = oldStatusId;

      // Step 2: Fetch the new statusId from the status table
      if (status) {
        const statusNames = [status];
        const pendingStatus = await client.query(
          AUTH.SELECT_ACCOUNT_STATUS(statusNames),
          statusNames
        );

        if (pendingStatus.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Status not found.");
        }

        newStatusId = pendingStatus.rows[0].id;
      }

      // Step 3: Update the document status
      const updateQuery = `
        UPDATE prf.images
        SET "statusId" = $1,
            "reason" = $2
        WHERE id = $3
        RETURNING *;
      `;

      const updateResult = await client.query(updateQuery, [
        newStatusId,
        rejectionReason || null,
        docId,
      ]);

      if (updateResult.rows.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent document not found.");
      }

      let normalizedIsPrivate = false;
      if (typeof isPrivate === "string") {
        normalizedIsPrivate = isPrivate.toLowerCase() === "true";
      } else if (typeof isPrivate === "boolean") {
        normalizedIsPrivate = isPrivate;
      }

      // Step 4: If status is not "verified" or isPrivate is true, insert a reason record
      if (status && status.trim().toLowerCase() != "verified" || isPrivate.toLowerCase() === "true") {
        // Step 4: Insert reason record with oldStatusId and newStatusId
        const insertReasonQuery = `
          INSERT INTO prf.reasons (
            "profileId",
            "reason",
            "createdBy",
            "oldStatusId",
            "newStatusId",
            "isPrivate"
          )
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *;`;

        const userId = req.user.id;

        await client.query(insertReasonQuery, [
          profileId,
          rejectionReason || null,
          userId,
          oldStatusId,
          newStatusId,
          normalizedIsPrivate,
        ]);
      }

      // Step 5: Fetch agent profile
      const { rows } = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      if (!rows.length) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile not found.");
      }

      const user = rows[0];

      const fullName = `${user.firstName} ${user.middleName} ${user.lastName}`;
      const email = user.email;

      if (
        status &&
        (status.trim().toLowerCase() == "verified" ||
          status.trim().toLowerCase() == "rejected")
      ) {
        await sendDocumentsStatusUpdatedEmail(
          fullName,
          email,
          status,
          rejectionReason,
          res
        );
      }

      await client.query("COMMIT");

      return response(res, 200, "Status updated successfully.");
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error updating statusId:", error);
      res.status(500).json({ message: "Server error updating statusId." });
    } finally {
      client.release();
    }
  }
);

export const sendAgentAccountDocumentDetails = asyncHandler(
  async (req: Request, res: Response) => {
    const { docId } = req.params;
    const { status, documentType } = req.body;

    if (!docId || !status) {
      return errorResponse(res, "Missing Document ID or status.");
    }

    try {
      const SELECT_IMAGE_BY_ID = `
      SELECT *
      FROM prf.images
      WHERE id = $1
      LIMIT 1;
    `;

      const { rows } = await db.query(SELECT_IMAGE_BY_ID, [docId]);

      if (!rows.length) {
        return errorResponse(res, "Agent profile not found.");
      }

      const { rows: resultProfile } = await db.query(AUTH.SELECT_BY_ID, [
        rows[0]?.profileId,
      ]);

      const user = resultProfile[0];
      const fullName = `${user.firstName} ${user.middleName} ${user.lastName}`;
      const email = user.email;
      await sendDocumentTypeStatusEmail(
        fullName,
        email,
        status,
        documentType,
        res
      );

      return response(res, 200, "Send to agent successfully.");
    } catch (error) {
      console.error("Send to agent:", error);
      res.status(500).json({ message: "Send to agent." });
    }
  }
);

export const getDocumentReasons = asyncHandler(
  async (req: Request, res: Response) => {
    const { docId } = req.params;

    if (!docId) {
      return errorResponse(res, "Missing Document ID.");
    }

    const client = await db.connect();

    try {
      // 1️⃣ Fetch profileId from the images table
      const getProfileIdQuery = `
      SELECT "profileId"
      FROM prf.images
      WHERE id = $1
      LIMIT 1;
    `;

      const { rows: profileRows } = await client.query(getProfileIdQuery, [
        docId,
      ]);

      if (profileRows.length === 0) {
        client.release();
        return errorResponse(res, "Document not found.");
      }

      const profileId = profileRows[0].profileId;

      // 2️⃣ Fetch all reasons for that profile, joined with users for createdBy
      const getReasonsQuery = `
      SELECT 
        r.id,
        r."profileId",
        r.reason,
        r."isPrivate",
        r."createdBy",
        r."created_at",
        u."firstName",
        u."lastName",
        u.email,
        old_status."name" AS "oldStatusName",
        new_status."name" AS "newStatusName"
      FROM prf.reasons r
      INNER JOIN ${TABLE.PROFILE_TABLE} u ON r."createdBy" = u.id
      LEFT JOIN ${TABLE.STATUS} old_status ON r."oldStatusId" = old_status.id
      LEFT JOIN ${TABLE.STATUS} new_status ON r."newStatusId" = new_status.id
      WHERE r."profileId" = $1
      ORDER BY r."created_at" DESC;
    `;

      const { rows: reasons } = await client.query(getReasonsQuery, [
        profileId,
      ]);

      client.release();

      return responseData(res, 200, "Reasons retrieved successfully", {
        reasons,
      });
    } catch (error) {
      console.error("Error fetching document reasons:", error);
      client.release();
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const deleteAgentDocumentById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      if (!id) {
        return errorResponse(res, "Document ID is required");
      }

      // 1. Get document record by ID
      const { rows } = await db.query(DOCUMENTS.GET_DOCUMENTS_BY_ID, [
        id,
        false,
      ]);

      if (!rows.length) {
        return errorResponse(res, "Document not found");
      }

      const document = rows[0];
      const fileUrl: string = document.url;

      // 2. Extract file key from URL
      const fileKey = fileUrl.replace(`${process.env.DOCUMENTS_BASE_URL}`, "");

      // 3. Delete from S3
      const deletedFromS3 = await deleteFileFromS3(fileKey);

      if (!deletedFromS3) {
        return errorResponse(res, "Failed to delete file from S3");
      }

      // 4. Delete from DB
      await db.query(DOCUMENTS.DELETE_DOCUMENT_BY_ID, [id]);

      return responseData(res, 200, "Document deleted successfully");
    } catch (err) {
      console.error("Error deleting document:", err);
      return errorCatchResponse(
        res,
        "Something went wrong while deleting the document"
      );
    }
  }
);
