import { NextFunction, Request, Response } from "express";
import { response } from "../utils/response";
import db from "../config/database";
import { verifyToken } from "../utils/services/jwt";
import { AUTH } from "../utils/database/queries/auth";

// Extend Express Request to include user object
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = req.cookies?.authToken;

    if (!token) {
      response(res, 401, "Unauthorized access");
      return;
    }

    // Verify token
    const decoded = verifyToken(token) as { id: string };
    if (!decoded || !decoded.id) {
      response(res, 401, "Invalid token");
      return;
    }

    const userId = decoded.id;

    // Fetch user details
    const result = await db.query(
      AUTH.SELECT_PROFILE_WITH_LOGIN_AND_LOGIN_ROLE,
      [userId]
    );
    const user = result.rows[0];

    // console.log("==> ", user);

    if (!user) {
      response(res, 404, "Account not found");
      return;
    }

    // Check user verification and status
    if (!user.isActivated) {
      response(
        res,
        403,
        "Your account has been disabled by the administrator."
      );
      return;
    }

    req.user = user;
    next();
  } catch (error: any) {
    console.log(error);
    if (error.name === "JsonWebTokenError") {
      response(res, 401, "Unauthorized access!");
      return;
    }

    if (error.name === "TokenExpiredError") {
      response(res, 401, "Session expired, please sign in again!");
      return;
    }

    response(res, 500, "Internal server error!");
    return;
  }
};
