CREATE SCHEMA IF NOT EXISTS web;
DROP TABLE IF EXISTS "web"."ads";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "web"."ads" (
    "id" SERIAL PRIMARY KEY,
    "agencyId" int4,
    "profileId" int4,
    "serviceId" int4,
    "eventId" int4,
    "propertyId" int4,
    "projectId" int4,
    "title" text NOT NULL,
    "description" text,
    "typeId" int4 NOT NULL,
    "imgUrl" text,
    "startDate" timestamptz NOT NULL,
    "endDate" timestamptz,
    "locationId" int4,
    "keywords" text,
    "paymentTypeId" int4,
    "currencyTypeId" int4,
    "price" numeric,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_ads_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id")
);

-- Indices
CREATE UNIQUE INDEX idx_16657_pk__ads__3214ec0735355b7e ON web.ads USING btree (id);


DROP TABLE IF EXISTS "web"."screens";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "web"."screens" (
    "id" SERIAL PRIMARY KEY,
    "name" text NOT NULL,
    "title" text NOT NULL,
    "typeId" int4 NOT NULL,
    "groupId" int4,
    "url" text,
    "html" text,
    "languageId" int4 NOT NULL,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdBy" int4 NOT NULL,
    CONSTRAINT "fk_screens_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id")
);

-- Indices
CREATE UNIQUE INDEX idx_16671_pk__screens__3214ec07e3ed324f ON web.screens USING btree (id);


DROP TABLE IF EXISTS "web"."details";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "web"."details" (
    "id" SERIAL PRIMARY KEY,
    "screenId" int4 NOT NULL,
    "details" text,
    "html" text,
    "img" text,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_details_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_details_screen" FOREIGN KEY ("screenId") REFERENCES "web"."screens"("id") ON DELETE CASCADE
);

-- Indices
CREATE UNIQUE INDEX idx_16664_pk__details__3214ec07fd2e4d41 ON web.details USING btree (id);


DROP TABLE IF EXISTS "web"."search";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "web"."search" (
    "id" SERIAL PRIMARY KEY,
    "searchQuery" text NOT NULL,
    "result" text,
    "timestamp" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "createdBy" int4 NOT NULL,
    CONSTRAINT "fk_search_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id")
);

-- Indices
CREATE UNIQUE INDEX idx_16677_pk__search__3214ec07c1b7e84a ON web.search USING btree (id);

DROP TABLE IF EXISTS "web"."uisettings";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "web"."uisettings" (
    "id" SERIAL PRIMARY KEY,
    "screenId" int4 NOT NULL,
    "typeId" int4 NOT NULL,
    "html" text,
    "json" text,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_uisettings_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_uisettings_screen" FOREIGN KEY ("screenId") REFERENCES "web"."screens"("id") ON DELETE CASCADE
);

-- Indices
CREATE UNIQUE INDEX idx_16683_pk__uisettin__3214ec07c446b261 ON web.uisettings USING btree (id);

DROP TABLE IF EXISTS "web"."usersessions";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "web"."usersessions" (
    "id" SERIAL PRIMARY KEY,
    "token" text NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "expiresOn" timestamptz NOT NULL,
    CONSTRAINT "fk_usersessions_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id")
);

-- Indices
CREATE UNIQUE INDEX idx_16689_pk__usersess__3214ec07a488d525 ON web.usersessions USING btree (id);
