import { z } from "zod";

export const createProjectSchema = z.object({
  userId: z.bigint().positive("User ID must be a positive integer"),
  developerId: z.bigint().positive("Developer ID must be a positive integer"),
  title: z.string().min(3, "Title must be at least 3 characters long").max(256),
  shortDescription: z.string().min(10, "Short description must be at least 10 characters long"),
  description: z.string().min(20, "Description must be at least 20 characters long"),
  projectId: z.string().min(3, "Project ID must be at least 3 characters long").max(256),
  location: z.string().min(3, "Location must be at least 3 characters long").max(256),
  usage: z.string().min(3, "Usage must be at least 3 characters long").max(256),
  purpose: z.string().min(3, "Purpose must be at least 3 characters long").max(256),
  type: z.string().min(3, "Type must be at least 3 characters long").max(256),
  registeredDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  completionDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  startPrice: z.number().positive("Start price must be a positive number"),
  paymentPlan: z.string().min(3, "Payment plan must be at least 3 characters long").max(256),
  units: z.number().int().positive("Units must be a positive integer"),
  slug: z.string().min(3, "Slug must be at least 3 characters long").max(256),
  pricing: z.number().positive("Pricing must be a positive number").optional(),
  statusId: z.bigint().positive("Status ID must be a positive integer").nullable(),
  updatedBy: z.bigint().positive("Updated By must be a positive integer").nullable(),
});

export const updateProjectSchema = z.object({
  userId: z.bigint().positive("User ID must be a positive integer").optional(),
  developerId: z.bigint().positive("Developer ID must be a positive integer").optional(),
  title: z.string().min(3).max(256).optional(),
  shortDescription: z.string().min(10).optional(),
  description: z.string().min(20).optional(),
  projectId: z.string().min(3).max(256).optional(),
  location: z.string().min(3).max(256).optional(),
  usage: z.string().min(3).max(256).optional(),
  purpose: z.string().min(3).max(256).optional(),
  type: z.string().min(3).max(256).optional(),
  registeredDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  completionDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  startPrice: z.number().positive().optional(),
  paymentPlan: z.string().min(3).max(256).optional(),
  units: z.number().int().positive().optional(),
  slug: z.string().min(3).max(256).optional(),
  pricing: z.number().positive().optional(),
  statusId: z.bigint().positive().nullable().optional(),
  updatedBy: z.bigint().positive().nullable().optional(),
}).refine((data) => Object.keys(data).length > 0, {
  message: "At least one field must be updated",
});
