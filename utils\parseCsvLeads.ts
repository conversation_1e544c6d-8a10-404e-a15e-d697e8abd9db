import { parse } from "csv-parse/sync";

export interface RawLeadRow {
  Email: string; // required
  Source?: string; // required
  Name?: string;
  Phone?: string;
  LicenseNumber?: string;
  Company?: string;
  Type: "agent" | "agency";
  Status?: string; // defaults to "New"
}

export function parseCsv(src: Buffer | string): RawLeadRow[] {
  return parse(src, {
    columns: true, // first line is the header
    skip_empty_lines: true,
    trim: true,
    relax_column_count: true, // ← allow 6-column or 7-column data rows
  });
}
