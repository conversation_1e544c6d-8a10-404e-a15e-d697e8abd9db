import { Request, Response } from "express";
import { response, responseData } from "../../utils/response";
import asyncHand<PERSON> from "../../middleware/trycatch";
import db from "../../config/database";
import { TYPE } from "../../utils/database/queries/type";

export const getServicesByParentAndGroup = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { parentId, group } = req.params;

      if (!parentId || !group) {
        return response(res, 400, "Missing parentId or group");
      }

      let parentIds: number[] = [];

      // Handle single or array-style parentId

      // Check if the parentId is a stringified array
      if (parentId.startsWith("[") && parentId.endsWith("]")) {
        try {
          const parsedArray = JSON.parse(parentId);
          if (!Array.isArray(parsedArray)) {
            return response(res, 400, "Invalid array format in parentId");
          }

          // Convert all elements to numbers and filter out any NaN values
          parentIds = parsedArray.map(Number).filter((n) => !isNaN(n));
          if (parentIds.length !== parsedArray.length) {
            return response(res, 400, "Array contains non-numeric values");
          }
        } catch (e) {
          return response(res, 400, "Malformed array in parentId");
        }
      } else {
        // Single ID case - convert to number
        const numericId = Number(parentId);
        if (isNaN(numericId)) {
          return response(res, 400, "parentId must be a number");
        }
        parentIds = [numericId];
      }

      // Fetch services for each parentId and group
      const results = await Promise.all(
        parentIds.map((singleId) =>
          db
            .query(TYPE.GET_ALL_SERVICE_BY_PARENT_AND_GROUP, [singleId, group])
            .then((result) => result.rows)
        )
      );

      // Flatten the array of arrays into a single array
      const allResults = results.flat();

      return responseData(res, 200, "Services fetched successfully", allResults);
    } catch (error) {
      console.error("Error in getServicesByParentAndGroup:", error);
      return response(res, 500, "Internal Server Error");
    }
  }
);

export const getSharedParentServices = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const result = await db.query(TYPE.GET_SHARED_PARENT_SERVICES);
      responseData(res, 200, "Associations fetched successfully", result.rows);
    } catch (error) {
      console.log(error);
      return response(res, 500, "Internal Server Error");
    }
  }
);

export const getParentServicesByIds = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Extract and split the comma-separated IDs from the URL
      const ids = req.params.ids.split(',').map(id => id.trim());

      // Validate and convert IDs to numbers      
      const validIds = ids
        .filter(id => !isNaN(Number(id)) && id !== '')
        .map(id => Number(id));

      if (validIds.length === 0) {
        return response(res, 400, "No valid IDs provided");
      }

      // Query the database with the array of IDs
      const result = await db.query({
        text: TYPE.GET_PARENT_SERVICES_BY_IDs,
        values: [validIds], // validIds is now an array of numbers
      });

      if (result.rows.length === 0) {
        return response(res, 404, "No parent services found for the provided IDs");
      }

      responseData(res, 200, "Parent services fetched successfully", result.rows);
    } catch (error) {
      console.log("Error fetching parent services:", error);
      return response(res, 500, "Internal Server Error");
    }
  }
);

// export const getAllServices = asyncHandler(
//   async (req: Request, res: Response) => {
//     try {
//       const result = await db.query(TYPE.GET_ALL_SERVICES);
//       responseData(res, 200, "Associations fetched successfully", result.rows);
//     } catch (error) {
//       console.log(error);
//       return response(res, 500, "Internal Server Error");
//     }
//   }
// );
