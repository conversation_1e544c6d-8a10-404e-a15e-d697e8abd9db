import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('prf.profile', (table) => {
    table.string("customerId").nullable().comment("Stripe Customer ID for the profile");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('prf.profile', (table) => {
    table.dropColumn("customerId");
  });
}
