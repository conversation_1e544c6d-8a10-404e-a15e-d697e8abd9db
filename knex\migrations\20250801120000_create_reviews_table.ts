import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create reviews table
  await knex.schema.withSchema('agn').createTable('reviews', (table) => {
    table.increments('id').primary();
    
    // Foreign Key to user profile who is giving the review
    table.integer('reviewerId').unsigned().notNullable();
    table.foreign('reviewerId').references('id').inTable('prf.profile').onDelete('CASCADE');
    
    // Foreign Key to profile being reviewed (agent or agency)
    table.integer('revieweeId').unsigned().notNullable();
    table.foreign('revieweeId').references('id').inTable('prf.profile').onDelete('CASCADE');
    
    // Review content
    table.text('reviewText').notNullable();
    table.integer('rating').notNullable().checkBetween([1, 5]); // 1-5 stars
    
    // Review status: 1=pending, 2=approved, 3=rejected, 4=hidden
    table.integer('statusId').unsigned().notNullable().defaultTo(1);
    table.foreign('statusId').references('id').inTable('look.status').onDelete('SET NULL');
    
    // Hide reason for when status is hidden (status 4)
    table.text('hideReason').nullable().comment('Reason for hiding the review when status is hidden (status 4)');
    
    // Timestamps
    table.timestamps(true, true);
    table.integer('createdBy').unsigned().notNullable();
    table.foreign('createdBy').references('id').inTable('sec.login').onDelete('CASCADE');
    table.integer('modifiedBy').unsigned().nullable();
    table.foreign('modifiedBy').references('id').inTable('sec.login').onDelete('SET NULL');
    
    // Ensure a user can only review the same profile once
    table.unique(['reviewerId', 'revieweeId']);
  });

  // Create review_notes table
  await knex.schema.withSchema('agn').createTable('review_notes', (table) => {
    table.increments('id').primary();
    table.integer('reviewId').unsigned().notNullable();
    table.foreign('reviewId').references('id').inTable('agn.reviews').onDelete('CASCADE');
    table.text('note').notNullable();
    table.integer('createdBy').unsigned().notNullable();
    table.foreign('createdBy').references('id').inTable('sec.login').onDelete('CASCADE');
    table.timestamp('created_at').defaultTo(knex.fn.now());
  });

  // Create review_history table
  await knex.schema.withSchema('agn').createTable('review_history', (table) => {
    table.increments('id').primary();
    table.integer('reviewId').unsigned().notNullable();
    table.foreign('reviewId').references('id').inTable('agn.reviews').onDelete('CASCADE');
    table.string('action', 50).notNullable();
    table.integer('previousStatus').nullable();
    table.integer('newStatus').nullable();
    table.text('notes').nullable();
    table.integer('createdBy').unsigned().notNullable();
    table.foreign('createdBy').references('id').inTable('sec.login').onDelete('CASCADE');
    table.timestamp('created_at').defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').dropTableIfExists('review_history');
  await knex.schema.withSchema('agn').dropTableIfExists('review_notes');
  await knex.schema.withSchema('agn').dropTable('reviews');
}
