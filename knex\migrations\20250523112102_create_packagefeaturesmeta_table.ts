import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').createTable('packagefeaturesmeta', (table) => {
    table.increments('featureId').primary();
    table.string('featureName', 255).notNullable().unique();
    table.string('featureType', 20).notNullable().checkIn(['TEXT', 'NUMERIC', 'BOOLEAN', 'ENUM']);
    table.string('featureConstant', 255).notNullable();
    table.integer('displayOrder').notNullable().defaultTo(1).unique();
    table.text('description').nullable();
    table.timestamps(true, true); // Automatically adds created_at and updated_at
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('look').dropTableIfExists('packagefeaturesmeta');
}