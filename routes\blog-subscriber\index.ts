import express from "express"; 
import { storageData   } from "../../utils/services/multer";
import { deleteNewsLetterSubscriber, getAllNewsLetterSubscribers, getAllSubscribersCount, sendBulkEmailHandler, updateNewsLetterSubscriber } from "../../controller/admin/blog-subscriber";
const upload = storageData("agentDetails");
const router = express.Router();
 
router.get("/all", getAllNewsLetterSubscribers); 
router.get("/count", getAllSubscribersCount); 
router.put("/update",upload.none(), updateNewsLetterSubscriber);
router.delete("/delete/:id",upload.none(), deleteNewsLetterSubscriber);
router.post("/send",upload.none(), sendBulkEmailHandler);
 
export default router;
