import { Knex } from "knex";

// Import individual seeders
import * as AdminSeeder from "./seed_admin_user";
import * as CountriesSeeder from "./seed_countries";
import * as LanguagesSeeder from "./seed_languages";
import * as RolesSeeder from "./seed_roles";
import * as ServiceSeeder from "./seed_services";
import * as StatusesSeeder from "./seed_statuses";
import * as TypesSeeder from "./seed_types";

// Add more as needed...

export async function seed(knex: Knex): Promise<void> {
  console.log("🔁 Starting Master Seeder...");

  await RolesSeeder.seed(knex);
  console.log("✅ Seeded: Roles Seeder");

  await StatusesSeeder.seed(knex);
  console.log("✅ Seeded: Statuses Seeder");

  await TypesSeeder.seed(knex);
  console.log("✅ Seeded: Types Seeder");
  
  await AdminSeeder.seed(knex);
  console.log("✅ Seeded: Admin User");

  await CountriesSeeder.seed(knex);
  console.log("✅ Seeded: Countries Seeder");

  await LanguagesSeeder.seed(knex);
  console.log("✅ Seeded: Languages Seeder");

  await ServiceSeeder.seed(knex);
  console.log("✅ Seeded: Service and Missions Seeder");

  console.log("🎉 All seeders executed successfully.");
}
