import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.string('employmentProofExpiry').nullable();
        table.string('emiratesIdExpiry').nullable();
        table.string('visaExpiry').nullable();
        table.string('passportExpiry').nullable();

        table.string('personalIdDocExpiry').nullable();
        table.string('passportDocExpiry').nullable();
        table.string('visaDocExpiry').nullable();
        table.string('supportingDocsDocExpiry').nullable();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.dropColumn('employmentProofExpiry');
        table.dropColumn('emiratesIdExpiry');
        table.dropColumn('visaExpiry');
        table.dropColumn('passportExpiry');

        table.dropColumn('personalIdDocExpiry');
        table.dropColumn('passportDocExpiry');
        table.dropColumn('visaDocExpiry');
        table.dropColumn('supportingDocsDocExpiry');
    });
}



