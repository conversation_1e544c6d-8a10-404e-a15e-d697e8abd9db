import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";

export const sendSubscriptionStatusEmail = async (
  fullName: string,
  email: string,
  packageName: string,
  status: 'Cancelled' | 'Deleted',
  res?: Response
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: email,
    subject: `Subscription ${status} - FindAnyAgent`,
    html: await subscriptionStatusEmailTemplate(fullName, status, packageName),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Subscription ${status} email sent to:`, email);
  } catch (error) {
    console.error(`Error sending ${status} email:`, error);
    if (res) {
      res.status(500).json({
        message: `Subscription ${status} successfully, but email failed to send.`,
      });
    }
  }
};

export const subscriptionStatusEmailTemplate = async (
  fullName: string,
  status: 'Cancelled' | 'Deleted',
  packageName: string
): Promise<string> => {
  return `
  <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; background-color: #f9f9f9; color: #333; }
        .container { max-width: 600px; margin: 40px auto; background: #fff; padding: 30px; border-radius: 10px; text-align: center; }
        .title { color: #d9534f; font-size: 24px; margin-bottom: 20px; }
        .content { font-size: 16px; line-height: 1.6; }
        .status { font-weight: bold; color: #d9534f; }
        .footer { margin-top: 30px; font-size: 14px; color: #777; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="title">Subscription ${status}</div>
        <div class="content">
          <p>Hello ${fullName},</p>
          <p>Your subscription to the <strong>${packageName}</strong> package has been <span class="status">${status}</span>.</p>
          <p>If this was a mistake or you need help, please contact support.</p>
        </div>
        <div class="footer">
          <p>Thanks,</p>
          <p><strong>FindAnyAgent Team</strong></p>
        </div>
      </div>
    </body>
  </html>`;
};
