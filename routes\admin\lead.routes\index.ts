import express from "express";
import { storageData } from "../../../utils/services/multer";
import {
  createLead,
  createNote,
  converLeadsAndSendEmails,
  deleteLead,
  getAllLeads,
  getLeadById,
  markLeadAsLost,
  updateLead,
  bulkCreateLeads,
  getNotesOfLead,
  bulkDeleteLeads,
} from "../../../controller/admin/leads";

const router = express.Router();

const upload = storageData("users");

router.get("/", getAllLeads);

router.get("/:id", getLeadById);

router.post("/", upload.none(), createLead);

router.put("/:id", upload.none(), updateLead);

router.delete("/:id", deleteLead);

router.delete("/bulk", bulkDeleteLeads);


router.post("/:id/notes", upload.none(), createNote);

router.put("/:id/mark-lost", upload.none(), markLeadAsLost);

router.post("/convert-leads", converLeadsAndSendEmails);

router.post("/bulk", upload.single("file"), bulkCreateLeads);

router.get("/note/:id", getNotesOfLead);

export default router;
