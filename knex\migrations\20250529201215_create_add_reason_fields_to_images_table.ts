import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.images', function (table) {
        table.text('reason').nullable().comment('Reason for the image, e.g., "profile", "cover", etc.');
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.images', function (table) {
        table.dropColumn('reason');
    });
}