import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.string('gender').nullable().defaultTo(null);
        table.string('personalWebsite').nullable().defaultTo(null);
        table.string('facebook').nullable().defaultTo(null);
        table.string('instagram').nullable().defaultTo(null);
        table.string('linkedin').nullable().defaultTo(null);
        table.string('twitter').nullable().defaultTo(null);
        table.string('youtube').nullable().defaultTo(null);
        table.specificType('personalIdDoc', 'text[]').nullable().defaultTo(null); // Array of text
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('agn.agentdetails', function (table) {
        table.dropColumn('gender');
        table.dropColumn('personalWebsite');
        table.dropColumn('facebook');
        table.dropColumn('instagram');
        table.dropColumn('linkedin');
        table.dropColumn('twitter');
        table.dropColumn('youtube');
        table.dropColumn('personalIdDoc');
    });
}