import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable("agn.properties", (table) => {
    table.boolean("isFeatured").defaultTo(false);
    table.boolean("isVerified").defaultTo(false);
    table.text("adminNote").nullable();
    table.timestamp("expiryDate").nullable();
    table.integer("listingType").nullable(); // 1: Sale, 2: Rent
    table.integer("completionStatus").nullable(); // 1: Ready, 2: Off-Plan
    table.integer("ownershipTypeId").nullable();
    table.text("slug").unique().nullable();
    table.text("metaTitle").nullable();
    table.text("metaDescription").nullable();
    table.integer("bedrooms").nullable();
    table.integer("bathrooms").nullable();
    table.boolean("furnished").defaultTo(false);
    table.string("permitId").nullable();
    table.string("unitNo").nullable();
    table.string("govtIssuedQr").nullable();
    table.string("projectId").nullable();
    table.string("tagLine").nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable("agn.properties", (table) => {
    table.dropColumn("isFeatured");
    table.dropColumn("isVerified");
    table.dropColumn("adminNote");
    table.dropColumn("expiryDate");
    table.dropColumn("listingType");
    table.dropColumn("completionStatus");
    table.dropColumn("ownershipTypeId");
    table.dropColumn("slug");
    table.dropColumn("metaTitle");
    table.dropColumn("metaDescription");
    table.dropColumn("bedrooms");
    table.dropColumn("bathrooms");
    table.dropColumn("furnished");
    table.dropColumn("permitId");
    table.dropColumn("unitNo");
    table.dropColumn("govtIssuedQr");
    table.dropColumn("projectId");
    table.dropColumn("tagLine");
  });
}
