import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("test_webhooks", (table) => {
    table.increments("id").primary();
    table.jsonb("data").notNullable();
    table.timestamps(true, true);
  });

  // Optional: documentation right in the DB
  await knex.schema.alterTable("test_webhooks", (table) => {
    table.comment("Stores raw payloads received from third-party webhooks");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("test_webhooks");
}
