import { Request, Response } from "express";
import { errorCatchResponse, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import db from "../../config/database";
import { NATIONALITIES } from "../../utils/database/queries/nationalities";

export const getAllNationalities = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const query = NATIONALITIES.GET_ALL_COUNTRIES;

      const result = await db.query(query);

      return responseData(
        res,
        200,
        "Locations fetched successfully",
        result.rows
      );
    } catch (error) {
      console.error("Error fetching locations:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getUAECities = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const query = NATIONALITIES.GET_UAE_AND_CITIES;
      const result = await db.query(query);
      return responseData(
        res,
        200,
        "Emirate of Operation fetched successfully",
        result.rows
      );
    } catch (error) {
      console.error("Error fetching locations:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);
