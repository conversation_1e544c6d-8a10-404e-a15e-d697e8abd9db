/agent/properties:
  get:
    tags:
      - Properties
    summary: Get filtered properties
    operationId: getFilteredProperties
    description: Fetch properties using filters like status, type, location, and listing type.
    security:
      - cookieAuth: []
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: pageSize
        in: query
        schema:
          type: integer
          default: 10
      - name: status
        in: query
        schema:
          type: string
      - name: propertyTypeId
        in: query
        schema:
          type: integer
      - name: locationId
        in: query
        schema:
          type: integer
      - name: listingType
        in: query
        schema:
          type: integer
    responses:
      "200":
        description: Properties fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: object
                  properties:
                    properties:
                      type: array
                      items:
                        $ref: "#/components/schemas/Property"
                    pagination:
                      $ref: "#/components/schemas/Pagination"
                    statusCounts:
                      type: array
                      items:
                        $ref: "#/components/schemas/StatusCount"

  post:
    tags:
      - Properties
    summary: Create or update a property
    operationId: createOrUpdateProperty
    security:
      - cookieAuth: []
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              id:
                type: string
              code:
                type: string
              name:
                type: string
              local:
                type: string
              propertyTypeId:
                type: integer
              apartmentTypeId:
                type: integer
              totalRooms:
                type: integer
              locationId:
                type: integer
              address:
                type: string
              currencyId:
                type: integer
              price:
                type: number
              size:
                type: number
              permitNo:
                type: string
              parking:
                type: boolean
              swimmingPools:
                type: boolean
              gym:
                type: boolean
              startDate:
                type: string
                format: date
              expiryDate:
                type: string
                format: date
              listingType:
                type: integer
              completionStatus:
                type: integer
              ownershipTypeId:
                type: integer
              metaTitle:
                type: string
              metaDescription:
                type: string
              bedrooms:
                type: integer
              bathrooms:
                type: integer
              furnished:
                type: boolean
              permitId:
                type: string
              unitNo:
                type: string
              projectId:
                type: integer
              tagLine:
                type: string
              features:
                type: string
              propertyPhotos:
                type: array
                items:
                  type: string
                  format: binary
              govtIssuedQr:
                type: string
                format: binary
    responses:
      "201":
        description: Property created or updated
      "400":
        description: Bad Request

/agent/properties/{id}:
  get:
    tags:
      - Properties
    summary: Get property by ID
    operationId: getPropertyById
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Property found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Property"
      "404":
        description: Property not found

  delete:
    tags:
      - Properties
    summary: Delete a property
    operationId: deleteProperty
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "204":
        description: Deleted successfully
      "404":
        description: Property not found

/agent/properties/{id}/status:
  put:
    tags:
      - Properties
    summary: Update property status
    operationId: updatePropertyStatus
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              status:
                type: string
    responses:
      "200":
        description: Status updated

/agent/properties/{id}/flag:
  put:
    tags:
      - Properties
    summary: Toggle a flag (isFeatured or isVerified)
    operationId: togglePropertyFlag
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              column:
                type: string
                enum: [isFeatured, isVerified]
    responses:
      "200":
        description: Flag toggled

/agent/properties/{id}/photos:
  patch:
    tags:
      - Properties
    summary: Update property photos
    operationId: updatePropertyPhotos
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              photoIdsToDelete:
                type: string
              propertyPhotos:
                type: array
                items:
                  type: string
                  format: binary
    responses:
      "200":
        description: Photos updated

components:
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: authToken
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Property:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        price:
          type: string
        size:
          type: string
        location_id:
          type: integer
        property_type_id:
          type: integer
        listing_type:
          type: integer
        status_id:
          type: integer
        status_name:
          type: string
        is_featured:
          type: boolean
        is_verified:
          type: boolean
        expiry_date:
          type: string
          format: date-time
        slug:
          type: string
        meta_title:
          type: string
        images:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              url:
                type: string

    Pagination:
      type: object
      properties:
        total:
          type: integer
        totalPages:
          type: integer
        currentPage:
          type: integer
        perPage:
          type: integer

    StatusCount:
      type: object
      properties:
        status_id:
          type: integer
        status_name:
          type: string
        count:
          type: integer
