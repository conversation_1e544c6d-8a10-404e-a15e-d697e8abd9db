import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.services', function (table) {
        table.integer('agencyId').nullable().alter();
        table.integer('currencyId').nullable().alter();
        table.integer('locationId').nullable().alter();
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable('prf.services', function (table) {
        table.dropColumn('agencyId');
        table.dropColumn('currencyId');
        table.dropColumn('locationId');
    });
}