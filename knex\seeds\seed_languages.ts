import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
    // Deletes ALL existing entries
    await knex('list.languages').del();

    // Inserts seed entries
    await knex('list.languages').insert([
        { name: 'Afrikaans', code: 'af', localName: 'Afrikaans', statusId: 1 },
        { name: 'Albanian', code: 'sq', localName: 'Shqip', statusId: 1 },
        { name: 'Amharic', code: 'am', localName: 'አማርኛ', statusId: 1 },
        { name: 'Arabic', code: 'ar', localName: 'العربية', statusId: 1 },
        { name: 'Belarusian', code: 'be', localName: 'Беларуская', statusId: 1 },
        { name: 'Bengali', code: 'bn', localName: 'বাংলা', statusId: 1 },
        { name: 'Bulgarian', code: 'bg', localName: 'български език', statusId: 1 },
        { name: 'Chinese', code: 'zh', localName: '中文 (Zhōngw<PERSON>), 汉语, 漢語', statusId: 1 },
        { name: 'Croatian', code: 'hr', localName: 'hrvatski', statusId: 1 },
        { name: 'Czech', code: 'cs', localName: 'česky, čeština', statusId: 1 },
        { name: 'Danish', code: 'da', localName: 'dansk', statusId: 1 },
        { name: 'Dutch', code: 'nl', localName: 'Nederlands, Vlaams', statusId: 1 },
        { name: 'English', code: 'en', localName: 'English', statusId: 1 },
        { name: 'Estonian', code: 'et', localName: 'eesti, eesti keel', statusId: 1 },
        { name: 'Farsi', code: 'fa', localName: 'فارسی', statusId: 1 },
        { name: 'Finnish', code: 'fi', localName: 'suomi, suomen kieli', statusId: 1 },
        { name: 'French', code: 'fr', localName: 'français, langue française', statusId: 1 },
        { name: 'Georgian', code: 'ka', localName: 'ქართული', statusId: 1 },
        { name: 'German', code: 'de', localName: 'Deutsch', statusId: 1 },
        { name: 'Greek', code: 'el', localName: 'Ελληνικά', statusId: 1 },
        { name: 'Hebrew', code: 'he', localName: 'עברית', statusId: 1 },
        { name: 'Hindi', code: 'hi', localName: 'हिन्दी, हिंदी', statusId: 1 },
        { name: 'Hungarian', code: 'hu', localName: 'Magyar', statusId: 1 },
        { name: 'Icelandic', code: 'is', localName: 'Íslenska', statusId: 1 },
        { name: 'Indonesian', code: 'id', localName: 'Bahasa Indonesia', statusId: 1 },
        { name: 'Irish', code: 'ga', localName: 'Gaeilge', statusId: 1 },
        { name: 'Italian', code: 'it', localName: 'Italiano', statusId: 1 },
        { name: 'Japanese', code: 'ja', localName: '日本語 (にほんご／にっぽんご)', statusId: 1 },
        { name: 'Kazakh', code: 'kk', localName: 'Қазақ тілі', statusId: 1 },
        { name: 'Khmer', code: 'km', localName: 'ភាសាខ្មែរ', statusId: 1 },
        { name: 'Korean', code: 'ko', localName: '한국어 (韓國語), 조선말 (朝鮮語)', statusId: 1 },
        { name: 'Kurdish', code: 'ku', localName: 'Kurdî, كوردی', statusId: 1 },
        { name: 'Malay', code: 'ms', localName: 'bahasa Melayu, بهاس ملايو', statusId: 1 },
        { name: 'Maori', code: 'mi', localName: 'te reo Māori', statusId: 1 },
        { name: 'Mongolian', code: 'mn', localName: 'монгол', statusId: 1 },
        { name: 'Nepali', code: 'ne', localName: 'नेपाली', statusId: 1 },
        { name: 'Norwegian', code: 'no', localName: 'Norsk', statusId: 1 },
        { name: 'Pashto', code: 'ps', localName: 'پښتو', statusId: 1 },
        { name: 'Polish', code: 'pl', localName: 'polski', statusId: 1 },
        { name: 'Portuguese', code: 'pt', localName: 'Português', statusId: 1 },
        { name: 'Quechua', code: 'qu', localName: 'Runa Simi, Kichwa', statusId: 1 },
        { name: 'Romanian', code: 'ro', localName: 'română', statusId: 1 },
        { name: 'Romansh', code: 'rm', localName: 'rumantsch grischun', statusId: 1 },
        { name: 'Russian', code: 'ru', localName: 'русский язык', statusId: 1 },
        { name: 'Serbian', code: 'sr', localName: 'српски језик', statusId: 1 },
        { name: 'Sinhala', code: 'si', localName: 'සිංහල', statusId: 1 },
        { name: 'Slovak', code: 'sk', localName: 'slovenčina', statusId: 1 },
        { name: 'Slovenian', code: 'sl', localName: 'slovenščina', statusId: 1 },
        { name: 'Spanish', code: 'es', localName: 'español, castellano', statusId: 1 },
        { name: 'Swedish', code: 'sv', localName: 'svenska', statusId: 1 },
        { name: 'Tagalog', code: 'tl', localName: 'Wikang Tagalog, ᜏᜒᜃᜅ᜔ ᜆᜄᜎᜓᜄ᜔', statusId: 1 },
        { name: 'Tamil', code: 'ta', localName: 'தமிழ்', statusId: 1 },
        { name: 'Thai', code: 'th', localName: 'ไทย', statusId: 1 },
        { name: 'Turkish', code: 'tr', localName: 'Türkçe', statusId: 1 },
        { name: 'Ukrainian', code: 'uk', localName: 'українська', statusId: 1 },
        { name: 'Urdu', code: 'ur', localName: 'اردو', statusId: 1 },
        { name: 'Uzbek', code: 'uz', localName: 'zbek, Ўзбек, أۇزبېك', statusId: 1 },
        { name: 'Vietnamese', code: 'vi', localName: 'Tiếng Việt', statusId: 1 },
        { name: 'Xhosa', code: 'xh', localName: 'isiXhosa', statusId: 1 },
        { name: 'Zulu', code: 'zu', localName: 'Zulu', statusId: 1 },
    ]);
}