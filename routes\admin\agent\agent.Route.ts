import express from "express";
import { storageData, uploadError<PERSON><PERSON><PERSON> } from "../../../utils/services/multer";
import { getAgentAccounts, getAgentAccountsById, getAllVerifiedAgentAccounts, getApplicatinReasons, updateProfileStatusById } from "../../../controller/admin/agent";
import { searchAgentByEmail } from "../../../controller/agents";

const router = express.Router();

const upload = storageData("users");

router.get("/", getAgentAccounts);
router.get("/all/verified", getAllVerifiedAgentAccounts);
router.get("/search", searchAgentByEmail); // GET /api/v1/admin/agents/search?email={email}
router.get("/:id", getAgentAccountsById);

router.put("/:profileId/status", upload.none(), updateProfileStatusById);

router.get("/:profileId/getReasons", getApplicatinReasons);

export default router;
