import nodemailer from "nodemailer";
import { config } from "dotenv";
config();

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number(process.env.SMTP_PORT),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

// transporter.verify(function (error, success) {
//   if (error) {
//     console.error("Transporter configuration is invalid:");
//     console.error(error);
//   } else {
//     console.log("Server is ready to take our messages:", success);
//   }
// });

export default transporter;
