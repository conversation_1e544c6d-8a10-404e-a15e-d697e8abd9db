CREATE SCHEMA IF NOT EXISTS prf;

DROP TABLE IF EXISTS "prf"."profile";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."profile" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "firstName" text NOT NULL,
    "middleName" text,
    "lastName" text NOT NULL,
    "localName" text,
    "emiratesId" text,
    "nationalityId" int4,
    "locationId" int4 NOT NULL,
    "address" text,
    "email" text,
    "phone" text,
    "typeId" int4,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_profile_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_profile_location" FOREIGN KEY ("locationId") REFERENCES "list"."location"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_profile_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_profile_type" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16581_pk__profile__3214ec07bb881681 ON prf.profile USING btree (id);

DROP TABLE IF EXISTS "prf"."accounts";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."accounts" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "name" text NOT NULL,
    "profileId" int4 NOT NULL,
    "accountTypeId" int4,
    "verified" bool NOT NULL,
    "badge" text,
    "packageTypeId" int4,
    "serviceId" int4,
    "licenseTypeId" int4,
    "licenseNo" text,
    "licenseIssueDate" timestamptz,
    "licenseExpiredDate" timestamptz,
    "dldAdm" text,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_account_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_account_licenseType" FOREIGN KEY ("licenseTypeId") REFERENCES "look"."type"("id"),
    CONSTRAINT "fk_account_packagetype" FOREIGN KEY ("packageTypeId") REFERENCES "look"."packagetype"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_account_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_account_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_account_type" FOREIGN KEY ("accountTypeId") REFERENCES "look"."type"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16503_pk__accounts__3214ec070b298675 ON prf.accounts USING btree (id);

DROP TABLE IF EXISTS "prf"."adshistory";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."adshistory" (
    "id" SERIAL PRIMARY KEY,
    "adsId" int4 NOT NULL,
    "profileId" int4 NOT NULL,
    "startDate" timestamptz NOT NULL,
    "expiredDate" timestamptz NOT NULL,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_adshistory_adsId" FOREIGN KEY ("adsId") REFERENCES "web"."ads"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_adshistory_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_adshistory_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_adshistory_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16509_pk__adshistory__3214ec07179ef143 ON prf.adshistory USING btree (id);

DROP TABLE IF EXISTS "prf"."bank";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."bank" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "paymentGateway" text NOT NULL,
    "typeId" int4 NOT NULL,
    "cardName" bytea,
    "cardNumber" bytea,
    "last4Digits" text NOT NULL,
    "expiredDate" date NOT NULL,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_bank_login" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_bank_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_bank_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id"),
    CONSTRAINT "fk_bank_type" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16513_pk__bank__3214ec07124d8734 ON prf.bank USING btree (id);

DROP TABLE IF EXISTS "prf"."chat";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."chat" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_chat_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_chat_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16519_pk__chat__3214ec077e156b25 ON prf.chat USING btree (id);

DROP TABLE IF EXISTS "prf"."chatsessions";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."chatsessions" (
    "id" SERIAL PRIMARY KEY,
    "chatId" int4 NOT NULL,
    "userId" int4 NOT NULL,
    "joinDateTime" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_chatSession_chat" FOREIGN KEY ("chatId") REFERENCES "prf"."chat"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_chatSession_user" FOREIGN KEY ("userId") REFERENCES "sec"."login"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16525_pk__chatsessions__3214ec07757cb78f ON prf.chatsessions USING btree (id);

DROP TABLE IF EXISTS "prf"."contacts";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."contacts" (
    "id" SERIAL PRIMARY KEY,
    "groupName" text NOT NULL,
    "profileId" int4 NOT NULL,
    "serviceId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_contact_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_contact_service" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id") ON DELETE CASCADE
);


-- Indices
CREATE UNIQUE INDEX idx_16529_pk__contacts__3214ec07b751b106 ON prf.contacts USING btree (id);

DROP TABLE IF EXISTS "prf"."email";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."email" (
    "id" SERIAL PRIMARY KEY,
    "userId" int4 NOT NULL,
    "typeId" int4,
    "serviceId" int4 NOT NULL,
    "title" text NOT NULL,
    "description" text,
    "readed" bool NOT NULL DEFAULT false,
    "response" bool NOT NULL DEFAULT false,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_email_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_email_service" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_email_type" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16535_pk__email__3214ec0781cc2bba ON prf.email USING btree (id);

DROP TABLE IF EXISTS "prf"."events";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."events" (
    "id" SERIAL PRIMARY KEY,
    "serviceId" int4 NOT NULL,
    "agencyId" int4 NOT NULL,
    "title" text,
    "description" text,
    "startDate" timestamptz NOT NULL,
    "endDate" timestamptz NOT NULL,
    "startEndTime" timestamptz,
    "locationId" int4 NOT NULL,
    "address" text,
    "json" text,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_createdByEvent" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_locationEvent" FOREIGN KEY ("locationId") REFERENCES "list"."location"("id"),
    CONSTRAINT "fk_serviceEvent" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id"),
    CONSTRAINT "fk_statusEvent" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16543_pk__events__3214ec07cf83ff8c ON prf.events USING btree (id);

DROP TABLE IF EXISTS "prf"."favorites";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."favorites" (
    "id" SERIAL PRIMARY KEY,
    "propertyId" int4,
    "projectId" int4,
    "agencyId" int4,
    "serviceId" int4,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_createdByFavorite" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_statusFavorite" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16549_pk__favorites__3214ec075c274ae6 ON prf.favorites USING btree (id);

DROP TABLE IF EXISTS "prf"."images";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."images" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "serviceId" int4 NOT NULL,
    "typeId" int4 NOT NULL,
    "url" text,
    "title" text,
    "description" text,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_createdByImage" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_profileImage" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_serviceImage" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id"),
    CONSTRAINT "fk_statusImage" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id"),
    CONSTRAINT "fk_typeImage" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16553_pk__images__3214ec07c51b6157 ON prf.images USING btree (id);

DROP TABLE IF EXISTS "prf"."language";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."language" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "languageId" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_languageForeign" FOREIGN KEY ("languageId") REFERENCES "list"."languages"("id"),
    CONSTRAINT "fk_profileLanguage" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16559_pk__language__3214ec076f9d5870 ON prf.language USING btree (id);

DROP TABLE IF EXISTS "prf"."message";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."message" (
    "id" SERIAL PRIMARY KEY,
    "chatId" int4 NOT NULL,
    "userId" int4 NOT NULL,
    "message" text NOT NULL,
    "url" text,
    "mediaTypeId" int4,
    "timestamp" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_message_chat" FOREIGN KEY ("chatId") REFERENCES "prf"."chat"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_message_mediaType" FOREIGN KEY ("mediaTypeId") REFERENCES "look"."type"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_message_user" FOREIGN KEY ("userId") REFERENCES "sec"."login"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16563_pk__message__3214ec079e277663 ON prf.message USING btree (id);

DROP TABLE IF EXISTS "prf"."passportvisa";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."passportvisa" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "passportNo" text,
    "passportIssueCountryId" int4 NOT NULL,
    "passportIssueDate" timestamptz,
    "passportExpireDate" timestamptz,
    "visaNo" text,
    "issueDate" timestamptz,
    "expireDate" timestamptz,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_createdByPassportVisa" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_passportCountry" FOREIGN KEY ("passportIssueCountryId") REFERENCES "list"."location"("id"),
    CONSTRAINT "fk_profilePassportVisa" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_statusPassportVisa" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16569_pk__passportvisa__3214ec07ca391d89 ON prf.passportvisa USING btree (id);

DROP TABLE IF EXISTS "prf"."portfolio";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."portfolio" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "serviceId" int4 NOT NULL,
    "agencyId" int4 NOT NULL,
    "title" text,
    "descriptions" text,
    "json" text,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_createdByPortfolio" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_profilePortfolio" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_servicePortfolio" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id"),
    CONSTRAINT "fk_statusPortfolio" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16575_pk__portfolio__3214ec075c855d3d ON prf.portfolio USING btree (id);

DROP TABLE IF EXISTS "prf"."purchase";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."purchase" (
    "id" SERIAL PRIMARY KEY,
    "accountId" int4 NOT NULL,
    "serviceId" int4,
    "amount" numeric NOT NULL,
    "date" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_purchase_account" FOREIGN KEY ("accountId") REFERENCES "prf"."accounts"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_purchase_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_purchase_service" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_purchase_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16587_pk__purchase__3214ec0786b2a67e ON prf.purchase USING btree (id);

DROP TABLE IF EXISTS "prf"."review";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."review" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "rate" numeric NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_createdByReview" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_profileReview" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16594_pk__review__3214ec07b071bc7f ON prf.review USING btree (id);

DROP TABLE IF EXISTS "prf"."services";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."services" (
    "id" SERIAL PRIMARY KEY,
    "profileId" int4 NOT NULL,
    "agencyId" int4 NOT NULL,
    "serviceId" int4 NOT NULL,
    "currencyId" int4 NOT NULL,
    "price" numeric,
    "locationId" int4 NOT NULL,
    "address" text,
    "json" text,
    "statusId" int4 NOT NULL,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedBy" int4,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_currency" FOREIGN KEY ("currencyId") REFERENCES "list"."currency"("id"),
    CONSTRAINT "fk_location" FOREIGN KEY ("locationId") REFERENCES "list"."location"("id"),
    CONSTRAINT "fk_profile" FOREIGN KEY ("profileId") REFERENCES "prf"."profile"("id"),
    CONSTRAINT "fk_service" FOREIGN KEY ("serviceId") REFERENCES "list"."services"("id"),
    CONSTRAINT "fk_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16600_pk__services__3214ec070725b53a ON prf.services USING btree (id);

DROP TABLE IF EXISTS "prf"."socialmedia";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."socialmedia" (
    "id" SERIAL PRIMARY KEY,
    "agencyId" int4,
    "profileId" int4,
    "typeId" int4 NOT NULL,
    "url" text NOT NULL,
    "statusId" int4 NOT NULL DEFAULT 1,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "modifiedOn" timestamptz,
    CONSTRAINT "fk_socialmedia_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id"),
    CONSTRAINT "fk_socialmedia_type" FOREIGN KEY ("typeId") REFERENCES "look"."type"("id")
);


-- Indices
CREATE UNIQUE INDEX idx_16606_pk__socialmedia__3214ec07fde11885 ON prf.socialmedia USING btree (id);

DROP TABLE IF EXISTS "prf"."transactions";
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "prf"."transactions" (
    "id" SERIAL PRIMARY KEY,
    "code" text NOT NULL,
    "accountId" int4 NOT NULL,
    "purchaseId" int4,
    "bankId" int4,
    "currencyId" int4 NOT NULL,
    "amount" numeric NOT NULL,
    "subscriptionId" int4,
    "token" text,
    "transactionTypeId" int4,
    "timestamp" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "statusId" int4,
    "createdBy" int4 NOT NULL,
    "createdOn" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_transaction_account" FOREIGN KEY ("accountId") REFERENCES "prf"."accounts"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_transaction_bank" FOREIGN KEY ("bankId") REFERENCES "prf"."bank"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_transaction_createdBy" FOREIGN KEY ("createdBy") REFERENCES "sec"."login"("id"),
    CONSTRAINT "fk_transaction_currency" FOREIGN KEY ("currencyId") REFERENCES "list"."currency"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_transaction_purchase" FOREIGN KEY ("purchaseId") REFERENCES "prf"."purchase"("id"),
    CONSTRAINT "fk_transaction_status" FOREIGN KEY ("statusId") REFERENCES "look"."status"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_transaction_subscription" FOREIGN KEY ("subscriptionId") REFERENCES "list"."subscription"("id") ON DELETE SET NULL,
    CONSTRAINT "fk_transaction_type" FOREIGN KEY ("transactionTypeId") REFERENCES "look"."type"("id") ON DELETE SET NULL
);


-- Indices
CREATE UNIQUE INDEX idx_16613_pk__transactions__3214ec0754052a38 ON prf.transactions USING btree (id);