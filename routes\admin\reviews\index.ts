import express from "express";
import {
  validateGetReviewsQuery,
  validateUpdateReviewStatus,
  validateBulkUpdateReviews,
  validateAddReviewNote,
  validateFlagReview,
  validateHideReview,
  validateRestoreReview,
} from "../../../middleware/reviewsValidation";
import {
  getAllReviews,
  getReviewById,
  updateReviewStatus,
  deleteReview,
  getReviewsStats,
  bulkUpdateReviewsStatus,
  addReviewNote,
  getReviewNotes,
  getReviewHistory,
  flagReview,
  hideReview,
  restoreReview,
} from "../../../controller/admin/reviews";
import { storageData } from "../../../utils/services/multer";

const router = express.Router();
const upload = storageData("reviews");

// Get all reviews with pagination and filtering
router.get("/", validateGetReviewsQuery, getAllReviews);

// Get reviews statistics for dashboard
router.get("/stats", getReviewsStats);

// Get single review by ID
router.get("/:id", getReviewById);

// Update review status (approve/reject)
router.patch("/:id/status", upload.none(),  updateReviewStatus);

// Delete review (soft delete)
router.delete("/:id", deleteReview);

// Bulk update reviews status
router.patch("/bulk/status", upload.none(), validateBulkUpdateReviews, bulkUpdateReviewsStatus);

// Add note to review
router.post("/:id/notes", upload.none(), validateAddReviewNote, addReviewNote);

// Get review notes
router.get("/:id/notes", getReviewNotes);

// Get review history
router.get("/:id/history", getReviewHistory);

// Flag review for moderation
router.patch("/:id/flag", upload.none(), validateFlagReview, flagReview);

// Hide review
router.patch("/:id/hide", upload.none(), validateHideReview, hideReview);

// Restore hidden review
router.patch("/:id/restore", upload.none(), validateRestoreReview, restoreReview);

export default router;
