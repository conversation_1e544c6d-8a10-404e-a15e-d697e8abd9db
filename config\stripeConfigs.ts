import dot from "dotenv";

dot.config();

// File: config/plansConfigs.ts
interface StripeKeys {
  stripe: {
    publicKey: string;
    secretKey: string;
  };
}

const STRIPE_PUBLIC_KEY = process.env.STRIPE_PUBLIC_KEY!;
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY!;

const stripeKeys: StripeKeys = {
  stripe: {
    publicKey: STRIPE_PUBLIC_KEY!,
    secretKey: STRIPE_SECRET_KEY!,
  },
};

export default stripeKeys;
